import { ApiResponse } from "./types";

// Get API base URL from environment or default to localhost
const getApiBaseUrl = (): string => {
  if (typeof window !== "undefined") {
    // Client-side: use current origin or environment variable
    return (
      process.env.NEXT_PUBLIC_API_URL ||
      `${window.location.protocol}//${window.location.hostname}:3011`
    );
  }
  // Server-side: use environment variable or default
  return process.env.NEXT_PUBLIC_API_URL || "http://localhost:3011";
};

const API_BASE_URL = getApiBaseUrl();

/**
 * Brand interface matching the backend Brand type
 */
export interface Brand {
  _id: string;
  name: string;
  description: string;
  slug: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO for creating a new brand
 */
export interface CreateBrandDto {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * DTO for updating a brand
 */
export interface UpdateBrandDto {
  name?: string;
  description?: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * Brand filters for querying
 */
export interface BrandFilters {
  isActive?: boolean;
  search?: string;
}

/**
 * API service for brand management
 */
export class BrandApi {
  private static readonly BASE_URL = `${API_BASE_URL}/api/brands`;

  /**
   * Get all brands with optional filtering
   */
  static async getBrands(
    filters?: BrandFilters
  ): Promise<ApiResponse<Brand[]>> {
    try {
      const params = new URLSearchParams();

      if (filters?.isActive !== undefined) {
        params.append("isActive", filters.isActive.toString());
      }

      if (filters?.search) {
        params.append("search", filters.search);
      }

      const queryString = params.toString();
      const url = queryString
        ? `${this.BASE_URL}?${queryString}`
        : this.BASE_URL;

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching brands:", error);
      throw error;
    }
  }

  /**
   * Get a brand by ID
   */
  static async getBrandById(id: string): Promise<ApiResponse<Brand>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching brand:", error);
      throw error;
    }
  }

  /**
   * Get a brand by slug
   */
  static async getBrandBySlug(slug: string): Promise<ApiResponse<Brand>> {
    try {
      const response = await fetch(`${this.BASE_URL}/slug/${slug}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching brand by slug:", error);
      throw error;
    }
  }

  /**
   * Create a new brand
   */
  static async createBrand(
    brandData: CreateBrandDto
  ): Promise<ApiResponse<Brand>> {
    try {
      const response = await fetch(this.BASE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(brandData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error creating brand:", error);
      throw error;
    }
  }

  /**
   * Update a brand
   */
  static async updateBrand(
    id: string,
    updateData: UpdateBrandDto
  ): Promise<ApiResponse<Brand>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error updating brand:", error);
      throw error;
    }
  }

  /**
   * Delete a brand
   */
  static async deleteBrand(id: string): Promise<ApiResponse<null>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error deleting brand:", error);
      throw error;
    }
  }

  /**
   * Recalculate product counts for all brands
   */
  static async recalculateProductCounts(): Promise<ApiResponse<null>> {
    try {
      const response = await fetch(`${this.BASE_URL}/recalculate-counts`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error recalculating brand product counts:", error);
      throw error;
    }
  }
}
