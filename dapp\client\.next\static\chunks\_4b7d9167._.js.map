{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/common/PageHeaderWrapper.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ntype PageHeaderWrapperProps = {\r\n  title: string;\r\n  description: string;\r\n  children?: React.ReactNode;\r\n};\r\n\r\n/**\r\n * A standardized page header component with title and description\r\n * Used across admin pages for consistent UI\r\n */\r\nexport const PageHeaderWrapper = ({\r\n  title,\r\n  description,\r\n  children,\r\n}: PageHeaderWrapperProps) => {\r\n  return (\r\n    <div className=\"container mx-auto py-6\">\r\n      <div className=\"flex flex-col items-start justify-between gap-4 border-b pb-5 sm:flex-row sm:items-center sm:gap-0\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold tracking-tight\">{title}</h1>\r\n          <p className=\"text-muted-foreground\">{description}</p>\r\n        </div>\r\n        {children && <div>{children}</div>}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAYO,MAAM,oBAAoB,CAAC,EAChC,KAAK,EACL,WAAW,EACX,QAAQ,EACe;IACvB,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC;;sCACC,4TAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,4TAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;gBAEvC,0BAAY,4TAAC;8BAAK;;;;;;;;;;;;;;;;;AAI3B;KAhBa", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductListActions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { FileDown, Plus, RefreshCw, Upload } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface ProductListActionsProps {\r\n  onRefresh?: () => void;\r\n  loading?: boolean;\r\n}\r\n\r\nexport const ProductListActions = ({\r\n  onRefresh,\r\n  loading = false,\r\n}: ProductListActionsProps) => {\r\n  const router = useRouter();\r\n\r\n  const handleExport = () => console.log(\"Exporting...\");\r\n  const handleImport = () => console.log(\"Importing...\");\r\n  const handleRefresh = () => onRefresh?.();\r\n  const handleCreate = () => router.push(\"/admin/products/add\");\r\n\r\n  return (\r\n    <div className=\"flex gap-2\">\r\n      <Button\r\n        variant=\"outline\"\r\n        size=\"sm\"\r\n        onClick={handleRefresh}\r\n        disabled={loading}\r\n      >\r\n        <RefreshCw\r\n          className={`mr-2 h-4 w-4 ${loading ? \"animate-spin\" : \"\"}`}\r\n        />\r\n        Refresh\r\n      </Button>\r\n\r\n      <Button variant=\"outline\" size=\"sm\" onClick={handleImport}>\r\n        <Upload className=\"mr-2 h-4 w-4\" />\r\n        Import\r\n      </Button>\r\n\r\n      <Button variant=\"outline\" size=\"sm\" onClick={handleExport}>\r\n        <FileDown className=\"mr-2 h-4 w-4\" />\r\n        Export\r\n      </Button>\r\n\r\n      <Button size=\"sm\" onClick={handleCreate}>\r\n        <Plus className=\"mr-2 h-4 w-4\" />\r\n        Add Product\r\n      </Button>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AACA;AAEA;;;AAPA;;;;AAcO,MAAM,qBAAqB,CAAC,EACjC,SAAS,EACT,UAAU,KAAK,EACS;;IACxB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,IAAM,QAAQ,GAAG,CAAC;IACvC,MAAM,eAAe,IAAM,QAAQ,GAAG,CAAC;IACvC,MAAM,gBAAgB,IAAM;IAC5B,MAAM,eAAe,IAAM,OAAO,IAAI,CAAC;IAEvC,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,8HAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,UAAU;;kCAEV,4TAAC,uSAAA,CAAA,YAAS;wBACR,WAAW,CAAC,aAAa,EAAE,UAAU,iBAAiB,IAAI;;;;;;oBAC1D;;;;;;;0BAIJ,4TAAC,8HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;gBAAK,SAAS;;kCAC3C,4TAAC,6RAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIrC,4TAAC,8HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;gBAAK,SAAS;;kCAC3C,4TAAC,qSAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIvC,4TAAC,8HAAA,CAAA,SAAM;gBAAC,MAAK;gBAAK,SAAS;;kCACzB,4TAAC,yRAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAKzC;GAzCa;;QAII,oQAAA,CAAA,YAAS;;;KAJb", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,4TAAC,+QAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,+QAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/products.ts"], "sourcesContent": ["/**\r\n * API functions for product operations\r\n * Handles all HTTP requests to the backend product endpoints\r\n */\r\n\r\n// Types for API responses\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  meta?: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    pages: number;\r\n  };\r\n}\r\n\r\nexport interface ProductFilters {\r\n  search?: string;\r\n  category?: string;\r\n  brand?: string;\r\n  minPrice?: number;\r\n  maxPrice?: number;\r\n  condition?: string;\r\n  inStock?: boolean;\r\n  tags?: string[];\r\n  status?: string;\r\n  sortBy?: \"price\" | \"createdAt\" | \"name\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\n// Get API base URL from environment or default to localhost\r\nconst getApiBaseUrl = (): string => {\r\n  if (typeof window !== \"undefined\") {\r\n    // Client-side: use current origin or environment variable\r\n    return (\r\n      process.env.NEXT_PUBLIC_API_URL ||\r\n      `${window.location.protocol}//${window.location.hostname}:3001`\r\n    );\r\n  }\r\n  // Server-side: use environment variable or default\r\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\";\r\n};\r\n\r\nconst API_BASE_URL = getApiBaseUrl();\r\n\r\n/**\r\n * Generic fetch wrapper with error handling\r\n */\r\nasync function apiRequest<T>(\r\n  endpoint: string,\r\n  options: RequestInit = {}\r\n): Promise<ApiResponse<T>> {\r\n  const url = `${API_BASE_URL}/api${endpoint}`;\r\n\r\n  const defaultOptions: RequestInit = {\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      ...options.headers,\r\n    },\r\n    ...options,\r\n  };\r\n\r\n  console.log(`🌐 Making API request to: ${url}`);\r\n  console.log(`🌐 Request options:`, defaultOptions);\r\n\r\n  try {\r\n    const response = await fetch(url, defaultOptions);\r\n\r\n    console.log(`🌐 Response status: ${response.status}`);\r\n    console.log(`🌐 Response ok: ${response.ok}`);\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      console.error(`🌐 Response error text:`, errorText);\r\n      throw new Error(\r\n        `HTTP error! status: ${response.status}, message: ${errorText}`\r\n      );\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(`🌐 Response data:`, data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(`🌐 API request failed for ${endpoint}:`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Get all products with optional filtering\r\n */\r\nexport async function getProducts(\r\n  filters: ProductFilters = {}\r\n): Promise<ApiResponse<any[]>> {\r\n  const searchParams = new URLSearchParams();\r\n\r\n  // Add filters to search params\r\n  Object.entries(filters).forEach(([key, value]) => {\r\n    if (value !== undefined && value !== null && value !== \"\") {\r\n      if (Array.isArray(value)) {\r\n        value.forEach((item) => searchParams.append(key, item.toString()));\r\n      } else {\r\n        searchParams.append(key, value.toString());\r\n      }\r\n    }\r\n  });\r\n\r\n  const queryString = searchParams.toString();\r\n  const endpoint = queryString ? `/products?${queryString}` : \"/products\";\r\n\r\n  return apiRequest<any[]>(endpoint);\r\n}\r\n\r\n/**\r\n * Get a single product by ID\r\n */\r\nexport async function getProductById(id: string): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}`);\r\n}\r\n\r\n/**\r\n * Get a product for editing (with safe field list)\r\n */\r\nexport async function getProductForEdit(id: string): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}/edit`);\r\n}\r\n\r\n/**\r\n * Create a new product\r\n */\r\nexport async function createProduct(\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  console.log(\"🌐 API createProduct called with data:\", productData);\r\n  console.log(\"🌐 API Base URL:\", API_BASE_URL);\r\n\r\n  try {\r\n    const result = await apiRequest<any>(\"/products\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(productData),\r\n    });\r\n    console.log(\"🌐 API createProduct response:\", result);\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"🌐 API createProduct error:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Update an existing product\r\n */\r\nexport async function updateProduct(\r\n  id: string,\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}`, {\r\n    method: \"PUT\",\r\n    body: JSON.stringify(productData),\r\n  });\r\n}\r\n\r\n/**\r\n * Update product with safe fields (PATCH)\r\n */\r\nexport async function updateProductSafe(\r\n  id: string,\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}/edit`, {\r\n    method: \"PATCH\",\r\n    body: JSON.stringify(productData),\r\n  });\r\n}\r\n\r\n/**\r\n * Delete a product\r\n */\r\nexport async function deleteProduct(id: string): Promise<ApiResponse<boolean>> {\r\n  return apiRequest<boolean>(`/products/${id}`, {\r\n    method: \"DELETE\",\r\n  });\r\n}\r\n\r\n/**\r\n * Health check for API connection\r\n */\r\nexport async function checkApiHealth(): Promise<boolean> {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/api`);\r\n    return response.ok;\r\n  } catch (error) {\r\n    console.error(\"API health check failed:\", error);\r\n    return false;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0BAA0B;;;;;;;;;;;AAkCpB;AALN,4DAA4D;AAC5D,MAAM,gBAAgB;IACpB,wCAAmC;QACjC,0DAA0D;QAC1D,OACE,6DACA,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;IAEnE;;AAGF;AAEA,MAAM,eAAe;AAErB;;CAEC,GACD,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,UAAU;IAE5C,MAAM,iBAA8B;QAClC,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,KAAK;IAC9C,QAAQ,GAAG,CAAC,CAAC,mBAAmB,CAAC,EAAE;IAEnC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QACpD,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;QAE5C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,uBAAuB,CAAC,EAAE;YACzC,MAAM,IAAI,MACR,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW;QAEnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;QACjC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC,EAAE;QACxD,MAAM;IACR;AACF;AAKO,eAAe,YACpB,UAA0B,CAAC,CAAC;IAE5B,MAAM,eAAe,IAAI;IAEzB,+BAA+B;IAC/B,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAC,OAAS,aAAa,MAAM,CAAC,KAAK,KAAK,QAAQ;YAChE,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;YACzC;QACF;IACF;IAEA,MAAM,cAAc,aAAa,QAAQ;IACzC,MAAM,WAAW,cAAc,CAAC,UAAU,EAAE,aAAa,GAAG;IAE5D,OAAO,WAAkB;AAC3B;AAKO,eAAe,eAAe,EAAU;IAC7C,OAAO,WAAgB,CAAC,UAAU,EAAE,IAAI;AAC1C;AAKO,eAAe,kBAAkB,EAAU;IAChD,OAAO,WAAgB,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC;AAC/C;AAKO,eAAe,cACpB,WAAgB;IAEhB,QAAQ,GAAG,CAAC,0CAA0C;IACtD,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,IAAI;QACF,MAAM,SAAS,MAAM,WAAgB,aAAa;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAKO,eAAe,cACpB,EAAU,EACV,WAAgB;IAEhB,OAAO,WAAgB,CAAC,UAAU,EAAE,IAAI,EAAE;QACxC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe,kBACpB,EAAU,EACV,WAAgB;IAEhB,OAAO,WAAgB,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC,EAAE;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe,cAAc,EAAU;IAC5C,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI,EAAE;QAC5C,QAAQ;IACV;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,IAAI,CAAC;QAClD,OAAO,SAAS,EAAE;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useProducts.ts"], "sourcesContent": ["/**\r\n * React hooks for product data management\r\n * Provides easy-to-use hooks for CRUD operations on products\r\n */\r\n\r\nimport { useCallback, useEffect, useState } from 'react';\r\nimport { toast } from 'sonner';\r\n\r\nimport {\r\n  createProduct,\r\n  deleteProduct,\r\n  getProductById,\r\n  getProductForEdit,\r\n  getProducts,\r\n  updateProduct,\r\n  updateProductSafe,\r\n  type ApiResponse,\r\n  type ProductFilters,\r\n} from '@/lib/api/products';\r\n\r\n// Hook state types\r\ninterface UseProductsState {\r\n  products: any[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  meta?: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    pages: number;\r\n  };\r\n}\r\n\r\ninterface UseProductState {\r\n  product: any | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\n/**\r\n * Hook for fetching and managing multiple products\r\n */\r\nexport function useProducts(initialFilters: ProductFilters = {}) {\r\n  const [state, setState] = useState<UseProductsState>({\r\n    products: [],\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  const [filters, setFilters] = useState<ProductFilters>(initialFilters);\r\n\r\n  const fetchProducts = useCallback(async (newFilters?: ProductFilters) => {\r\n    setState(prev => ({ ...prev, loading: true, error: null }));\r\n    \r\n    try {\r\n      const filtersToUse = newFilters || filters;\r\n      const response = await getProducts(filtersToUse);\r\n      \r\n      setState({\r\n        products: response.data,\r\n        loading: false,\r\n        error: null,\r\n        meta: response.meta,\r\n      });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch products';\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error('Failed to load products');\r\n    }\r\n  }, [filters]);\r\n\r\n  const updateFilters = useCallback((newFilters: ProductFilters) => {\r\n    setFilters(newFilters);\r\n    fetchProducts(newFilters);\r\n  }, [fetchProducts]);\r\n\r\n  const refreshProducts = useCallback(() => {\r\n    fetchProducts();\r\n  }, [fetchProducts]);\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchProducts();\r\n  }, [fetchProducts]);\r\n\r\n  return {\r\n    ...state,\r\n    filters,\r\n    updateFilters,\r\n    refreshProducts,\r\n    refetch: fetchProducts,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for fetching and managing a single product\r\n */\r\nexport function useProduct(id: string | null) {\r\n  const [state, setState] = useState<UseProductState>({\r\n    product: null,\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  const fetchProduct = useCallback(async () => {\r\n    if (!id) {\r\n      setState({ product: null, loading: false, error: null });\r\n      return;\r\n    }\r\n\r\n    setState(prev => ({ ...prev, loading: true, error: null }));\r\n    \r\n    try {\r\n      const response = await getProductById(id);\r\n      setState({\r\n        product: response.data,\r\n        loading: false,\r\n        error: null,\r\n      });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product';\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error('Failed to load product');\r\n    }\r\n  }, [id]);\r\n\r\n  useEffect(() => {\r\n    fetchProduct();\r\n  }, [fetchProduct]);\r\n\r\n  return {\r\n    ...state,\r\n    refetch: fetchProduct,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for product CRUD operations\r\n */\r\nexport function useProductMutations() {\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const createProductMutation = useCallback(async (productData: any) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await createProduct(productData);\r\n      toast.success('Product created successfully');\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateProductMutation = useCallback(async (id: string, productData: any) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await updateProduct(id, productData);\r\n      toast.success('Product updated successfully');\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateProductSafeMutation = useCallback(async (id: string, productData: any) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await updateProductSafe(id, productData);\r\n      toast.success('Product updated successfully');\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const deleteProductMutation = useCallback(async (id: string) => {\r\n    setLoading(true);\r\n    try {\r\n      await deleteProduct(id);\r\n      toast.success('Product deleted successfully');\r\n      return true;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  return {\r\n    loading,\r\n    createProduct: createProductMutation,\r\n    updateProduct: updateProductMutation,\r\n    updateProductSafe: updateProductSafeMutation,\r\n    deleteProduct: deleteProductMutation,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for fetching product for editing\r\n */\r\nexport function useProductForEdit(id: string | null) {\r\n  const [state, setState] = useState<UseProductState & { editableFields?: string[] }>({\r\n    product: null,\r\n    loading: true,\r\n    error: null,\r\n    editableFields: [],\r\n  });\r\n\r\n  const fetchProductForEdit = useCallback(async () => {\r\n    if (!id) {\r\n      setState({ product: null, loading: false, error: null, editableFields: [] });\r\n      return;\r\n    }\r\n\r\n    setState(prev => ({ ...prev, loading: true, error: null }));\r\n    \r\n    try {\r\n      const response = await getProductForEdit(id);\r\n      setState({\r\n        product: response.data,\r\n        loading: false,\r\n        error: null,\r\n        editableFields: response.editableFields || [],\r\n      });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product for editing';\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error('Failed to load product for editing');\r\n    }\r\n  }, [id]);\r\n\r\n  useEffect(() => {\r\n    fetchProductForEdit();\r\n  }, [fetchProductForEdit]);\r\n\r\n  return {\r\n    ...state,\r\n    refetch: fetchProductForEdit,\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AACA;AAEA;;;;;AAkCO,SAAS,YAAY,iBAAiC,CAAC,CAAC;;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,UAAU,EAAE;QACZ,SAAS;QACT,OAAO;IACT;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvD,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kDAAE,OAAO;YACvC;0DAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK,CAAC;;YAEzD,IAAI;gBACF,MAAM,eAAe,cAAc;gBACnC,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE;gBAEnC,SAAS;oBACP,UAAU,SAAS,IAAI;oBACvB,SAAS;oBACT,OAAO;oBACP,MAAM,SAAS,IAAI;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D;8DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO;wBACT,CAAC;;gBACD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;iDAAG;QAAC;KAAQ;IAEZ,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACjC,WAAW;YACX,cAAc;QAChB;iDAAG;QAAC;KAAc;IAElB,MAAM,kBAAkB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;oDAAE;YAClC;QACF;mDAAG;QAAC;KAAc;IAElB,gBAAgB;IAChB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;KAAc;IAElB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA,SAAS;IACX;AACF;GAtDgB;AA2DT,SAAS,WAAW,EAAiB;;IAC1C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,SAAS;QACT,SAAS;QACT,OAAO;IACT;IAEA,MAAM,eAAe,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;gDAAE;YAC/B,IAAI,CAAC,IAAI;gBACP,SAAS;oBAAE,SAAS;oBAAM,SAAS;oBAAO,OAAO;gBAAK;gBACtD;YACF;YAEA;wDAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK,CAAC;;YAEzD,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE;gBACtC,SAAS;oBACP,SAAS,SAAS,IAAI;oBACtB,SAAS;oBACT,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D;4DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO;wBACT,CAAC;;gBACD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;+CAAG;QAAC;KAAG;IAEP,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC;KAAa;IAEjB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;IAzCgB;AA8CT,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,wBAAwB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kEAAE,OAAO;YAC/C,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE;gBACrC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;iEAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kEAAE,OAAO,IAAY;YAC3D,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;gBACzC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;iEAAG,EAAE;IAEL,MAAM,4BAA4B,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;sEAAE,OAAO,IAAY;YAC/D,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI;gBAC7C,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;qEAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kEAAE,OAAO;YAC/C,WAAW;YACX,IAAI;gBACF,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE;gBACpB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;iEAAG,EAAE;IAEL,OAAO;QACL;QACA,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,eAAe;IACjB;AACF;IAtEgB;AA2ET,SAAS,kBAAkB,EAAiB;;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAmD;QAClF,SAAS;QACT,SAAS;QACT,OAAO;QACP,gBAAgB,EAAE;IACpB;IAEA,MAAM,sBAAsB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;8DAAE;YACtC,IAAI,CAAC,IAAI;gBACP,SAAS;oBAAE,SAAS;oBAAM,SAAS;oBAAO,OAAO;oBAAM,gBAAgB,EAAE;gBAAC;gBAC1E;YACF;YAEA;sEAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK,CAAC;;YAEzD,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE;gBACzC,SAAS;oBACP,SAAS,SAAS,IAAI;oBACtB,SAAS;oBACT,OAAO;oBACP,gBAAgB,SAAS,cAAc,IAAI,EAAE;gBAC/C;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D;0EAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO;wBACT,CAAC;;gBACD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;6DAAG;QAAC;KAAG;IAEP,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAoB;IAExB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;IA3CgB", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,4TAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kRAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,4TAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,uSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,kRAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,2SAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,kRAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,4TAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,4TAAC,kRAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,4TAAC;;;;;8BACD,4TAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,4TAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,4TAAC,2RAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,4TAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kRAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,kRAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/constants/categories.ts"], "sourcesContent": ["// constants/filters.ts\r\nexport const categories = [\r\n  { label: \"All\", value: \"all\" },\r\n  { label: \"Electronics\", value: \"electronics\" },\r\n  { label: \"Furniture\", value: \"furniture\" },\r\n  { label: \"Clothing\", value: \"clothing\" },\r\n];\r\n\r\nexport const sortOptions = [\r\n  { label: \"Last added\", value: \"latest\" },\r\n  { label: \"Price: Low to High\", value: \"price-asc\" },\r\n  { label: \"Price: High to Low\", value: \"price-desc\" },\r\n];\r\n\r\nexport const availabilityOptions = [\r\n  { label: \"All\", value: \"all\" },\r\n  { label: \"In Stock\", value: \"in-stock\" },\r\n  { label: \"Out of Stock\", value: \"out-of-stock\" },\r\n];\r\n\r\nexport const brandOptions = [\r\n  { label: \"All\", value: \"all\" },\r\n  { label: \"Apple\", value: \"apple\" },\r\n  { label: \"Samsung\", value: \"samsung\" },\r\n  { label: \"Sony\", value: \"sony\" },\r\n  { label: \"Microsoft\", value: \"microsoft\" },\r\n];\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;;;AAChB,MAAM,aAAa;IACxB;QAAE,OAAO;QAAO,OAAO;IAAM;IAC7B;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;CACxC;AAEM,MAAM,cAAc;IACzB;QAAE,OAAO;QAAc,OAAO;IAAS;IACvC;QAAE,OAAO;QAAsB,OAAO;IAAY;IAClD;QAAE,OAAO;QAAsB,OAAO;IAAa;CACpD;AAEM,MAAM,sBAAsB;IACjC;QAAE,OAAO;QAAO,OAAO;IAAM;IAC7B;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAgB,OAAO;IAAe;CAChD;AAEM,MAAM,eAAe;IAC1B;QAAE,OAAO;QAAO,OAAO;IAAM;IAC7B;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C", "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductFilter.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  Download,\r\n  Filter,\r\n  Grid3X3,\r\n  List,\r\n  RefreshCw,\r\n  Search,\r\n} from \"lucide-react\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { brandOptions, categories } from \"@/constants/categories\";\r\nimport { ProductFilters } from \"@/lib/api/products\";\r\n\r\ninterface ProductFilterProps {\r\n  onFiltersChange?: (filters: ProductFilters) => void;\r\n  onRefresh?: () => void;\r\n  loading?: boolean;\r\n  initialFilters?: ProductFilters;\r\n}\r\n\r\nexport const ProductFilter = ({\r\n  onFiltersChange,\r\n  onRefresh,\r\n  loading = false,\r\n  initialFilters = {},\r\n}: ProductFilterProps) => {\r\n  const [filters, setFilters] = useState<ProductFilters>({\r\n    search: \"\",\r\n    category: \"\",\r\n    brand: \"\",\r\n    status: \"\",\r\n    sortBy: \"createdAt\",\r\n    sortOrder: \"desc\",\r\n    ...initialFilters,\r\n  });\r\n\r\n  const updateFilters = useCallback(\r\n    (newFilters: Partial<ProductFilters>) => {\r\n      const updatedFilters = { ...filters, ...newFilters };\r\n      setFilters(updatedFilters);\r\n      onFiltersChange?.(updatedFilters);\r\n    },\r\n    [filters, onFiltersChange]\r\n  );\r\n\r\n  const handleSearchChange = useCallback(\r\n    (value: string) => {\r\n      updateFilters({ search: value });\r\n    },\r\n    [updateFilters]\r\n  );\r\n\r\n  const handleCategoryChange = useCallback(\r\n    (value: string) => {\r\n      updateFilters({ category: value === \"all\" ? \"\" : value });\r\n    },\r\n    [updateFilters]\r\n  );\r\n\r\n  const handleBrandChange = useCallback(\r\n    (value: string) => {\r\n      updateFilters({ brand: value === \"all\" ? \"\" : value });\r\n    },\r\n    [updateFilters]\r\n  );\r\n\r\n  const handleStatusChange = useCallback(\r\n    (value: string) => {\r\n      updateFilters({ status: value === \"all\" ? \"\" : value });\r\n    },\r\n    [updateFilters]\r\n  );\r\n\r\n  const handleSortChange = useCallback(\r\n    (value: string) => {\r\n      const [sortBy, sortOrder] = value.split(\"-\");\r\n      updateFilters({\r\n        sortBy: sortBy as \"price\" | \"createdAt\" | \"name\",\r\n        sortOrder: sortOrder as \"asc\" | \"desc\",\r\n      });\r\n    },\r\n    [updateFilters]\r\n  );\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Search and Quick Actions */}\r\n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n        <div className=\"relative max-w-md flex-1\">\r\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n          <Input\r\n            placeholder=\"Search products by name, brand, or category...\"\r\n            className=\"pl-10\"\r\n            value={filters.search}\r\n            onChange={(e) => handleSearchChange(e.target.value)}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex gap-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={onRefresh}\r\n            disabled={loading}\r\n          >\r\n            <RefreshCw\r\n              className={`mr-2 h-4 w-4 ${loading ? \"animate-spin\" : \"\"}`}\r\n            />\r\n            Refresh\r\n          </Button>\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <Download className=\"mr-2 h-4 w-4\" />\r\n            Export\r\n          </Button>\r\n          <div className=\"flex rounded-md border\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"rounded-r-none border-r\"\r\n            >\r\n              <Grid3X3 className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"rounded-l-none\">\r\n              <List className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"flex flex-wrap gap-4\">\r\n        <Select\r\n          value={filters.category || \"all\"}\r\n          onValueChange={handleCategoryChange}\r\n        >\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <Filter className=\"mr-2 h-4 w-4\" />\r\n            <SelectValue placeholder=\"Category\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Categories</SelectItem>\r\n            {categories.map((category) => (\r\n              <SelectItem key={category.value} value={category.value}>\r\n                {category.label}\r\n              </SelectItem>\r\n            ))}\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select\r\n          value={filters.brand || \"all\"}\r\n          onValueChange={handleBrandChange}\r\n        >\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <SelectValue placeholder=\"Brand\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Brands</SelectItem>\r\n            {brandOptions.map((option) => (\r\n              <SelectItem key={option.value} value={option.value}>\r\n                {option.label}\r\n              </SelectItem>\r\n            ))}\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select\r\n          value={filters.status || \"all\"}\r\n          onValueChange={handleStatusChange}\r\n        >\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <SelectValue placeholder=\"Status\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Status</SelectItem>\r\n            <SelectItem value=\"in-stock\">In Stock</SelectItem>\r\n            <SelectItem value=\"out-of-stock\">Out of Stock</SelectItem>\r\n            <SelectItem value=\"draft\">Draft</SelectItem>\r\n            <SelectItem value=\"archived\">Archived</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select\r\n          value={`${filters.sortBy}-${filters.sortOrder}`}\r\n          onValueChange={handleSortChange}\r\n        >\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <SelectValue placeholder=\"Sort by\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"createdAt-desc\">Newest First</SelectItem>\r\n            <SelectItem value=\"createdAt-asc\">Oldest First</SelectItem>\r\n            <SelectItem value=\"name-asc\">Name A-Z</SelectItem>\r\n            <SelectItem value=\"name-desc\">Name Z-A</SelectItem>\r\n            <SelectItem value=\"price-asc\">Price Low-High</SelectItem>\r\n            <SelectItem value=\"price-desc\">Price High-Low</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AAOA;;;AAtBA;;;;;;;AAgCO,MAAM,gBAAgB,CAAC,EAC5B,eAAe,EACf,SAAS,EACT,UAAU,KAAK,EACf,iBAAiB,CAAC,CAAC,EACA;;IACnB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkB;QACrD,QAAQ;QACR,UAAU;QACV,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,GAAG,cAAc;IACnB;IAEA,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;oDAC9B,CAAC;YACC,MAAM,iBAAiB;gBAAE,GAAG,OAAO;gBAAE,GAAG,UAAU;YAAC;YACnD,WAAW;YACX,kBAAkB;QACpB;mDACA;QAAC;QAAS;KAAgB;IAG5B,MAAM,qBAAqB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;yDACnC,CAAC;YACC,cAAc;gBAAE,QAAQ;YAAM;QAChC;wDACA;QAAC;KAAc;IAGjB,MAAM,uBAAuB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;2DACrC,CAAC;YACC,cAAc;gBAAE,UAAU,UAAU,QAAQ,KAAK;YAAM;QACzD;0DACA;QAAC;KAAc;IAGjB,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;wDAClC,CAAC;YACC,cAAc;gBAAE,OAAO,UAAU,QAAQ,KAAK;YAAM;QACtD;uDACA;QAAC;KAAc;IAGjB,MAAM,qBAAqB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;yDACnC,CAAC;YACC,cAAc;gBAAE,QAAQ,UAAU,QAAQ,KAAK;YAAM;QACvD;wDACA;QAAC;KAAc;IAGjB,MAAM,mBAAmB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;uDACjC,CAAC;YACC,MAAM,CAAC,QAAQ,UAAU,GAAG,MAAM,KAAK,CAAC;YACxC,cAAc;gBACZ,QAAQ;gBACR,WAAW;YACb;QACF;sDACA;QAAC;KAAc;IAEjB,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,6RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,4TAAC,6HAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,WAAU;gCACV,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kCAItD,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,4TAAC,uSAAA,CAAA,YAAS;wCACR,WAAW,CAAC,aAAa,EAAE,UAAU,iBAAiB,IAAI;;;;;;oCAC1D;;;;;;;0CAGJ,4TAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,4TAAC,iSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,4TAAC,mSAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAC5C,cAAA,4TAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxB,4TAAC;gBAAI,WAAU;;kCACb,4TAAC,8HAAA,CAAA,SAAM;wBACL,OAAO,QAAQ,QAAQ,IAAI;wBAC3B,eAAe;;0CAEf,4TAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,4TAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,4TAAC,8HAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;;0CAE3B,4TAAC,8HAAA,CAAA,gBAAa;;kDACZ,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;oCACvB,0HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,yBACf,4TAAC,8HAAA,CAAA,aAAU;4CAAsB,OAAO,SAAS,KAAK;sDACnD,SAAS,KAAK;2CADA,SAAS,KAAK;;;;;;;;;;;;;;;;;kCAOrC,4TAAC,8HAAA,CAAA,SAAM;wBACL,OAAO,QAAQ,KAAK,IAAI;wBACxB,eAAe;;0CAEf,4TAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,4TAAC,8HAAA,CAAA,gBAAa;;kDACZ,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;oCACvB,0HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,uBACjB,4TAAC,8HAAA,CAAA,aAAU;4CAAoB,OAAO,OAAO,KAAK;sDAC/C,OAAO,KAAK;2CADE,OAAO,KAAK;;;;;;;;;;;;;;;;;kCAOnC,4TAAC,8HAAA,CAAA,SAAM;wBACL,OAAO,QAAQ,MAAM,IAAI;wBACzB,eAAe;;0CAEf,4TAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,4TAAC,8HAAA,CAAA,gBAAa;;kDACZ,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAe;;;;;;kDACjC,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAQ;;;;;;kDAC1B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;;;;;;;;;;;;;kCAIjC,4TAAC,8HAAA,CAAA,SAAM;wBACL,OAAO,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE;wBAC/C,eAAe;;0CAEf,4TAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,4TAAC,8HAAA,CAAA,gBAAa;;kDACZ,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAiB;;;;;;kDACnC,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAgB;;;;;;kDAClC,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;GApLa;KAAA", "debugId": null}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,2KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,4TAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf;KAVS", "debugId": null}}, {"offset": {"line": 1710, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\n\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAEA;AACA;AAEA;AAPA;;;;;;AASA,MAAM,SAAS,kRAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kRAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kRAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC;;0BACC,4TAAC;;;;;0BACD,4TAAC,kRAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,4TAAC,kRAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,4TAAC,mRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,4TAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,kRAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1861, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/confirmation-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { AlertTriangle, Loader2 } from \"lucide-react\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\n\r\ninterface ConfirmationDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  title: string;\r\n  description: string;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  onConfirm: () => void | Promise<void>;\r\n  loading?: boolean;\r\n  variant?: \"default\" | \"destructive\";\r\n}\r\n\r\nexport function ConfirmationDialog({\r\n  open,\r\n  onOpenChange,\r\n  title,\r\n  description,\r\n  confirmText = \"Confirm\",\r\n  cancelText = \"Cancel\",\r\n  onConfirm,\r\n  loading = false,\r\n  variant = \"default\",\r\n}: ConfirmationDialogProps) {\r\n  const handleConfirm = async () => {\r\n    await onConfirm();\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <div className=\"flex items-center gap-3\">\r\n            {variant === \"destructive\" && (\r\n              <div className=\"flex h-10 w-10 items-center justify-center rounded-full bg-red-100\">\r\n                <AlertTriangle className=\"h-5 w-5 text-red-600\" />\r\n              </div>\r\n            )}\r\n            <div>\r\n              <DialogTitle className=\"text-left\">{title}</DialogTitle>\r\n              <DialogDescription className=\"text-left\">\r\n                {description}\r\n              </DialogDescription>\r\n            </div>\r\n          </div>\r\n        </DialogHeader>\r\n        <DialogFooter className=\"flex gap-2 sm:gap-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => onOpenChange(false)}\r\n            disabled={loading}\r\n          >\r\n            {cancelText}\r\n          </Button>\r\n          <Button\r\n            variant={variant === \"destructive\" ? \"destructive\" : \"default\"}\r\n            onClick={handleConfirm}\r\n            disabled={loading}\r\n          >\r\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n            {confirmText}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAEA;AACA;AAPA;;;;;AA4BO,SAAS,mBAAmB,EACjC,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,SAAS,EACT,UAAU,KAAK,EACf,UAAU,SAAS,EACK;IACxB,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,4TAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,4TAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,4TAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,4TAAC;wBAAI,WAAU;;4BACZ,YAAY,+BACX,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC,+SAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAG7B,4TAAC;;kDACC,4TAAC,8HAAA,CAAA,cAAW;wCAAC,WAAU;kDAAa;;;;;;kDACpC,4TAAC,8HAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC1B;;;;;;;;;;;;;;;;;;;;;;;8BAKT,4TAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,4TAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;sCAET;;;;;;sCAEH,4TAAC,8HAAA,CAAA,SAAM;4BACL,SAAS,YAAY,gBAAgB,gBAAgB;4BACrD,SAAS;4BACT,UAAU;;gCAET,yBAAW,4TAAC,wSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B;;;;;;;;;;;;;;;;;;;;;;;;AAMb;KArDgB", "debugId": null}}, {"offset": {"line": 1999, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\n\r\nimport clsx from \"clsx\";\r\nimport { Edit, Eye, MoreHorizontal, Package, Star, Trash2 } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { ConfirmationDialog } from \"@/components/ui/confirmation-dialog\";\r\nimport { useProductMutations } from \"@/hooks/useProducts\";\r\n\r\ntype Product = {\r\n  _id?: string;\r\n  id?: number | string;\r\n  name: string;\r\n  price: number | string;\r\n  originalPrice?: number | string;\r\n  mainImage?: string;\r\n  image?: string;\r\n  category?: string;\r\n  brand?: string;\r\n  stock?: number;\r\n  status?:\r\n    | \"in-stock\"\r\n    | \"out-of-stock\"\r\n    | \"coming-soon\"\r\n    | \"archived\"\r\n    | \"draft\"\r\n    | \"suspended\"\r\n    | \"active\";\r\n  averageRating?: number;\r\n  rating?: number;\r\n  isOnSale?: boolean;\r\n  currency?: string;\r\n  saleEndsAt?: string | Date;\r\n};\r\n\r\nexport const ProductCard = ({\r\n  product,\r\n  index,\r\n  onDelete,\r\n}: {\r\n  product: Product;\r\n  index: number;\r\n  onDelete?: () => void;\r\n}) => {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const router = useRouter();\r\n  const { deleteProduct } = useProductMutations();\r\n\r\n  // Handle both backend (_id) and frontend (id) identifiers\r\n  const productId = product._id || product.id;\r\n\r\n  // Default values for optional properties\r\n  const stock = product.stock ?? 0;\r\n  const status = product.status ?? \"active\";\r\n  const rating = product.averageRating || product.rating ?? 0;\r\n  const isOnSale = product.isOnSale ?? (product.saleEndsAt ? new Date(product.saleEndsAt) > new Date() : false);\r\n\r\n  // Handle image - backend uses mainImage, frontend uses image\r\n  const productImage = product.mainImage || product.image || '/images/placeholder.jpg';\r\n\r\n  // Format price with currency\r\n  const formatPrice = (price: number | string, currency?: string) => {\r\n    const numPrice = typeof price === 'string' ? parseFloat(price) : price;\r\n    const currencySymbol = currency === 'EUR' ? '€' : '$';\r\n    return `${currencySymbol}${numPrice.toFixed(2)}`;\r\n  };\r\n\r\n  const handleViewProduct = () => {\r\n    router.push(`/admin/products/${productId}`);\r\n  };\r\n\r\n  const handleEditProduct = () => {\r\n    // Navigate to product details page - edit mode will be handled there\r\n    router.push(`/admin/products/${productId}?edit=true`);\r\n  };\r\n\r\n  const handleDeleteClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmDelete = async () => {\r\n    if (!productId) {\r\n      toast.error(\"Product ID not found\");\r\n      setShowDeleteDialog(false);\r\n      return;\r\n    }\r\n\r\n    setIsDeleting(true);\r\n    try {\r\n      await deleteProduct(productId.toString());\r\n      // Call the onDelete callback to refresh the product list\r\n      onDelete?.();\r\n      setShowDeleteDialog(false);\r\n    } catch (error) {\r\n      console.error(\"Failed to delete product:\", error);\r\n      // Error toast is already handled in the mutation hook\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case \"active\":\r\n      case \"in-stock\":\r\n        return \"bg-green-100 text-green-700\";\r\n      case \"draft\":\r\n      case \"coming-soon\":\r\n        return \"bg-yellow-100 text-yellow-700\";\r\n      case \"archived\":\r\n      case \"suspended\":\r\n        return \"bg-gray-100 text-gray-700\";\r\n      case \"out-of-stock\":\r\n        return \"bg-red-100 text-red-700\";\r\n      default:\r\n        return \"bg-gray-100 text-gray-700\";\r\n    }\r\n  };\r\n\r\n  const getStockStatus = (stock: number) => {\r\n    if (stock === 0) return { text: \"Out of Stock\", color: \"text-red-600\" };\r\n    if (stock < 10) return { text: \"Low Stock\", color: \"text-orange-600\" };\r\n    return { text: \"In Stock\", color: \"text-green-600\" };\r\n  };\r\n\r\n  const stockStatus = getStockStatus(stock);\r\n\r\n  return (\r\n    <Card className=\"group relative w-full overflow-hidden p-3 shadow-sm transition-all hover:shadow-lg\">\r\n      {/* Status Badge */}\r\n      {status !== \"active\" && (\r\n        <Badge\r\n          className={`absolute left-2 top-2 z-10 text-xs ${getStatusColor(status)}`}\r\n        >\r\n          {status.charAt(0).toUpperCase() + status.slice(1)}\r\n        </Badge>\r\n      )}\r\n\r\n      {/* Sale Badge */}\r\n      {isOnSale && (\r\n        <Badge className=\"absolute right-2 top-2 z-10 bg-red-500 text-white\">\r\n          Sale\r\n        </Badge>\r\n      )}\r\n\r\n      {/* Product Image */}\r\n      <div\r\n        className=\"relative mb-3 h-52 w-full cursor-pointer overflow-hidden rounded-md\"\r\n        onClick={handleViewProduct}\r\n      >\r\n        {isLoading && (\r\n          <Skeleton className=\"absolute inset-0 size-full rounded-md\" />\r\n        )}\r\n\r\n        <Image\r\n          src={productImage}\r\n          alt={product.name}\r\n          fill\r\n          priority={index === 0}\r\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 20vw\"\r\n          onLoad={() => setIsLoading(false)}\r\n          onError={() => setIsLoading(false)}\r\n          className={clsx(\r\n            \"rounded object-cover object-center transition-all duration-300 group-hover:scale-105\",\r\n            isLoading ? \"opacity-0\" : \"opacity-100\"\r\n          )}\r\n        />\r\n\r\n        {/* Quick Actions Overlay */}\r\n        <div className=\"absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity group-hover:opacity-100\">\r\n          <Button\r\n            size=\"sm\"\r\n            variant=\"secondary\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              handleViewProduct();\r\n            }}\r\n          >\r\n            <Eye className=\"mr-2 h-4 w-4\" />\r\n            View\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      <CardContent className=\"p-2\">\r\n        {/* Product Info */}\r\n        <div className=\"mb-2\">\r\n          <Link\r\n            href={`/admin/products/${productId}`}\r\n            className=\"text-sm font-medium hover:text-blue-600 hover:underline\"\r\n          >\r\n            {product.name}\r\n          </Link>\r\n          {product.category && (\r\n            <div className=\"text-xs text-gray-500\">{product.category}</div>\r\n          )}\r\n          {product.brand && (\r\n            <div className=\"text-xs text-gray-400\">{product.brand}</div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Rating */}\r\n        {rating > 0 && (\r\n          <div className=\"mb-2 flex items-center gap-1\">\r\n            <div className=\"flex\">\r\n              {[...Array(5)].map((_, i) => (\r\n                <Star\r\n                  key={i}\r\n                  className={clsx(\r\n                    \"h-3 w-3\",\r\n                    i < rating\r\n                      ? \"fill-yellow-400 text-yellow-400\"\r\n                      : \"text-gray-300\"\r\n                  )}\r\n                />\r\n              ))}\r\n            </div>\r\n            <span className=\"text-xs text-gray-500\">({rating})</span>\r\n          </div>\r\n        )}\r\n\r\n        {/* Price */}\r\n        <div className=\"mb-2 flex items-center gap-2\">\r\n          <span className=\"font-semibold text-gray-900\">\r\n            {formatPrice(product.price, product.currency)}\r\n          </span>\r\n          {product.originalPrice && isOnSale && (\r\n            <span className=\"text-sm text-gray-500 line-through\">\r\n              {formatPrice(product.originalPrice, product.currency)}\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {/* Stock Status */}\r\n        <div className=\"mb-3 flex items-center gap-1\">\r\n          <Package className=\"h-3 w-3 text-gray-400\" />\r\n          <span className={`text-xs ${stockStatus.color}`}>\r\n            {stockStatus.text} {stock > 0 && `(${stock})`}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Actions */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex gap-1\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"h-8 px-2 text-xs\"\r\n              onClick={handleEditProduct}\r\n            >\r\n              <Edit className=\"mr-1 h-3 w-3\" />\r\n              Edit\r\n            </Button>\r\n          </div>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n                <MoreHorizontal className=\"h-4 w-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\">\r\n              <DropdownMenuItem onClick={handleViewProduct}>\r\n                <Eye className=\"mr-2 h-4 w-4\" />\r\n                View Details\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleEditProduct}>\r\n                <Edit className=\"mr-2 h-4 w-4\" />\r\n                Edit Product\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                className=\"text-red-600\"\r\n                onClick={handleDeleteClick}\r\n                disabled={isDeleting}\r\n              >\r\n                <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                Delete Product\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </CardContent>\r\n\r\n      {/* Delete Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        open={showDeleteDialog}\r\n        onOpenChange={setShowDeleteDialog}\r\n        title=\"Delete Product\"\r\n        description={`Are you sure you want to delete \"${product.name}\"? This action cannot be undone and will permanently remove the product from your inventory.`}\r\n        confirmText={isDeleting ? \"Deleting...\" : \"Delete Product\"}\r\n        cancelText=\"Cancel\"\r\n        onConfirm={handleConfirmDelete}\r\n        loading={isDeleting}\r\n        variant=\"destructive\"\r\n      />\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;;;AAvBA;;;;;;;;;;;;;;;AAmDO,MAAM,cAAc,CAAC,EAC1B,OAAO,EACP,KAAK,EACL,QAAQ,EAKT;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD;IAE5C,0DAA0D;IAC1D,MAAM,YAAY,QAAQ,GAAG,IAAI,QAAQ,EAAE;IAE3C,yCAAyC;IACzC,MAAM,QAAQ,QAAQ,KAAK,IAAI;IAC/B,MAAM,SAAS,QAAQ,MAAM,IAAI;IACjC,MAAM,SAAS,CAAA,QAAQ,aAAa,IAAI,QAAQ,MAAM,AAAD,KAAK;IAC1D,MAAM,WAAW,QAAQ,QAAQ,IAAI,CAAC,QAAQ,UAAU,GAAG,IAAI,KAAK,QAAQ,UAAU,IAAI,IAAI,SAAS,KAAK;IAE5G,6DAA6D;IAC7D,MAAM,eAAe,QAAQ,SAAS,IAAI,QAAQ,KAAK,IAAI;IAE3D,6BAA6B;IAC7B,MAAM,cAAc,CAAC,OAAwB;QAC3C,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;QACjE,MAAM,iBAAiB,aAAa,QAAQ,MAAM;QAClD,OAAO,GAAG,iBAAiB,SAAS,OAAO,CAAC,IAAI;IAClD;IAEA,MAAM,oBAAoB;QACxB,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,WAAW;IAC5C;IAEA,MAAM,oBAAoB;QACxB,qEAAqE;QACrE,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,UAAU,UAAU,CAAC;IACtD;IAEA,MAAM,oBAAoB;QACxB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,WAAW;YACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,oBAAoB;YACpB;QACF;QAEA,cAAc;QACd,IAAI;YACF,MAAM,cAAc,UAAU,QAAQ;YACtC,yDAAyD;YACzD;YACA,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,sDAAsD;QACxD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;YAAE,MAAM;YAAgB,OAAO;QAAe;QACtE,IAAI,QAAQ,IAAI,OAAO;YAAE,MAAM;YAAa,OAAO;QAAkB;QACrE,OAAO;YAAE,MAAM;YAAY,OAAO;QAAiB;IACrD;IAEA,MAAM,cAAc,eAAe;IAEnC,qBACE,4TAAC,4HAAA,CAAA,OAAI;QAAC,WAAU;;YAEb,WAAW,0BACV,4TAAC,6HAAA,CAAA,QAAK;gBACJ,WAAW,CAAC,mCAAmC,EAAE,eAAe,SAAS;0BAExE,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;YAKlD,0BACC,4TAAC,6HAAA,CAAA,QAAK;gBAAC,WAAU;0BAAoD;;;;;;0BAMvE,4TAAC;gBACC,WAAU;gBACV,SAAS;;oBAER,2BACC,4TAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCAGtB,4TAAC,+PAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,UAAU,UAAU;wBACpB,OAAM;wBACN,QAAQ,IAAM,aAAa;wBAC3B,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EACZ,wFACA,YAAY,cAAc;;;;;;kCAK9B,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;;8CAEA,4TAAC,uRAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAMtC,4TAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8RAAA,CAAA,UAAI;gCACH,MAAM,CAAC,gBAAgB,EAAE,WAAW;gCACpC,WAAU;0CAET,QAAQ,IAAI;;;;;;4BAEd,QAAQ,QAAQ,kBACf,4TAAC;gCAAI,WAAU;0CAAyB,QAAQ,QAAQ;;;;;;4BAEzD,QAAQ,KAAK,kBACZ,4TAAC;gCAAI,WAAU;0CAAyB,QAAQ,KAAK;;;;;;;;;;;;oBAKxD,SAAS,mBACR,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,4TAAC,yRAAA,CAAA,OAAI;wCAEH,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EACZ,WACA,IAAI,SACA,oCACA;uCALD;;;;;;;;;;0CAUX,4TAAC;gCAAK,WAAU;;oCAAwB;oCAAE;oCAAO;;;;;;;;;;;;;kCAKrD,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAK,WAAU;0CACb,YAAY,QAAQ,KAAK,EAAE,QAAQ,QAAQ;;;;;;4BAE7C,QAAQ,aAAa,IAAI,0BACxB,4TAAC;gCAAK,WAAU;0CACb,YAAY,QAAQ,aAAa,EAAE,QAAQ,QAAQ;;;;;;;;;;;;kCAM1D,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,+RAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,4TAAC;gCAAK,WAAW,CAAC,QAAQ,EAAE,YAAY,KAAK,EAAE;;oCAC5C,YAAY,IAAI;oCAAC;oCAAE,QAAQ,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;;;;;;;;;;;;;kCAKjD,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;;sDAET,4TAAC,kSAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAKrC,4TAAC,wIAAA,CAAA,eAAY;;kDACX,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,4TAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,4TAAC,uSAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG9B,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,4TAAC,wIAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,4TAAC,uRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,4TAAC,wIAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,4TAAC,kSAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,4TAAC,wIAAA,CAAA,wBAAqB;;;;;0DACtB,4TAAC,wIAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS;gDACT,UAAU;;kEAEV,4TAAC,iSAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,OAAM;gBACN,aAAa,CAAC,iCAAiC,EAAE,QAAQ,IAAI,CAAC,4FAA4F,CAAC;gBAC3J,aAAa,aAAa,gBAAgB;gBAC1C,YAAW;gBACX,WAAW;gBACX,SAAS;gBACT,SAAQ;;;;;;;;;;;;AAIhB;GAzQa;;QAYI,oQAAA,CAAA,YAAS;QACE,uHAAA,CAAA,sBAAmB;;;KAblC", "debugId": null}}, {"offset": {"line": 2508, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductGrid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AlertCircle, RefreshCw } from \"lucide-react\";\r\n\r\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nimport { ProductCard } from \"./ProductCard\";\r\n\r\ninterface ProductGridProps {\r\n  products?: unknown[];\r\n  loading?: boolean;\r\n  error?: string | null;\r\n  onRefresh?: () => void;\r\n}\r\n\r\nexport const ProductGrid = ({\r\n  products = [],\r\n  loading = false,\r\n  error = null,\r\n  onRefresh,\r\n}: ProductGridProps) => {\r\n  if (loading) {\r\n    return (\r\n      <section className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5\">\r\n        {Array.from({ length: 8 }).map((_, index) => (\r\n          <div key={index} className=\"w-full\">\r\n            <Skeleton className=\"h-64 w-full rounded-lg\" />\r\n            <div className=\"mt-4 space-y-2\">\r\n              <Skeleton className=\"h-4 w-3/4\" />\r\n              <Skeleton className=\"h-4 w-1/2\" />\r\n              <Skeleton className=\"h-6 w-1/4\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </section>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Alert className=\"mx-auto max-w-md\">\r\n        <AlertCircle className=\"h-4 w-4\" />\r\n        <AlertDescription className=\"flex items-center justify-between\">\r\n          <span>Failed to load products: {error}</span>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={onRefresh}\r\n            className=\"ml-2\"\r\n          >\r\n            <RefreshCw className=\"mr-1 h-4 w-4\" />\r\n            Retry\r\n          </Button>\r\n        </AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n\r\n  if (!products || products.length === 0) {\r\n    return (\r\n      <div className=\"py-12 text-center\">\r\n        <p className=\"text-lg text-gray-500\">No products found</p>\r\n        <Button variant=\"outline\" onClick={onRefresh} className=\"mt-4\">\r\n          <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n          Refresh\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <section className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5\">\r\n      {products.map((product, index) => (\r\n        <ProductCard\r\n          key={(product as any)?._id || (product as any)?.id || index}\r\n          product={product as any}\r\n          index={index}\r\n          onDelete={onRefresh}\r\n        />\r\n      ))}\r\n    </section>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAEA;AACA;AACA;AAEA;AARA;;;;;;;AAiBO,MAAM,cAAc,CAAC,EAC1B,WAAW,EAAE,EACb,UAAU,KAAK,EACf,QAAQ,IAAI,EACZ,SAAS,EACQ;IACjB,IAAI,SAAS;QACX,qBACE,4TAAC;YAAQ,WAAU;sBAChB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,4TAAC;oBAAgB,WAAU;;sCACzB,4TAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,4TAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,4TAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;mBALd;;;;;;;;;;IAWlB;IAEA,IAAI,OAAO;QACT,qBACE,4TAAC,6HAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,4TAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,4TAAC,6HAAA,CAAA,mBAAgB;oBAAC,WAAU;;sCAC1B,4TAAC;;gCAAK;gCAA0B;;;;;;;sCAChC,4TAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,4TAAC,uSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;IAMhD;IAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,qBACE,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAE,WAAU;8BAAwB;;;;;;8BACrC,4TAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,SAAS;oBAAW,WAAU;;sCACtD,4TAAC,uSAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAK9C;IAEA,qBACE,4TAAC;QAAQ,WAAU;kBAChB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,4TAAC,kJAAA,CAAA,cAAW;gBAEV,SAAS;gBACT,OAAO;gBACP,UAAU;eAHL,AAAC,SAAiB,OAAQ,SAAiB,MAAM;;;;;;;;;;AAQhE;KAnEa", "debugId": null}}, {"offset": {"line": 2707, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductPagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { Chevron<PERSON>eft, ChevronRight } from \"lucide-react\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\n\r\nexport const ProductPagination = () => {\r\n  return (\r\n    <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n      {/* Results info */}\r\n      <div className=\"text-sm text-gray-500\">\r\n        Showing <span className=\"font-medium text-gray-900\">1-12</span> of{\" \"}\r\n        <span className=\"font-medium text-gray-900\">48</span> products\r\n      </div>\r\n\r\n      {/* Pagination controls */}\r\n      <div className=\"flex items-center gap-4\">\r\n        {/* Items per page */}\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-gray-500\">Show:</span>\r\n          <Select defaultValue=\"12\">\r\n            <SelectTrigger className=\"w-[70px]\">\r\n              <SelectValue />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"12\">12</SelectItem>\r\n              <SelectItem value=\"24\">24</SelectItem>\r\n              <SelectItem value=\"48\">48</SelectItem>\r\n              <SelectItem value=\"96\">96</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n\r\n        {/* Page navigation */}\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button variant=\"outline\" size=\"sm\" disabled>\r\n            <ChevronLeft className=\"h-4 w-4\" />\r\n            Previous\r\n          </Button>\r\n\r\n          {/* Page numbers */}\r\n          <div className=\"flex items-center gap-1\">\r\n            <Button variant=\"default\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              1\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              2\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              3\r\n            </Button>\r\n            <span className=\"px-2 text-sm text-gray-500\">...</span>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              4\r\n            </Button>\r\n          </div>\r\n\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            Next\r\n            <ChevronRight className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAEA;AACA;AAPA;;;;;AAeO,MAAM,oBAAoB;IAC/B,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;oBAAwB;kCAC7B,4TAAC;wBAAK,WAAU;kCAA4B;;;;;;oBAAW;oBAAI;kCACnE,4TAAC;wBAAK,WAAU;kCAA4B;;;;;;oBAAS;;;;;;;0BAIvD,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,4TAAC,8HAAA,CAAA,SAAM;gCAAC,cAAa;;kDACnB,4TAAC,8HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,4TAAC,8HAAA,CAAA,gBAAa;;0DACZ,4TAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,4TAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,4TAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,4TAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAM7B,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,QAAQ;;kDAC1C,4TAAC,2SAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAKrC,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,4TAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAK9D,4TAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;oCAAK;kDAElC,4TAAC,6SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;KA3Da", "debugId": null}}, {"offset": {"line": 2962, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductListWrapper.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, {\r\n  forwardRef,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useState,\r\n} from \"react\";\r\n\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { useProducts } from \"@/hooks/useProducts\";\r\nimport { ProductFilters } from \"@/lib/api/products\";\r\n\r\nimport { ProductFilter } from \"./ProductFilter\";\r\nimport { ProductGrid } from \"./ProductGrid\";\r\nimport { ProductPagination } from \"./ProductPagination\";\r\n\r\nexport interface ProductListWrapperRef {\r\n  handleRefresh: () => void;\r\n  loading: boolean;\r\n}\r\n\r\nexport interface ProductListWrapperProps {\r\n  initialFilters?: ProductFilters;\r\n}\r\n\r\nexport const ProductListWrapper = forwardRef<\r\n  ProductListWrapperRef,\r\n  ProductListWrapperProps\r\n>(({ initialFilters = {} }, ref) => {\r\n  const [filters, setFilters] = useState<ProductFilters>(initialFilters);\r\n  const { products, loading, error, updateFilters, refreshProducts, meta } =\r\n    useProducts(filters);\r\n\r\n  const handleFiltersChange = (newFilters: ProductFilters) => {\r\n    setFilters(newFilters);\r\n    updateFilters(newFilters);\r\n  };\r\n\r\n  const handleRefresh = () => {\r\n    refreshProducts();\r\n  };\r\n\r\n  // Expose methods to parent component\r\n  useImperativeHandle(ref, () => ({\r\n    handleRefresh,\r\n    loading,\r\n  }));\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardContent className=\"p-6\">\r\n          <ProductFilter\r\n            onFiltersChange={handleFiltersChange}\r\n            onRefresh={handleRefresh}\r\n            loading={loading}\r\n            initialFilters={initialFilters}\r\n          />\r\n\r\n          <Separator className=\"my-3 mb-6\" />\r\n\r\n          <ProductGrid\r\n            products={products}\r\n            loading={loading}\r\n            error={error}\r\n            onRefresh={handleRefresh}\r\n          />\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {meta && meta.pages > 1 && (\r\n        <div className=\"mt-6\">\r\n          <ProductPagination\r\n            currentPage={meta.page}\r\n            totalPages={meta.pages}\r\n            onPageChange={(page) => handleFiltersChange({ ...filters, page })}\r\n          />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nProductListWrapper.displayName = \"ProductListWrapper\";\r\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AACA;AACA;AAGA;AACA;AACA;;;AAhBA;;;;;;;;AA2BO,MAAM,mCAAqB,GAAA,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,UAGzC,CAAC,EAAE,iBAAiB,CAAC,CAAC,EAAE,EAAE;;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,IAAI,EAAE,GACtE,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;IAEd,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,cAAc;IAChB;IAEA,MAAM,gBAAgB;QACpB;IACF;IAEA,qCAAqC;IACrC,CAAA,GAAA,4RAAA,CAAA,sBAAmB,AAAD,EAAE;kDAAK,IAAM,CAAC;gBAC9B;gBACA;YACF,CAAC;;IAED,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,4HAAA,CAAA,OAAI;0BACH,cAAA,4TAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,4TAAC,oJAAA,CAAA,gBAAa;4BACZ,iBAAiB;4BACjB,WAAW;4BACX,SAAS;4BACT,gBAAgB;;;;;;sCAGlB,4TAAC,iIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCAErB,4TAAC,kJAAA,CAAA,cAAW;4BACV,UAAU;4BACV,SAAS;4BACT,OAAO;4BACP,WAAW;;;;;;;;;;;;;;;;;YAKhB,QAAQ,KAAK,KAAK,GAAG,mBACpB,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,wJAAA,CAAA,oBAAiB;oBAChB,aAAa,KAAK,IAAI;oBACtB,YAAY,KAAK,KAAK;oBACtB,cAAc,CAAC,OAAS,oBAAoB;4BAAE,GAAG,OAAO;4BAAE;wBAAK;;;;;;;;;;;;;;;;;AAM3E;;QAlDI,uHAAA,CAAA,cAAW;;;;QAAX,uHAAA,CAAA,cAAW;;;;AAoDf,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3094, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/app/admin/products/list/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useRef, useState } from \"react\";\r\n\r\nimport { useSearchParams } from \"next/navigation\";\r\n\r\nimport { PageHeaderWrapper } from \"@/components/common/PageHeaderWrapper\";\r\nimport { ProductListActions } from \"@/components/pages/products/ProductListActions\";\r\nimport { ProductListWrapper } from \"@/components/pages/products/ProductListWrapper\";\r\nimport { ProductFilters } from \"@/lib/api/products\";\r\n\r\nexport default function AdminProductsList() {\r\n  const searchParams = useSearchParams();\r\n  const [initialFilters, setInitialFilters] = useState<ProductFilters>({});\r\n  const wrapperRef = useRef<{ handleRefresh: () => void; loading: boolean }>(\r\n    null\r\n  );\r\n\r\n  // Extract filters from URL parameters\r\n  useEffect(() => {\r\n    const filters: ProductFilters = {};\r\n\r\n    // Get category from URL\r\n    const category = searchParams.get(\"category\");\r\n    if (category) {\r\n      filters.category = category;\r\n    }\r\n\r\n    // Get other potential filters from URL\r\n    const brand = searchParams.get(\"brand\");\r\n    if (brand) {\r\n      filters.brand = brand;\r\n    }\r\n\r\n    const search = searchParams.get(\"search\");\r\n    if (search) {\r\n      filters.search = search;\r\n    }\r\n\r\n    const status = searchParams.get(\"status\");\r\n    if (status) {\r\n      filters.status = status;\r\n    }\r\n\r\n    const minPrice = searchParams.get(\"minPrice\");\r\n    if (minPrice) {\r\n      filters.minPrice = Number(minPrice);\r\n    }\r\n\r\n    const maxPrice = searchParams.get(\"maxPrice\");\r\n    if (maxPrice) {\r\n      filters.maxPrice = Number(maxPrice);\r\n    }\r\n\r\n    const sortBy = searchParams.get(\"sortBy\");\r\n    if (sortBy) {\r\n      filters.sortBy = sortBy as \"price\" | \"createdAt\" | \"name\";\r\n    }\r\n\r\n    const sortOrder = searchParams.get(\"sortOrder\");\r\n    if (sortOrder) {\r\n      filters.sortOrder = sortOrder as \"asc\" | \"desc\";\r\n    }\r\n\r\n    setInitialFilters(filters);\r\n  }, [searchParams]);\r\n\r\n  const handleRefresh = () => {\r\n    wrapperRef.current?.handleRefresh();\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <PageHeaderWrapper\r\n        title=\"Products\"\r\n        description=\"Browse and manage your product catalog\"\r\n      >\r\n        <ProductListActions\r\n          onRefresh={handleRefresh}\r\n          loading={wrapperRef.current?.loading || false}\r\n        />\r\n      </PageHeaderWrapper>\r\n\r\n      <div className=\"container mx-auto mt-6\">\r\n        <ProductListWrapper\r\n          ref={wrapperRef}\r\n          initialFilters={initialFilters}\r\n          key={JSON.stringify(initialFilters)}\r\n        />\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AACA;AACA;;;AARA;;;;;;AAWe,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,oQAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IACtE,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EACtB;IAGF,sCAAsC;IACtC,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,UAA0B,CAAC;YAEjC,wBAAwB;YACxB,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,IAAI,UAAU;gBACZ,QAAQ,QAAQ,GAAG;YACrB;YAEA,uCAAuC;YACvC,MAAM,QAAQ,aAAa,GAAG,CAAC;YAC/B,IAAI,OAAO;gBACT,QAAQ,KAAK,GAAG;YAClB;YAEA,MAAM,SAAS,aAAa,GAAG,CAAC;YAChC,IAAI,QAAQ;gBACV,QAAQ,MAAM,GAAG;YACnB;YAEA,MAAM,SAAS,aAAa,GAAG,CAAC;YAChC,IAAI,QAAQ;gBACV,QAAQ,MAAM,GAAG;YACnB;YAEA,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,IAAI,UAAU;gBACZ,QAAQ,QAAQ,GAAG,OAAO;YAC5B;YAEA,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,IAAI,UAAU;gBACZ,QAAQ,QAAQ,GAAG,OAAO;YAC5B;YAEA,MAAM,SAAS,aAAa,GAAG,CAAC;YAChC,IAAI,QAAQ;gBACV,QAAQ,MAAM,GAAG;YACnB;YAEA,MAAM,YAAY,aAAa,GAAG,CAAC;YACnC,IAAI,WAAW;gBACb,QAAQ,SAAS,GAAG;YACtB;YAEA,kBAAkB;QACpB;sCAAG;QAAC;KAAa;IAEjB,MAAM,gBAAgB;QACpB,WAAW,OAAO,EAAE;IACtB;IAEA,qBACE;;0BACE,4TAAC,6IAAA,CAAA,oBAAiB;gBAChB,OAAM;gBACN,aAAY;0BAEZ,cAAA,4TAAC,yJAAA,CAAA,qBAAkB;oBACjB,WAAW;oBACX,SAAS,WAAW,OAAO,EAAE,WAAW;;;;;;;;;;;0BAI5C,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,yJAAA,CAAA,qBAAkB;oBACjB,KAAK;oBACL,gBAAgB;mBACX,KAAK,SAAS,CAAC;;;;;;;;;;;;AAK9B;GAjFwB;;QACD,oQAAA,CAAA,kBAAe;;;KADd", "debugId": null}}]}