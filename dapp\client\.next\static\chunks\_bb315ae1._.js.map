{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/layout/NavItems.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nimport {\r\n  BarChart2,\r\n  Blocks,\r\n  CreditCard,\r\n  Flame,\r\n  Hammer,\r\n  LayoutDashboard,\r\n  List,\r\n  Package,\r\n  Palette,\r\n  PlusCircle,\r\n  Settings,\r\n  Star,\r\n  Store,\r\n  Tag,\r\n  Users,\r\n} from \"lucide-react\";\r\n\r\nexport type NavItemType = {\r\n  label: string;\r\n  icon: React.ReactNode;\r\n  href?: string;\r\n  subItems?: {\r\n    label: string;\r\n    icon?: React.ReactNode;\r\n    href: string;\r\n    badge?: number;\r\n    description?: string;\r\n  }[];\r\n  badge?: number;\r\n  isDividerAfter?: boolean;\r\n  isNew?: boolean;\r\n  description?: string;\r\n  group?: string;\r\n};\r\n\r\nexport const NavItems: NavItemType[] = [\r\n  // Main Navigation\r\n  {\r\n    label: \"Dashboard\",\r\n    icon: <LayoutDashboard size={18} />,\r\n    href: \"/admin/dashboard\",\r\n    description: \"Overview and analytics\",\r\n    group: \"main\",\r\n  },\r\n\r\n  // Catalog Management\r\n  {\r\n    label: \"Products\",\r\n    icon: <Package size={18} />,\r\n    description: \"Manage your product catalog\",\r\n    group: \"catalog\",\r\n    subItems: [\r\n      {\r\n        label: \"All Products\",\r\n        icon: <List size={16} />,\r\n        href: \"/admin/products/list\",\r\n        description: \"View and manage all products\",\r\n      },\r\n      {\r\n        label: \"Add Product\",\r\n        icon: <PlusCircle size={16} />,\r\n        href: \"/admin/products/add\",\r\n        description: \"Create a new product\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    label: \"Catalog Settings\",\r\n    icon: <Blocks size={18} />,\r\n    href: \"/admin/products/catalog-settings\",\r\n    description: \"Organize your catalog with categories, brands, and materials\",\r\n    group: \"catalog\",\r\n  },\r\n\r\n  // Sales & Orders\r\n  {\r\n    label: \"Orders\",\r\n    icon: <CreditCard size={18} />,\r\n    href: \"/admin/orders/list\",\r\n    description: \"Manage customer orders\",\r\n    group: \"sales\",\r\n    badge: 12,\r\n  },\r\n  {\r\n    label: \"Customers\",\r\n    icon: <Users size={18} />,\r\n    description: \"Manage customer relationships\",\r\n    group: \"sales\",\r\n    subItems: [\r\n      {\r\n        label: \"All Customers\",\r\n        icon: <List size={16} />,\r\n        href: \"/admin/customers/list\",\r\n        description: \"View and manage all customers\",\r\n      },\r\n      {\r\n        label: \"Create Account\",\r\n        icon: <PlusCircle size={16} />,\r\n        href: \"/admin/customers/add\",\r\n        description:\r\n          \"Create customer account for phone orders & business clients\",\r\n      },\r\n    ],\r\n  },\r\n\r\n  // Analytics & Reports\r\n  {\r\n    label: \"Analytics\",\r\n    icon: <BarChart2 size={18} />,\r\n    href: \"/admin/analytics\",\r\n    description: \"Visual insights and performance analytics\",\r\n    group: \"analytics\",\r\n  },\r\n  {\r\n    label: \"Reviews\",\r\n    icon: <Star size={18} />,\r\n    href: \"/admin/reviews\",\r\n    description: \"Customer feedback and ratings\",\r\n    group: \"analytics\",\r\n    badge: 3,\r\n  },\r\n\r\n  // Financial\r\n  {\r\n    label: \"Transactions\",\r\n    icon: <CreditCard size={18} />,\r\n    badge: 8,\r\n    href: \"/admin/transactions\",\r\n    description: \"Payment and transaction history\",\r\n    group: \"financial\",\r\n  },\r\n  {\r\n    label: \"Sellers\",\r\n    icon: <Store size={18} />,\r\n    href: \"/admin/sellers\",\r\n    description: \"Manage seller accounts\",\r\n    group: \"financial\",\r\n  },\r\n\r\n  // Marketing\r\n  {\r\n    label: \"Promotions\",\r\n    icon: <Flame size={18} />,\r\n    isDividerAfter: true,\r\n    href: \"/admin/promotions\",\r\n    description: \"Manage sales, discounts, and promotional campaigns\",\r\n    group: \"marketing\",\r\n    isNew: true,\r\n  },\r\n\r\n  // System\r\n  {\r\n    label: \"Appearance\",\r\n    icon: <Palette size={18} />,\r\n    href: \"/admin/appearance\",\r\n    description: \"Customize your store's look\",\r\n    group: \"system\",\r\n  },\r\n  {\r\n    label: \"Settings\",\r\n    icon: <Settings size={18} />,\r\n    href: \"/admin/settings\",\r\n    description: \"System configuration\",\r\n    group: \"system\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAoCO,MAAM,WAA0B;IACrC,kBAAkB;IAClB;QACE,OAAO;QACP,oBAAM,4TAAC,mTAAA,CAAA,kBAAe;YAAC,MAAM;;;;;;QAC7B,MAAM;QACN,aAAa;QACb,OAAO;IACT;IAEA,qBAAqB;IACrB;QACE,OAAO;QACP,oBAAM,4TAAC,+RAAA,CAAA,UAAO;YAAC,MAAM;;;;;;QACrB,aAAa;QACb,OAAO;QACP,UAAU;YACR;gBACE,OAAO;gBACP,oBAAM,4TAAC,yRAAA,CAAA,OAAI;oBAAC,MAAM;;;;;;gBAClB,MAAM;gBACN,aAAa;YACf;YACA;gBACE,OAAO;gBACP,oBAAM,4TAAC,ySAAA,CAAA,aAAU;oBAAC,MAAM;;;;;;gBACxB,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,oBAAM,4TAAC,6RAAA,CAAA,SAAM;YAAC,MAAM;;;;;;QACpB,MAAM;QACN,aAAa;QACb,OAAO;IACT;IAEA,iBAAiB;IACjB;QACE,OAAO;QACP,oBAAM,4TAAC,ySAAA,CAAA,aAAU;YAAC,MAAM;;;;;;QACxB,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,oBAAM,4TAAC,2RAAA,CAAA,QAAK;YAAC,MAAM;;;;;;QACnB,aAAa;QACb,OAAO;QACP,UAAU;YACR;gBACE,OAAO;gBACP,oBAAM,4TAAC,yRAAA,CAAA,OAAI;oBAAC,MAAM;;;;;;gBAClB,MAAM;gBACN,aAAa;YACf;YACA;gBACE,OAAO;gBACP,oBAAM,4TAAC,ySAAA,CAAA,aAAU;oBAAC,MAAM;;;;;;gBACxB,MAAM;gBACN,aACE;YACJ;SACD;IACH;IAEA,sBAAsB;IACtB;QACE,OAAO;QACP,oBAAM,4TAAC,uTAAA,CAAA,YAAS;YAAC,MAAM;;;;;;QACvB,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,OAAO;QACP,oBAAM,4TAAC,yRAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;IACT;IAEA,YAAY;IACZ;QACE,OAAO;QACP,oBAAM,4TAAC,ySAAA,CAAA,aAAU;YAAC,MAAM;;;;;;QACxB,OAAO;QACP,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,OAAO;QACP,oBAAM,4TAAC,2RAAA,CAAA,QAAK;YAAC,MAAM;;;;;;QACnB,MAAM;QACN,aAAa;QACb,OAAO;IACT;IAEA,YAAY;IACZ;QACE,OAAO;QACP,oBAAM,4TAAC,2RAAA,CAAA,QAAK;YAAC,MAAM;;;;;;QACnB,gBAAgB;QAChB,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;IACT;IAEA,SAAS;IACT;QACE,OAAO;QACP,oBAAM,4TAAC,+RAAA,CAAA,UAAO;YAAC,MAAM;;;;;;QACrB,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA;QACE,OAAO;QACP,oBAAM,4TAAC,iSAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,MAAM;QACN,aAAa;QACb,OAAO;IACT;CACD", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/layout/Sidebar.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\n\r\nimport clsx from \"clsx\";\r\nimport {\r\n  ChevronDown,\r\n  ChevronRight,\r\n  IndentDecrease,\r\n  IndentIncrease,\r\n  Search,\r\n  Sparkles,\r\n} from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\n\r\nimport { NavItemType, NavItems } from \"./NavItems\";\r\n\r\n// Create a custom event for sidebar collapse state changes\r\nconst SIDEBAR_COLLAPSE_EVENT = \"sidebarCollapseChange\";\r\n\r\n// Create a function to dispatch the event\r\nconst dispatchCollapseEvent = (isCollapsed: boolean) => {\r\n  const event = new CustomEvent(SIDEBAR_COLLAPSE_EVENT, {\r\n    detail: { collapsed: isCollapsed },\r\n  });\r\n  window.dispatchEvent(event);\r\n};\r\n\r\nexport default function Sidebar() {\r\n  const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({});\r\n  const [collapsed, setCollapsed] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n\r\n  // Notify about collapse state changes\r\n  const handleCollapseToggle = () => {\r\n    const newCollapsedState = !collapsed;\r\n    setCollapsed(newCollapsedState);\r\n    dispatchCollapseEvent(newCollapsedState);\r\n  };\r\n\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  useEffect(() => {\r\n    let activeParentLabel: string | null = null;\r\n    for (const item of NavItems) {\r\n      if (item.subItems) {\r\n        const basePath = item.subItems[0]?.href.substring(\r\n          0,\r\n          item.subItems[0].href.lastIndexOf(\"/\")\r\n        );\r\n        if (basePath && pathname.startsWith(basePath + \"/\")) {\r\n          if (\r\n            item.subItems.some(\r\n              (sub) =>\r\n                pathname === sub.href || pathname.startsWith(sub.href + \"/\")\r\n            )\r\n          ) {\r\n            activeParentLabel = item.label;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if (activeParentLabel) {\r\n      setOpenMenus((prev) => ({ ...prev, [activeParentLabel!]: true }));\r\n    }\r\n    // If you want to close *all other* menus when one becomes active:\r\n    // const newOpenMenus: Record<string, boolean> = {};\r\n    // if (activeParentLabel) {\r\n    //   newOpenMenus[activeParentLabel] = true;\r\n    // }\r\n    // setOpenMenus(newOpenMenus);\r\n  }, [pathname]);\r\n\r\n  const toggleMenu = (label: string) => {\r\n    setOpenMenus((prev) => ({ ...prev, [label]: !prev[label] }));\r\n  };\r\n\r\n  const handleParentClick = (item: NavItemType) => {\r\n    if (item.subItems?.length) {\r\n      const isMenuOpen = openMenus[item.label];\r\n\r\n      if (isMenuOpen) {\r\n        toggleMenu(item.label);\r\n        return;\r\n      }\r\n\r\n      toggleMenu(item.label);\r\n\r\n      const isAlreadyInSection = item.subItems.some((sub) =>\r\n        pathname.startsWith(sub.href)\r\n      );\r\n\r\n      if (!isAlreadyInSection) {\r\n        router.push(item.subItems[0].href);\r\n      }\r\n    } else if (item.href) {\r\n      router.push(item.href);\r\n    }\r\n  };\r\n\r\n  // Filter navigation items based on search query\r\n  const filteredNavItems = NavItems.filter((item) => {\r\n    if (!searchQuery) return true;\r\n\r\n    const query = searchQuery.toLowerCase();\r\n    const matchesLabel = item.label.toLowerCase().includes(query);\r\n    const matchesDescription = item.description?.toLowerCase().includes(query);\r\n    const matchesSubItems = item.subItems?.some(\r\n      (sub) =>\r\n        sub.label.toLowerCase().includes(query) ||\r\n        sub.description?.toLowerCase().includes(query)\r\n    );\r\n\r\n    return matchesLabel || matchesDescription || matchesSubItems;\r\n  });\r\n\r\n  return (\r\n    <aside\r\n      className={`fixed left-0 top-0 flex h-screen flex-col border-r bg-white transition-all duration-200 ${\r\n        collapsed ? \"w-20\" : \"w-72\"\r\n      }`}\r\n    >\r\n      {/* Header Section */}\r\n      <div className=\"border-b border-gray-100 p-4\">\r\n        <div\r\n          className={clsx(\"flex items-center justify-between\", {\r\n            \"px-1\": collapsed,\r\n            \"px-0\": !collapsed,\r\n          })}\r\n        >\r\n          {/* Logo */}\r\n          {!collapsed && (\r\n            <Link href={\"/admin/dashboard\"}>\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"rounded-lg bg-gradient-to-br from-blue-600 to-blue-700 p-2.5 text-white shadow-lg\">\r\n                  <svg width=\"20\" height=\"20\" fill=\"currentColor\">\r\n                    <circle cx=\"10\" cy=\"10\" r=\"8\" />\r\n                  </svg>\r\n                </div>\r\n                <div>\r\n                  <div className=\"text-lg font-bold text-gray-900\">Admin</div>\r\n                  <div className=\"-mt-1 text-xs text-gray-500\">\r\n                    E-commerce Dashboard\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </Link>\r\n          )}\r\n\r\n          {/* Toggle collapse button */}\r\n          <button\r\n            className=\"rounded-lg p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-900\"\r\n            onClick={handleCollapseToggle}\r\n          >\r\n            {collapsed ? (\r\n              <IndentIncrease size={18} />\r\n            ) : (\r\n              <IndentDecrease size={18} />\r\n            )}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Navigation Search */}\r\n        {!collapsed && (\r\n          <div className=\"relative mt-4\">\r\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Find navigation...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              className=\"w-full rounded-lg border border-gray-200 bg-gray-50 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n            />\r\n            {searchQuery && (\r\n              <div className=\"absolute right-3 top-1/2 -translate-y-1/2\">\r\n                <button\r\n                  onClick={() => setSearchQuery(\"\")}\r\n                  className=\"text-gray-400 hover:text-gray-600\"\r\n                >\r\n                  <span className=\"text-xs\">✕</span>\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <nav className=\"flex-1 space-y-1 overflow-y-auto p-4 text-sm\">\r\n        {filteredNavItems.map((item, index) => {\r\n          const isParentItselfActive = !!item.href && pathname === item.href;\r\n          const isParentHighlighted = isParentItselfActive;\r\n\r\n          return (\r\n            <div key={index} className=\"group\">\r\n              {/* Parent item Button/Link */}\r\n              <div className=\"relative\">\r\n                <button\r\n                  onClick={() => handleParentClick(item)}\r\n                  className={clsx(\r\n                    \"flex w-full items-center rounded-lg px-3 py-2.5 transition-all duration-150 hover:bg-gray-50\",\r\n                    {\r\n                      \"border border-blue-200 bg-blue-50 font-medium text-blue-700 shadow-sm\":\r\n                        isParentHighlighted,\r\n                      \"text-gray-700 hover:text-gray-900\": !isParentHighlighted,\r\n                      \"justify-center\": collapsed,\r\n                    }\r\n                  )}\r\n                  title={collapsed ? item.label : undefined}\r\n                >\r\n                  <span\r\n                    className={clsx(\"flex-shrink-0\", { \"mr-3\": !collapsed })}\r\n                  >\r\n                    {item.icon}\r\n                  </span>\r\n\r\n                  {!collapsed && (\r\n                    <>\r\n                      <div className=\"flex-1 text-left\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <span className=\"truncate font-medium\">\r\n                            {item.label}\r\n                          </span>\r\n                          {item.isNew && (\r\n                            <span className=\"inline-flex items-center gap-1 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-700\">\r\n                              <Sparkles className=\"h-3 w-3\" />\r\n                              New\r\n                            </span>\r\n                          )}\r\n                        </div>\r\n                        {item.description && !searchQuery && (\r\n                          <div className=\"mt-0.5 text-xs text-gray-500\">\r\n                            {item.description}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"flex items-center gap-1\">\r\n                        {item.badge && (\r\n                          <span className=\"rounded-full bg-red-500 px-2 py-0.5 text-xs font-medium text-white\">\r\n                            {item.badge}\r\n                          </span>\r\n                        )}\r\n                        {item.subItems && (\r\n                          <span className=\"text-gray-400\">\r\n                            {openMenus[item.label] ? (\r\n                              <ChevronDown size={16} />\r\n                            ) : (\r\n                              <ChevronRight size={16} />\r\n                            )}\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </>\r\n                  )}\r\n                </button>\r\n\r\n                {/* Tooltip for collapsed state */}\r\n                {collapsed && (\r\n                  <div className=\"absolute left-full top-1/2 z-50 ml-2 hidden -translate-y-1/2 group-hover:block\">\r\n                    <div className=\"rounded-lg bg-gray-900 px-3 py-2 text-sm text-white shadow-lg\">\r\n                      <div className=\"font-medium\">{item.label}</div>\r\n                      {item.description && (\r\n                        <div className=\"mt-1 text-xs text-gray-300\">\r\n                          {item.description}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Sub-items */}\r\n              {!collapsed && item.subItems && openMenus[item.label] && (\r\n                <div className=\"ml-6 mt-2 space-y-1 border-l-2 border-gray-100 pl-4\">\r\n                  {item.subItems.map((sub, i) => {\r\n                    const isSubActive = pathname === sub.href;\r\n                    return (\r\n                      <Link key={i} href={sub.href} passHref legacyBehavior>\r\n                        <a\r\n                          className={clsx(\r\n                            \"flex w-full items-center rounded-lg px-3 py-2 text-left transition-all duration-150 hover:bg-gray-50\",\r\n                            {\r\n                              \"border border-blue-200 bg-blue-50 font-medium text-blue-600 shadow-sm\":\r\n                                isSubActive,\r\n                              \"text-gray-600 hover:text-gray-900\": !isSubActive,\r\n                            }\r\n                          )}\r\n                        >\r\n                          {sub.icon && (\r\n                            <span className=\"mr-3 flex-shrink-0 text-gray-400\">\r\n                              {sub.icon}\r\n                            </span>\r\n                          )}\r\n\r\n                          <div className=\"flex-1\">\r\n                            <div className=\"font-medium\">{sub.label}</div>\r\n                            {sub.description && (\r\n                              <div className=\"mt-0.5 text-xs text-gray-500\">\r\n                                {sub.description}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n\r\n                          {sub.badge && (\r\n                            <span className=\"rounded-full bg-orange-500 px-2 py-0.5 text-xs font-medium text-white\">\r\n                              {sub.badge}\r\n                            </span>\r\n                          )}\r\n                        </a>\r\n                      </Link>\r\n                    );\r\n                  })}\r\n                </div>\r\n              )}\r\n\r\n              {/* Divider line */}\r\n              {item.isDividerAfter && !collapsed && (\r\n                <div className=\"my-4 border-t border-gray-200\" />\r\n              )}\r\n            </div>\r\n          );\r\n        })}\r\n      </nav>\r\n    </aside>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAEA;;;;;;;;;AAEA,2DAA2D;AAC3D,MAAM,yBAAyB;AAE/B,0CAA0C;AAC1C,MAAM,wBAAwB,CAAC;IAC7B,MAAM,QAAQ,IAAI,YAAY,wBAAwB;QACpD,QAAQ;YAAE,WAAW;QAAY;IACnC;IACA,OAAO,aAAa,CAAC;AACvB;AAEe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,sCAAsC;IACtC,MAAM,uBAAuB;QAC3B,MAAM,oBAAoB,CAAC;QAC3B,aAAa;QACb,sBAAsB;IACxB;IAEA,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,oBAAmC;YACvC,KAAK,MAAM,QAAQ,oIAAA,CAAA,WAAQ,CAAE;gBAC3B,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,EAAE,KAAK,UACtC,GACA,KAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC;oBAEpC,IAAI,YAAY,SAAS,UAAU,CAAC,WAAW,MAAM;wBACnD,IACE,KAAK,QAAQ,CAAC,IAAI;iDAChB,CAAC,MACC,aAAa,IAAI,IAAI,IAAI,SAAS,UAAU,CAAC,IAAI,IAAI,GAAG;iDAE5D;4BACA,oBAAoB,KAAK,KAAK;4BAC9B;wBACF;oBACF;gBACF;YACF;YAEA,IAAI,mBAAmB;gBACrB;yCAAa,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,kBAAmB,EAAE;wBAAK,CAAC;;YACjE;QACA,kEAAkE;QAClE,oDAAoD;QACpD,2BAA2B;QAC3B,4CAA4C;QAC5C,IAAI;QACJ,8BAA8B;QAChC;4BAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAC;QAClB,aAAa,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM;YAAC,CAAC;IAC5D;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,KAAK,QAAQ,EAAE,QAAQ;YACzB,MAAM,aAAa,SAAS,CAAC,KAAK,KAAK,CAAC;YAExC,IAAI,YAAY;gBACd,WAAW,KAAK,KAAK;gBACrB;YACF;YAEA,WAAW,KAAK,KAAK;YAErB,MAAM,qBAAqB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,MAC7C,SAAS,UAAU,CAAC,IAAI,IAAI;YAG9B,IAAI,CAAC,oBAAoB;gBACvB,OAAO,IAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,CAAC,IAAI;YACnC;QACF,OAAO,IAAI,KAAK,IAAI,EAAE;YACpB,OAAO,IAAI,CAAC,KAAK,IAAI;QACvB;IACF;IAEA,gDAAgD;IAChD,MAAM,mBAAmB,oIAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,aAAa,OAAO;QAEzB,MAAM,QAAQ,YAAY,WAAW;QACrC,MAAM,eAAe,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;QACvD,MAAM,qBAAqB,KAAK,WAAW,EAAE,cAAc,SAAS;QACpE,MAAM,kBAAkB,KAAK,QAAQ,EAAE,KACrC,CAAC,MACC,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACjC,IAAI,WAAW,EAAE,cAAc,SAAS;QAG5C,OAAO,gBAAgB,sBAAsB;IAC/C;IAEA,qBACE,4TAAC;QACC,WAAW,CAAC,wFAAwF,EAClG,YAAY,SAAS,QACrB;;0BAGF,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBACC,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,qCAAqC;4BACnD,QAAQ;4BACR,QAAQ,CAAC;wBACX;;4BAGC,CAAC,2BACA,4TAAC,8RAAA,CAAA,UAAI;gCAAC,MAAM;0CACV,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,MAAK;0DAC/B,cAAA,4TAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAG9B,4TAAC;;8DACC,4TAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,4TAAC;oDAAI,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;0CASrD,4TAAC;gCACC,WAAU;gCACV,SAAS;0CAER,0BACC,4TAAC,iTAAA,CAAA,iBAAc;oCAAC,MAAM;;;;;yDAEtB,4TAAC,iTAAA,CAAA,iBAAc;oCAAC,MAAM;;;;;;;;;;;;;;;;;oBAM3B,CAAC,2BACA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,6RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,4TAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;4BAEX,6BACC,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,4TAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStC,4TAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM;oBAC3B,MAAM,uBAAuB,CAAC,CAAC,KAAK,IAAI,IAAI,aAAa,KAAK,IAAI;oBAClE,MAAM,sBAAsB;oBAE5B,qBACE,4TAAC;wBAAgB,WAAU;;0CAEzB,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EACZ,gGACA;4CACE,yEACE;4CACF,qCAAqC,CAAC;4CACtC,kBAAkB;wCACpB;wCAEF,OAAO,YAAY,KAAK,KAAK,GAAG;;0DAEhC,4TAAC;gDACC,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,iBAAiB;oDAAE,QAAQ,CAAC;gDAAU;0DAErD,KAAK,IAAI;;;;;;4CAGX,CAAC,2BACA;;kEACE,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAK,WAAU;kFACb,KAAK,KAAK;;;;;;oEAEZ,KAAK,KAAK,kBACT,4TAAC;wEAAK,WAAU;;0FACd,4TAAC,iSAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;;4DAKrC,KAAK,WAAW,IAAI,CAAC,6BACpB,4TAAC;gEAAI,WAAU;0EACZ,KAAK,WAAW;;;;;;;;;;;;kEAKvB,4TAAC;wDAAI,WAAU;;4DACZ,KAAK,KAAK,kBACT,4TAAC;gEAAK,WAAU;0EACb,KAAK,KAAK;;;;;;4DAGd,KAAK,QAAQ,kBACZ,4TAAC;gEAAK,WAAU;0EACb,SAAS,CAAC,KAAK,KAAK,CAAC,iBACpB,4TAAC,2SAAA,CAAA,cAAW;oEAAC,MAAM;;;;;yFAEnB,4TAAC,6SAAA,CAAA,eAAY;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;oCAUjC,2BACC,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;8DAAe,KAAK,KAAK;;;;;;gDACvC,KAAK,WAAW,kBACf,4TAAC;oDAAI,WAAU;8DACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;4BAS5B,CAAC,aAAa,KAAK,QAAQ,IAAI,SAAS,CAAC,KAAK,KAAK,CAAC,kBACnD,4TAAC;gCAAI,WAAU;0CACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;oCACvB,MAAM,cAAc,aAAa,IAAI,IAAI;oCACzC,qBACE,4TAAC,8RAAA,CAAA,UAAI;wCAAS,MAAM,IAAI,IAAI;wCAAE,QAAQ;wCAAC,cAAc;kDACnD,cAAA,4TAAC;4CACC,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EACZ,wGACA;gDACE,yEACE;gDACF,qCAAqC,CAAC;4CACxC;;gDAGD,IAAI,IAAI,kBACP,4TAAC;oDAAK,WAAU;8DACb,IAAI,IAAI;;;;;;8DAIb,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;sEAAe,IAAI,KAAK;;;;;;wDACtC,IAAI,WAAW,kBACd,4TAAC;4DAAI,WAAU;sEACZ,IAAI,WAAW;;;;;;;;;;;;gDAKrB,IAAI,KAAK,kBACR,4TAAC;oDAAK,WAAU;8DACb,IAAI,KAAK;;;;;;;;;;;;uCA5BP;;;;;gCAkCf;;;;;;4BAKH,KAAK,cAAc,IAAI,CAAC,2BACvB,4TAAC;gCAAI,WAAU;;;;;;;uBA5HT;;;;;gBAgId;;;;;;;;;;;;AAIR;GA7SwB;;QAYP,oQAAA,CAAA,YAAS;QACP,oQAAA,CAAA,cAAW;;;KAbN", "debugId": null}}, {"offset": {"line": 809, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,4TAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,sRAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,sRAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,sRAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,sRAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,sRAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,sRAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,4TAAC,sRAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,6SAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,sRAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,sRAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,sRAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,4TAAC,sRAAA,CAAA,SAA4B;kBAC3B,cAAA,4TAAC,sRAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,sRAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,4TAAC,sRAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,sRAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,4TAAC,sRAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,4TAAC,2RAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,sRAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,sRAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,4TAAC,6RAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,sRAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,4TAAC,sRAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,sRAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,sRAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,sRAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/layout/TopNavbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  BarChart3,\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronRight,\r\n  Command,\r\n  HelpCircle,\r\n  Home,\r\n  LogOut,\r\n  MessageSquare,\r\n  Moon,\r\n  Package,\r\n  Plus,\r\n  Search,\r\n  Settings,\r\n  ShoppingCart,\r\n  Sun,\r\n  User,\r\n  Users,\r\n  X,\r\n  Zap,\r\n} from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\nexport default function TopNavbar() {\r\n  const [darkMode, setDarkMode] = useState(false);\r\n  const [searchOpen, setSearchOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [notifications] = useState([\r\n    {\r\n      id: 1,\r\n      title: \"New order received\",\r\n      message: \"Order #1234 from <PERSON>\",\r\n      time: \"2 min ago\",\r\n      unread: true,\r\n    },\r\n    {\r\n      id: 2,\r\n      title: \"Low stock alert\",\r\n      message: \"T-shirt inventory is running low\",\r\n      time: \"1 hour ago\",\r\n      unread: true,\r\n    },\r\n    {\r\n      id: 3,\r\n      title: \"Payment processed\",\r\n      message: \"Payment of $299.99 confirmed\",\r\n      time: \"3 hours ago\",\r\n      unread: false,\r\n    },\r\n  ]);\r\n\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n\r\n  const toggleDarkMode = () => {\r\n    const html = document.documentElement;\r\n    html.classList.toggle(\"dark\");\r\n    setDarkMode(html.classList.contains(\"dark\"));\r\n  };\r\n\r\n  // Generate breadcrumbs from pathname\r\n  const generateBreadcrumbs = () => {\r\n    const paths = pathname.split(\"/\").filter(Boolean);\r\n    const breadcrumbs = [];\r\n\r\n    // Handle root/admin paths\r\n    if (\r\n      pathname === \"/\" ||\r\n      pathname === \"/admin\" ||\r\n      pathname === \"/admin/dashboard\"\r\n    ) {\r\n      return [{ label: \"Dashboard\", href: \"/admin/dashboard\", icon: Home }];\r\n    }\r\n\r\n    // Skip admin prefix and build meaningful breadcrumbs\r\n    const adminPaths = paths.slice(1); // Remove 'admin' from paths\r\n\r\n    // Always start with Dashboard for non-dashboard pages\r\n    breadcrumbs.push({\r\n      label: \"Dashboard\",\r\n      href: \"/admin/dashboard\",\r\n      icon: Home,\r\n    });\r\n\r\n    let currentPath = \"/admin\";\r\n    adminPaths.forEach((path, index) => {\r\n      currentPath += `/${path}`;\r\n\r\n      // Convert path to readable label\r\n      let label = path.charAt(0).toUpperCase() + path.slice(1);\r\n      if (label === \"List\") label = \"All\";\r\n      if (label === \"Add\") label = \"New\";\r\n\r\n      // Add appropriate icons\r\n      let icon = null;\r\n      if (path === \"products\") icon = Package;\r\n      if (path === \"orders\") icon = ShoppingCart;\r\n      if (path === \"customers\") icon = Users;\r\n      if (path === \"analytics\") icon = BarChart3;\r\n\r\n      breadcrumbs.push({ label, href: currentPath, icon });\r\n    });\r\n\r\n    return breadcrumbs;\r\n  };\r\n\r\n  const breadcrumbs = generateBreadcrumbs();\r\n  const unreadCount = notifications.filter((n) => n.unread).length;\r\n\r\n  // Quick actions based on current page\r\n  const getQuickActions = () => {\r\n    if (pathname.includes(\"/products\")) {\r\n      return [\r\n        { label: \"Add Product\", href: \"/admin/products/add\", icon: Plus },\r\n        {\r\n          label: \"Categories\",\r\n          href: \"/admin/products/categories\",\r\n          icon: Package,\r\n        },\r\n      ];\r\n    }\r\n    if (pathname.includes(\"/orders\")) {\r\n      return [\r\n        { label: \"New Order\", href: \"/admin/orders/new\", icon: Plus },\r\n        {\r\n          label: \"Export Orders\",\r\n          action: () => console.log(\"Export\"),\r\n          icon: BarChart3,\r\n        },\r\n      ];\r\n    }\r\n    if (pathname.includes(\"/customers\")) {\r\n      return [\r\n        { label: \"Add Customer\", href: \"/admin/customers/add\", icon: Plus },\r\n        {\r\n          label: \"Export Customers\",\r\n          action: () => console.log(\"Export\"),\r\n          icon: BarChart3,\r\n        },\r\n      ];\r\n    }\r\n    return [\r\n      { label: \"Add Product\", href: \"/admin/products/add\", icon: Plus },\r\n      { label: \"View Orders\", href: \"/admin/orders/list\", icon: ShoppingCart },\r\n    ];\r\n  };\r\n\r\n  const quickActions = getQuickActions();\r\n\r\n  // Close search overlay when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const target = event.target as Element;\r\n      if (!target.closest(\"header\")) {\r\n        setSearchOpen(false);\r\n      }\r\n    };\r\n\r\n    if (searchOpen) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n      return () =>\r\n        document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n  }, [searchOpen]);\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-10 border-b bg-white shadow-sm\">\r\n      {/* Main Navigation Bar */}\r\n      <div className=\"flex w-full items-center justify-between px-6 py-3\">\r\n        {/* Left: Breadcrumbs */}\r\n        <div className=\"flex flex-1 items-center gap-4\">\r\n          <nav className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            {breadcrumbs.map((crumb, index) => (\r\n              <div key={`${crumb.href}-${index}`} className=\"flex items-center\">\r\n                {index > 0 && <ChevronRight className=\"mx-2 h-4 w-4\" />}\r\n                <Link\r\n                  href={crumb.href}\r\n                  className={`flex items-center gap-1 rounded-md px-2 py-1 hover:bg-gray-100 hover:text-gray-900 ${\r\n                    index === breadcrumbs.length - 1\r\n                      ? \"font-medium text-gray-900\"\r\n                      : \"text-gray-500\"\r\n                  }`}\r\n                >\r\n                  {crumb.icon && <crumb.icon className=\"h-4 w-4\" />}\r\n                  {crumb.label}\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </nav>\r\n        </div>\r\n\r\n        {/* Center: Search */}\r\n        <div className=\"flex flex-1 justify-center\">\r\n          <div className=\"relative w-full max-w-md\">\r\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Search products, orders, customers...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              className=\"w-full rounded-lg border border-gray-200 bg-gray-50 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:bg-white focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n              onFocus={() => setSearchOpen(true)}\r\n            />\r\n            {searchQuery && (\r\n              <button\r\n                onClick={() => setSearchQuery(\"\")}\r\n                className=\"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600\"\r\n              >\r\n                <X className=\"h-4 w-4\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right: Actions & User */}\r\n        <div className=\"flex flex-1 items-center justify-end gap-2\">\r\n          {/* Quick Actions */}\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"outline\" size=\"sm\" className=\"hidden sm:flex\">\r\n                <Zap className=\"mr-2 h-4 w-4\" />\r\n                Quick Actions\r\n                <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-48\">\r\n              {quickActions.map((action, index) => (\r\n                <DropdownMenuItem key={index} asChild={!!action.href}>\r\n                  {action.href ? (\r\n                    <Link href={action.href} className=\"flex items-center\">\r\n                      <action.icon className=\"mr-2 h-4 w-4\" />\r\n                      {action.label}\r\n                    </Link>\r\n                  ) : (\r\n                    <button\r\n                      onClick={action.action}\r\n                      className=\"flex w-full items-center\"\r\n                    >\r\n                      <action.icon className=\"mr-2 h-4 w-4\" />\r\n                      {action.label}\r\n                    </button>\r\n                  )}\r\n                </DropdownMenuItem>\r\n              ))}\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n\r\n          {/* Separator */}\r\n          <div className=\"hidden h-6 w-px bg-gray-200 sm:block\" />\r\n\r\n          {/* Help & Support */}\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"h-9 w-9 p-0\"\r\n                title=\"Help & Support\"\r\n              >\r\n                <HelpCircle className=\"h-4 w-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56\">\r\n              <div className=\"p-3\">\r\n                <h3 className=\"text-sm font-semibold\">Help & Support</h3>\r\n                <p className=\"mt-1 text-xs text-gray-500\">\r\n                  Get help and learn more\r\n                </p>\r\n              </div>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem>\r\n                <HelpCircle className=\"mr-2 h-4 w-4\" />\r\n                Documentation\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem>\r\n                <MessageSquare className=\"mr-2 h-4 w-4\" />\r\n                Contact Support\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem>\r\n                <Command className=\"mr-2 h-4 w-4\" />\r\n                Keyboard Shortcuts\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem>\r\n                <div className=\"flex w-full items-center justify-between\">\r\n                  <span className=\"text-sm\">What's New</span>\r\n                  <Badge className=\"bg-green-100 text-xs text-green-700\">\r\n                    New\r\n                  </Badge>\r\n                </div>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem className=\"text-xs text-gray-500\">\r\n                Version 2.1.0\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n\r\n          {/* Dark mode toggle */}\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={toggleDarkMode}\r\n            className=\"h-9 w-9 p-0\"\r\n            title=\"Toggle theme\"\r\n          >\r\n            {darkMode ? (\r\n              <Sun className=\"h-4 w-4\" />\r\n            ) : (\r\n              <Moon className=\"h-4 w-4\" />\r\n            )}\r\n          </Button>\r\n\r\n          {/* Notifications */}\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"relative flex h-9 w-9 p-0\"\r\n              >\r\n                <Bell className=\"h-4 w-4\" />\r\n                {unreadCount > 0 && (\r\n                  <Badge className=\"absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 p-0 text-xs font-medium text-white\">\r\n                    {unreadCount}\r\n                  </Badge>\r\n                )}\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-80\">\r\n              <div className=\"flex items-center justify-between p-3\">\r\n                <h3 className=\"font-semibold\">Notifications</h3>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"h-auto p-0 text-xs\"\r\n                >\r\n                  Mark all read\r\n                </Button>\r\n              </div>\r\n              <DropdownMenuSeparator />\r\n              <div className=\"max-h-64 overflow-y-auto\">\r\n                {notifications.map((notification) => (\r\n                  <DropdownMenuItem\r\n                    key={notification.id}\r\n                    className=\"flex-col items-start p-3\"\r\n                  >\r\n                    <div className=\"flex w-full items-start justify-between\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <p className=\"text-sm font-medium\">\r\n                            {notification.title}\r\n                          </p>\r\n                          {notification.unread && (\r\n                            <div className=\"h-2 w-2 rounded-full bg-blue-500\" />\r\n                          )}\r\n                        </div>\r\n                        <p className=\"mt-1 text-xs text-gray-500\">\r\n                          {notification.message}\r\n                        </p>\r\n                        <p className=\"mt-1 text-xs text-gray-400\">\r\n                          {notification.time}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </DropdownMenuItem>\r\n                ))}\r\n              </div>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem className=\"justify-center p-3\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"h-auto p-0 text-xs\"\r\n                >\r\n                  View all notifications\r\n                </Button>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n\r\n          {/* User Menu */}\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-9 w-9 rounded-full p-0\"\r\n              >\r\n                <Image\r\n                  src=\"https://randomuser.me/api/portraits/men/75.jpg\"\r\n                  alt=\"User\"\r\n                  width={36}\r\n                  height={36}\r\n                  className=\"rounded-full object-cover\"\r\n                />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-64\">\r\n              {/* User Profile Header */}\r\n              <div className=\"flex items-center gap-3 bg-gray-50 p-4\">\r\n                <Image\r\n                  src=\"https://randomuser.me/api/portraits/men/75.jpg\"\r\n                  alt=\"User\"\r\n                  width={48}\r\n                  height={48}\r\n                  className=\"rounded-full object-cover shadow-sm ring-2 ring-white\"\r\n                />\r\n                <div className=\"flex-1\">\r\n                  <p className=\"font-semibold text-gray-900\">Admin User</p>\r\n                  <p className=\"text-sm text-gray-500\"><EMAIL></p>\r\n                  <p className=\"text-xs text-gray-400\">Administrator</p>\r\n                </div>\r\n              </div>\r\n              <DropdownMenuSeparator />\r\n\r\n              {/* Account Management */}\r\n              <div className=\"p-1\">\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/admin/profile\" className=\"flex items-center\">\r\n                    <User className=\"mr-2 h-4 w-4\" />\r\n                    <div>\r\n                      <div className=\"font-medium\">Profile</div>\r\n                      <div className=\"text-xs text-gray-500\">\r\n                        Manage your account\r\n                      </div>\r\n                    </div>\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <div>\r\n                    <div className=\"font-medium\">Account Settings</div>\r\n                    <div className=\"text-xs text-gray-500\">\r\n                      Privacy and preferences\r\n                    </div>\r\n                  </div>\r\n                </DropdownMenuItem>\r\n              </div>\r\n\r\n              <DropdownMenuSeparator />\r\n\r\n              {/* Quick Stats */}\r\n              <div className=\"bg-blue-50 p-3\">\r\n                <div className=\"mb-2 text-xs font-medium text-blue-900\">\r\n                  Quick Stats\r\n                </div>\r\n                <div className=\"grid grid-cols-2 gap-3 text-xs\">\r\n                  <div>\r\n                    <div className=\"font-medium text-gray-900\">24</div>\r\n                    <div className=\"text-gray-500\">Orders Today</div>\r\n                  </div>\r\n                  <div>\r\n                    <div className=\"font-medium text-gray-900\">$1,234</div>\r\n                    <div className=\"text-gray-500\">Revenue</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <DropdownMenuSeparator />\r\n\r\n              {/* Sign Out */}\r\n              <div className=\"p-1\">\r\n                <DropdownMenuItem className=\"text-red-600 focus:bg-red-50 focus:text-red-700\">\r\n                  <LogOut className=\"mr-2 h-4 w-4\" />\r\n                  <div>\r\n                    <div className=\"font-medium\">Sign out</div>\r\n                    <div className=\"text-xs opacity-75\">End your session</div>\r\n                  </div>\r\n                </DropdownMenuItem>\r\n              </div>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Search Results Overlay */}\r\n      {searchOpen && searchQuery && (\r\n        <div className=\"absolute left-0 right-0 top-full z-50 border-b bg-white shadow-lg\">\r\n          <div className=\"p-4\">\r\n            <div className=\"mb-3 text-sm text-gray-500\">\r\n              Search results for \"{searchQuery}\"\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center gap-3 rounded-lg p-2 hover:bg-gray-50\">\r\n                <Package className=\"h-4 w-4 text-gray-400\" />\r\n                <div>\r\n                  <p className=\"text-sm font-medium\">T-shirt for Men</p>\r\n                  <p className=\"text-xs text-gray-500\">Product • $90.00</p>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center gap-3 rounded-lg p-2 hover:bg-gray-50\">\r\n                <ShoppingCart className=\"h-4 w-4 text-gray-400\" />\r\n                <div>\r\n                  <p className=\"text-sm font-medium\">Order #1234</p>\r\n                  <p className=\"text-xs text-gray-500\">Order • $299.99</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"mt-3 border-t pt-3\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"w-full justify-start\"\r\n              >\r\n                <Search className=\"mr-2 h-4 w-4\" />\r\n                Search all results for \"{searchQuery}\"\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AACA;AACA;AAEA;AACA;AACA;;;AAhCA;;;;;;;;;AAwCe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;QACV;KACD;IAED,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,iBAAiB;QACrB,MAAM,OAAO,SAAS,eAAe;QACrC,KAAK,SAAS,CAAC,MAAM,CAAC;QACtB,YAAY,KAAK,SAAS,CAAC,QAAQ,CAAC;IACtC;IAEA,qCAAqC;IACrC,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACzC,MAAM,cAAc,EAAE;QAEtB,0BAA0B;QAC1B,IACE,aAAa,OACb,aAAa,YACb,aAAa,oBACb;YACA,OAAO;gBAAC;oBAAE,OAAO;oBAAa,MAAM;oBAAoB,MAAM,0RAAA,CAAA,OAAI;gBAAC;aAAE;QACvE;QAEA,qDAAqD;QACrD,MAAM,aAAa,MAAM,KAAK,CAAC,IAAI,4BAA4B;QAE/D,sDAAsD;QACtD,YAAY,IAAI,CAAC;YACf,OAAO;YACP,MAAM;YACN,MAAM,0RAAA,CAAA,OAAI;QACZ;QAEA,IAAI,cAAc;QAClB,WAAW,OAAO,CAAC,CAAC,MAAM;YACxB,eAAe,CAAC,CAAC,EAAE,MAAM;YAEzB,iCAAiC;YACjC,IAAI,QAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;YACtD,IAAI,UAAU,QAAQ,QAAQ;YAC9B,IAAI,UAAU,OAAO,QAAQ;YAE7B,wBAAwB;YACxB,IAAI,OAAO;YACX,IAAI,SAAS,YAAY,OAAO,+RAAA,CAAA,UAAO;YACvC,IAAI,SAAS,UAAU,OAAO,6SAAA,CAAA,eAAY;YAC1C,IAAI,SAAS,aAAa,OAAO,2RAAA,CAAA,QAAK;YACtC,IAAI,SAAS,aAAa,OAAO,ySAAA,CAAA,YAAS;YAE1C,YAAY,IAAI,CAAC;gBAAE;gBAAO,MAAM;gBAAa;YAAK;QACpD;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IACpB,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,EAAE,MAAM;IAEhE,sCAAsC;IACtC,MAAM,kBAAkB;QACtB,IAAI,SAAS,QAAQ,CAAC,cAAc;YAClC,OAAO;gBACL;oBAAE,OAAO;oBAAe,MAAM;oBAAuB,MAAM,yRAAA,CAAA,OAAI;gBAAC;gBAChE;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,+RAAA,CAAA,UAAO;gBACf;aACD;QACH;QACA,IAAI,SAAS,QAAQ,CAAC,YAAY;YAChC,OAAO;gBACL;oBAAE,OAAO;oBAAa,MAAM;oBAAqB,MAAM,yRAAA,CAAA,OAAI;gBAAC;gBAC5D;oBACE,OAAO;oBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;oBAC1B,MAAM,ySAAA,CAAA,YAAS;gBACjB;aACD;QACH;QACA,IAAI,SAAS,QAAQ,CAAC,eAAe;YACnC,OAAO;gBACL;oBAAE,OAAO;oBAAgB,MAAM;oBAAwB,MAAM,yRAAA,CAAA,OAAI;gBAAC;gBAClE;oBACE,OAAO;oBACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;oBAC1B,MAAM,ySAAA,CAAA,YAAS;gBACjB;aACD;QACH;QACA,OAAO;YACL;gBAAE,OAAO;gBAAe,MAAM;gBAAuB,MAAM,yRAAA,CAAA,OAAI;YAAC;YAChE;gBAAE,OAAO;gBAAe,MAAM;gBAAsB,MAAM,6SAAA,CAAA,eAAY;YAAC;SACxE;IACH;IAEA,MAAM,eAAe;IAErB,6CAA6C;IAC7C,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;0DAAqB,CAAC;oBAC1B,MAAM,SAAS,MAAM,MAAM;oBAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,WAAW;wBAC7B,cAAc;oBAChB;gBACF;;YAEA,IAAI,YAAY;gBACd,SAAS,gBAAgB,CAAC,aAAa;gBACvC;2CAAO,IACL,SAAS,mBAAmB,CAAC,aAAa;;YAC9C;QACF;8BAAG;QAAC;KAAW;IAEf,qBACE,4TAAC;QAAO,WAAU;;0BAEhB,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,4TAAC;oCAAmC,WAAU;;wCAC3C,QAAQ,mBAAK,4TAAC,6SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACtC,4TAAC,8RAAA,CAAA,UAAI;4CACH,MAAM,MAAM,IAAI;4CAChB,WAAW,CAAC,mFAAmF,EAC7F,UAAU,YAAY,MAAM,GAAG,IAC3B,8BACA,iBACJ;;gDAED,MAAM,IAAI,kBAAI,4TAAC,MAAM,IAAI;oDAAC,WAAU;;;;;;gDACpC,MAAM,KAAK;;;;;;;;mCAXN,GAAG,MAAM,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;kCAmBxC,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,6RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,4TAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;oCACV,SAAS,IAAM,cAAc;;;;;;gCAE9B,6BACC,4TAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,4TAAC,mRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOrB,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC,wIAAA,CAAA,eAAY;;kDACX,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,4TAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;;8DAC5C,4TAAC,uRAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;8DAEhC,4TAAC,2SAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG3B,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;kDACxC,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,4TAAC,wIAAA,CAAA,mBAAgB;gDAAa,SAAS,CAAC,CAAC,OAAO,IAAI;0DACjD,OAAO,IAAI,iBACV,4TAAC,8RAAA,CAAA,UAAI;oDAAC,MAAM,OAAO,IAAI;oDAAE,WAAU;;sEACjC,4TAAC,OAAO,IAAI;4DAAC,WAAU;;;;;;wDACtB,OAAO,KAAK;;;;;;yEAGf,4TAAC;oDACC,SAAS,OAAO,MAAM;oDACtB,WAAU;;sEAEV,4TAAC,OAAO,IAAI;4DAAC,WAAU;;;;;;wDACtB,OAAO,KAAK;;;;;;;+CAZI;;;;;;;;;;;;;;;;0CAqB7B,4TAAC;gCAAI,WAAU;;;;;;0CAGf,4TAAC,wIAAA,CAAA,eAAY;;kDACX,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,OAAM;sDAEN,cAAA,4TAAC,ySAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG1B,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,4TAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAI5C,4TAAC,wIAAA,CAAA,wBAAqB;;;;;0DACtB,4TAAC,wIAAA,CAAA,mBAAgB;;kEACf,4TAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGzC,4TAAC,wIAAA,CAAA,mBAAgB;;kEACf,4TAAC,+SAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG5C,4TAAC,wIAAA,CAAA,mBAAgB;;kEACf,4TAAC,+RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGtC,4TAAC,wIAAA,CAAA,wBAAqB;;;;;0DACtB,4TAAC,wIAAA,CAAA,mBAAgB;0DACf,cAAA,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,4TAAC,6HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsC;;;;;;;;;;;;;;;;;0DAK3D,4TAAC,wIAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAOxD,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEL,yBACC,4TAAC,uRAAA,CAAA,MAAG;oCAAC,WAAU;;;;;yDAEf,4TAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAKpB,4TAAC,wIAAA,CAAA,eAAY;;kDACX,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,4TAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,cAAc,mBACb,4TAAC,6HAAA,CAAA,QAAK;oDAAC,WAAU;8DACd;;;;;;;;;;;;;;;;;kDAKT,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,4TAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;0DAIH,4TAAC,wIAAA,CAAA,wBAAqB;;;;;0DACtB,4TAAC;gDAAI,WAAU;0DACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,4TAAC,wIAAA,CAAA,mBAAgB;wDAEf,WAAU;kEAEV,cAAA,4TAAC;4DAAI,WAAU;sEACb,cAAA,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;0FACb,4TAAC;gFAAE,WAAU;0FACV,aAAa,KAAK;;;;;;4EAEpB,aAAa,MAAM,kBAClB,4TAAC;gFAAI,WAAU;;;;;;;;;;;;kFAGnB,4TAAC;wEAAE,WAAU;kFACV,aAAa,OAAO;;;;;;kFAEvB,4TAAC;wEAAE,WAAU;kFACV,aAAa,IAAI;;;;;;;;;;;;;;;;;uDAjBnB,aAAa,EAAE;;;;;;;;;;0DAwB1B,4TAAC,wIAAA,CAAA,wBAAqB;;;;;0DACtB,4TAAC,wIAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAC1B,cAAA,4TAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAQP,4TAAC,wIAAA,CAAA,eAAY;;kDACX,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;sDAEV,cAAA,4TAAC,+PAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAIhB,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DAEzC,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,+PAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEZ,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAE,WAAU;0EAA8B;;;;;;0EAC3C,4TAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,4TAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,4TAAC,wIAAA,CAAA,wBAAqB;;;;;0DAGtB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,wIAAA,CAAA,mBAAgB;wDAAC,OAAO;kEACvB,cAAA,4TAAC,8RAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EACpC,4TAAC,yRAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,4TAAC;;sFACC,4TAAC;4EAAI,WAAU;sFAAc;;;;;;sFAC7B,4TAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;kEAM7C,4TAAC,wIAAA,CAAA,mBAAgB;;0EACf,4TAAC,iSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,4TAAC;;kFACC,4TAAC;wEAAI,WAAU;kFAAc;;;;;;kFAC7B,4TAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAO7C,4TAAC,wIAAA,CAAA,wBAAqB;;;;;0DAGtB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;kEAAyC;;;;;;kEAGxD,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;;kFACC,4TAAC;wEAAI,WAAU;kFAA4B;;;;;;kFAC3C,4TAAC;wEAAI,WAAU;kFAAgB;;;;;;;;;;;;0EAEjC,4TAAC;;kFACC,4TAAC;wEAAI,WAAU;kFAA4B;;;;;;kFAC3C,4TAAC;wEAAI,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;;;;;;;0DAKrC,4TAAC,wIAAA,CAAA,wBAAqB;;;;;0DAGtB,4TAAC;gDAAI,WAAU;0DACb,cAAA,4TAAC,wIAAA,CAAA,mBAAgB;oDAAC,WAAU;;sEAC1B,4TAAC,iSAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,4TAAC;;8EACC,4TAAC;oEAAI,WAAU;8EAAc;;;;;;8EAC7B,4TAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjD,cAAc,6BACb,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;gCAA6B;gCACrB;gCAAY;;;;;;;sCAEnC,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,+RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAsB;;;;;;8DACnC,4TAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,6SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAsB;;;;;;8DACnC,4TAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;sCAI3C,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,4TAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;oCACV;oCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAzewB;;QA4BL,oQAAA,CAAA,cAAW;QACb,oQAAA,CAAA,YAAS;;;KA7BF", "debugId": null}}, {"offset": {"line": 2451, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/layout/Template.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\n\r\nimport Sidebar from \"@/components/layout/Sidebar\";\r\nimport TopNavbar from \"@/components/layout/TopNavbar\";\r\n\r\n// Pages that should not use the admin layout\r\nconst authPages = [\"/login\", \"/help\", \"/forgot-password\"];\r\n\r\n// Event name for sidebar collapse state changes (must match the one in Sidebar.tsx)\r\nconst SIDEBAR_COLLAPSE_EVENT = \"sidebarCollapseChange\";\r\n\r\nexport default function AdminTemplate({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\r\n  const pathname = usePathname();\r\n  const isAuthPage = authPages.some((page) => pathname?.includes(page));\r\n\r\n  // Listen for sidebar collapse events\r\n  useEffect(() => {\r\n    const handleSidebarCollapseChange = (event: any) => {\r\n      setSidebarCollapsed(event.detail.collapsed);\r\n    };\r\n\r\n    window.addEventListener(\r\n      SIDEBAR_COLLAPSE_EVENT,\r\n      handleSidebarCollapseChange\r\n    );\r\n\r\n    return () => {\r\n      window.removeEventListener(\r\n        SIDEBAR_COLLAPSE_EVENT,\r\n        handleSidebarCollapseChange\r\n      );\r\n    };\r\n  }, []);\r\n\r\n  if (isAuthPage) {\r\n    return <>{children}</>;\r\n  }\r\n\r\n  return (\r\n    <main className=\"flex min-h-screen w-full bg-gray-50\">\r\n      <Sidebar />\r\n\r\n      {/* Add margin to account for the fixed sidebar - adjust based on collapsed state */}\r\n      <div\r\n        className={`flex flex-1 flex-col transition-all duration-200 ${\r\n          sidebarCollapsed ? \"ml-20\" : \"ml-72\"\r\n        }`}\r\n      >\r\n        <TopNavbar />\r\n\r\n        <div className=\"container mx-auto max-w-screen-2xl flex-1 overflow-y-auto p-6 pt-4 dark:bg-gray-800\">\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AACA;;;AAPA;;;;;AASA,6CAA6C;AAC7C,MAAM,YAAY;IAAC;IAAU;IAAS;CAAmB;AAEzD,oFAAoF;AACpF,MAAM,yBAAyB;AAEhB,SAAS,cAAc,EACpC,QAAQ,EAGT;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,aAAa,UAAU,IAAI,CAAC,CAAC,OAAS,UAAU,SAAS;IAE/D,qCAAqC;IACrC,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;uEAA8B,CAAC;oBACnC,oBAAoB,MAAM,MAAM,CAAC,SAAS;gBAC5C;;YAEA,OAAO,gBAAgB,CACrB,wBACA;YAGF;2CAAO;oBACL,OAAO,mBAAmB,CACxB,wBACA;gBAEJ;;QACF;kCAAG,EAAE;IAEL,IAAI,YAAY;QACd,qBAAO;sBAAG;;IACZ;IAEA,qBACE,4TAAC;QAAK,WAAU;;0BACd,4TAAC,mIAAA,CAAA,UAAO;;;;;0BAGR,4TAAC;gBACC,WAAW,CAAC,iDAAiD,EAC3D,mBAAmB,UAAU,SAC7B;;kCAEF,4TAAC,qIAAA,CAAA,UAAS;;;;;kCAEV,4TAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX;GAlDwB;;QAML,oQAAA,CAAA,cAAW;;;KANN", "debugId": null}}]}