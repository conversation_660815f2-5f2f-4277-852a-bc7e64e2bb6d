"use client";

import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useRouter } from "next/navigation";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { MaterialManagerEnhanced } from "@/components/pages/management/MaterialManagerEnhanced";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Material } from "@/lib/api/materials";

export default function MaterialsPage() {
  const router = useRouter();
  const [materials, setMaterials] = useState<Material[]>([]);

  const handleMaterialsChange = (updatedMaterials: Material[]) => {
    setMaterials(updatedMaterials);
  };

  return (
    <>
      <PageHeaderWrapper
        title="Material Management"
        description="Define and manage materials used in your products for better specifications"
      >
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/admin/catalog")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Catalog
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Hammer className="h-5 w-5 text-purple-600" />
              Material Management
            </CardTitle>
            <p className="text-sm text-gray-600">
              Define and manage materials used in your products for better specifications
            </p>
          </CardHeader>
          <CardContent>
            <MaterialManagerEnhanced
              initialMaterials={materials}
              onMaterialsChange={handleMaterialsChange}
            />
          </CardContent>
        </Card>
      </div>
    </>
  );
}
