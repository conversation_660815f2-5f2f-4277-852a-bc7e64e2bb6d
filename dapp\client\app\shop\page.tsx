export default function ShopHomepage() {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Welcome to Your Store
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Customer-facing e-commerce experience
        </p>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            🚧 E-shop Under Development
          </h2>
          <p className="text-blue-700">
            This is where your customer-facing store will be built. 
            It will have a completely different layout and functionality from the admin panel.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white border rounded-lg p-6">
          <h3 className="font-semibold mb-2">Product Catalog</h3>
          <p className="text-gray-600 text-sm">
            Browse and search products with filters, categories, and detailed views.
          </p>
        </div>
        
        <div className="bg-white border rounded-lg p-6">
          <h3 className="font-semibold mb-2">Shopping Cart</h3>
          <p className="text-gray-600 text-sm">
            Add products to cart, manage quantities, and proceed to checkout.
          </p>
        </div>
        
        <div className="bg-white border rounded-lg p-6">
          <h3 className="font-semibold mb-2">User Account</h3>
          <p className="text-gray-600 text-sm">
            Customer registration, login, order history, and profile management.
          </p>
        </div>
      </div>

      <div className="text-center">
        <a 
          href="/admin/dashboard" 
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Go to Admin Panel →
        </a>
      </div>
    </div>
  );
}
