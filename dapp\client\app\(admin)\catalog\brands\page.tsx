"use client";

import React, { useState } from "react";
import { ArrowLeft, Tag } from "lucide-react";
import { useRouter } from "next/navigation";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { BrandManagerEnhanced } from "@/components/pages/brands/BrandManagerEnhanced";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Brand } from "@/lib/api/brands";

export default function BrandsPage() {
  const router = useRouter();
  const [brands, setBrands] = useState<Brand[]>([]);

  const handleBrandsChange = (updatedBrands: Brand[]) => {
    setBrands(updatedBrands);
  };

  return (
    <>
      <PageHeaderWrapper
        title="Brand Management"
        description="Create, organize, and manage product brands for your catalog"
      >
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/admin/catalog")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Catalog
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5 text-green-600" />
              Brand Management
            </CardTitle>
            <p className="text-sm text-gray-600">
              Create, organize, and manage product brands for your catalog
            </p>
          </CardHeader>
          <CardContent>
            <BrandManagerEnhanced
              initialBrands={brands}
              onBrandsChange={handleBrandsChange}
            />
          </CardContent>
        </Card>
      </div>
    </>
  );
}
