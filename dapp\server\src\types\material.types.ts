import { Document } from "mongoose";

/**
 * Material properties interface
 */
export interface MaterialProperties {
  durability: "low" | "medium" | "high";
  waterResistant: boolean;
  recyclable: boolean;
  weight: "light" | "medium" | "heavy";
}

/**
 * Material interface matching the frontend Material type
 */
export interface IMaterial {
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color?: string;
  properties: MaterialProperties;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
}

/**
 * Material document interface for MongoDB
 */
export interface MaterialDocument extends IMaterial, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * DTO for creating a new material
 */
export interface CreateMaterialDto {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  properties?: Partial<MaterialProperties>;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * DTO for updating a material
 */
export interface UpdateMaterialDto {
  name?: string;
  description?: string;
  slug?: string;
  icon?: string;
  color?: string;
  properties?: Partial<MaterialProperties>;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * Material filters for querying
 */
export interface MaterialFilters {
  isActive?: boolean;
  search?: string;
  durability?: "low" | "medium" | "high";
  waterResistant?: boolean;
  recyclable?: boolean;
  weight?: "light" | "medium" | "heavy";
}
