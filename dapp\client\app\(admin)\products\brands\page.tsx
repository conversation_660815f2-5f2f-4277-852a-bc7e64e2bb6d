"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";

import { BrandManagement } from "@/components/pages/brands/BrandManagement";
import { Brand } from "@/lib/api/brands";

export default function BrandsPage() {
  const router = useRouter();

  const handleCreateBrand = () => {
    // TODO: Open create brand modal/form
    console.log("Create brand clicked");
  };

  const handleEditBrand = (brand: Brand) => {
    // TODO: Open edit brand modal/form
    console.log("Edit brand clicked:", brand);
  };

  const handleViewProducts = (brand: Brand) => {
    // Navigate to products page filtered by this brand
    router.push(`/admin/products/list?brand=${encodeURIComponent(brand.name)}`);
  };

  return (
    <div className="container mx-auto py-6">
      <BrandManagement
        onCreateBrand={handleCreateBrand}
        onEditBrand={handleEditBrand}
        onViewProducts={handleViewProducts}
      />
    </div>
  );
}
