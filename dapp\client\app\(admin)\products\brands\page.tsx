"use client";

import React, { useState } from "react";

import { BrandManagerEnhanced } from "@/components/pages/brands/BrandManagerEnhanced";
import { Brand } from "@/lib/api/brands";

export default function BrandsPage() {
  const [brands, setBrands] = useState<Brand[]>([]);

  const handleBrandsChange = (updatedBrands: Brand[]) => {
    setBrands(updatedBrands);
  };

  return (
    <div className="container mx-auto py-6">
      <BrandManagerEnhanced
        initialBrands={brands}
        onBrandsChange={handleBrandsChange}
      />
    </div>
  );
}
