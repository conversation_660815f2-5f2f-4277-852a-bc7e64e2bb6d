"use client";

import React, { useState } from "react";

import { Info, Ruler, Tag, Weight } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Product } from "@/types/product";

type ProductSectionProps = {
  product: Product;
  isEditing: boolean;
  onProductUpdate: (product: Product) => void;
};

export const ProductDetailsSection = ({
  product,
  isEditing,
  onProductUpdate,
}: ProductSectionProps) => {
  const [editedProduct, setEditedProduct] = useState(product);
  const [tags, setTags] = useState(product.tags || []);

  const handleInputChange = (field: string, value: string) => {
    setEditedProduct((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleTagRemove = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const handleTagAdd = (newTag: string) => {
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag]);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          Additional Details
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Product Tags */}
        <div>
          <Label>Product Tags</Label>
          {isEditing ? (
            <div className="mt-1 space-y-2">
              <div className="flex flex-wrap gap-1">
                {tags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="cursor-pointer hover:bg-red-100"
                    onClick={() => handleTagRemove(tag)}
                  >
                    {tag} ×
                  </Badge>
                ))}
              </div>
              <Input
                placeholder="Add a tag and press Enter"
                onKeyPress={(e) => {
                  if (e.key === "Enter") {
                    handleTagAdd(e.currentTarget.value);
                    e.currentTarget.value = "";
                  }
                }}
              />
            </div>
          ) : (
            <div className="mt-1 flex flex-wrap gap-1">
              {tags.map((tag, index) => (
                <Badge key={index} variant="secondary">
                  <Tag className="mr-1 h-3 w-3" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Dimensions */}
        <div>
          <Label>Dimensions</Label>
          {isEditing ? (
            <div className="mt-1 grid grid-cols-3 gap-2">
              <Input placeholder="Length" />
              <Input placeholder="Width" />
              <Input placeholder="Height" />
            </div>
          ) : (
            <div className="mt-1 flex items-center gap-1 text-gray-600">
              <Ruler className="h-4 w-4" />
              <span>
                {product.dimensions
                  ? `${product.dimensions.width} × ${product.dimensions.height} × ${product.dimensions.depth} ${product.dimensions.unit}`
                  : "Not specified"}
              </span>
            </div>
          )}
        </div>

        {/* Weight */}
        <div>
          <Label htmlFor="weight">Weight</Label>
          {isEditing ? (
            <Input id="weight" placeholder="Weight in kg" className="mt-1" />
          ) : (
            <div className="mt-1 flex items-center gap-1 text-gray-600">
              <Weight className="h-4 w-4" />
              <span>
                {product.weight
                  ? `${product.weight.value} ${product.weight.unit}`
                  : "Not specified"}
              </span>
            </div>
          )}
        </div>

        {/* Material */}
        <div>
          <Label htmlFor="material">Material</Label>
          {isEditing ? (
            <Input
              id="material"
              placeholder="e.g. Cotton, Polyester"
              className="mt-1"
            />
          ) : (
            <p className="mt-1 text-gray-600">
              {product.material || "Not specified"}
            </p>
          )}
        </div>

        {/* Color */}
        <div>
          <Label htmlFor="color">Color</Label>
          {isEditing ? (
            <Input id="color" placeholder="e.g. Blue, Red" className="mt-1" />
          ) : (
            <p className="mt-1 text-gray-600">
              {product.color || "Not specified"}
            </p>
          )}
        </div>

        {/* Care Instructions */}
        {!isEditing && (
          <div>
            <Label>Care Instructions</Label>
            <div className="mt-1 space-y-1 text-sm text-gray-600">
              <p>• Machine wash cold</p>
              <p>• Do not bleach</p>
              <p>• Tumble dry low</p>
              <p>• Iron on low heat</p>
            </div>
          </div>
        )}

        {/* Additional Information */}
        {!isEditing && (
          <div className="space-y-2 rounded-md bg-gray-50 p-3">
            <h4 className="text-sm font-medium">Additional Information</h4>
            <div className="space-y-1 text-sm text-gray-600">
              {product.origin?.country && (
                <div className="flex justify-between">
                  <span>Country of Origin:</span>
                  <span>{product.origin.country}</span>
                </div>
              )}
              {product.warranty?.duration && (
                <div className="flex justify-between">
                  <span>Warranty:</span>
                  <span>
                    {product.warranty.duration}{" "}
                    {product.warranty.type || "months"}
                  </span>
                </div>
              )}
              {product.returnPolicy?.period && (
                <div className="flex justify-between">
                  <span>Return Policy:</span>
                  <span>{product.returnPolicy.period} Days</span>
                </div>
              )}
              {product.yearMade && (
                <div className="flex justify-between">
                  <span>Year Made:</span>
                  <span>{product.yearMade}</span>
                </div>
              )}
              {product.condition && (
                <div className="flex justify-between">
                  <span>Condition:</span>
                  <span className="capitalize">{product.condition}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
