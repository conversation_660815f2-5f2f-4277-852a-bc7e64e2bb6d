import { Document } from "mongoose";

/**
 * Brand interface matching the frontend Brand type
 */
export interface IBrand {
  name: string;
  description: string;
  slug: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
}

/**
 * Brand document interface for MongoDB
 */
export interface BrandDocument extends IBrand, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * DTO for creating a new brand
 */
export interface CreateBrandDto {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * DTO for updating a brand
 */
export interface UpdateBrandDto {
  name?: string;
  description?: string;
  slug?: string;
  logo?: string;
  website?: string;
  color?: string;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * Brand filters for querying
 */
export interface BrandFilters {
  isActive?: boolean;
  search?: string;
}
