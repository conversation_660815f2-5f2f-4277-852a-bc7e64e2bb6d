/**
 * Backend utility functions for generating product SKUs and barcodes
 * Following industry best practices for product identification
 */

import { Product } from "../models/product.model";

/**
 * Generate a SKU (Stock Keeping Unit) based on product information
 * Format: [BRAND]-[CATEGORY]-[TYPE]-[RANDOM]
 * Example: NIKE-SHOES-PHYS-A1B2C3
 */
export function generateSKU(productData: {
  brand?: string;
  category?: string;
  productType?: string;
  name?: string;
}): string {
  const { brand, category, productType, name } = productData;

  // Helper function to clean and format text for SKU
  const formatForSKU = (text: string, maxLength: number = 4): string => {
    return text
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '') // Remove special characters
      .substring(0, maxLength);
  };

  // Generate parts
  const brandPart = brand ? formatForSKU(brand, 4) : 'UNKN';
  const categoryPart = category ? formatForSKU(category, 4) : 'MISC';
  
  // Product type abbreviations
  const typeMap: Record<string, string> = {
    'physical': 'PHYS',
    'digital': 'DIGI',
    'service': 'SERV',
    'subscription': 'SUBS',
    'bundle': 'BNDL'
  };
  const typePart = productType ? (typeMap[productType] || formatForSKU(productType, 4)) : 'PROD';

  // Generate random alphanumeric suffix
  const randomPart = Math.random().toString(36).substring(2, 8).toUpperCase();

  return `${brandPart}-${categoryPart}-${typePart}-${randomPart}`;
}

/**
 * Generate a barcode (EAN-13 format) for the product
 * Format: 13-digit number starting with country code
 * Note: This generates a valid format but may not be registered with GS1
 */
export function generateBarcode(productData: {
  brand?: string;
  category?: string;
  price?: number;
}): string {
  const { brand, category, price } = productData;

  // Country code (first 3 digits) - using 200-299 for internal use
  const countryCode = '200';

  // Company code (next 4 digits) - based on brand hash
  const brandHash = brand ? hashString(brand) : 1000;
  const companyCode = String(brandHash % 10000).padStart(4, '0');

  // Product code (next 5 digits) - based on category and price
  const categoryHash = category ? hashString(category) : 100;
  const priceHash = price ? Math.floor(price * 100) : 0;
  const productCode = String((categoryHash + priceHash) % 100000).padStart(5, '0');

  // Calculate check digit (EAN-13 algorithm)
  const digits = (countryCode + companyCode + productCode).split('').map(Number);
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += digits[i] * (i % 2 === 0 ? 1 : 3);
  }
  const checkDigit = (10 - (sum % 10)) % 10;

  return countryCode + companyCode + productCode + checkDigit;
}

/**
 * Generate a simple hash from a string
 */
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Validate SKU format
 */
export function validateSKU(sku: string): boolean {
  // SKU should be 3-50 characters, alphanumeric with hyphens
  const skuRegex = /^[A-Z0-9-]{3,50}$/;
  return skuRegex.test(sku);
}

/**
 * Validate barcode format (EAN-13)
 */
export function validateBarcode(barcode: string): boolean {
  // Should be exactly 13 digits
  if (!/^\d{13}$/.test(barcode)) {
    return false;
  }

  // Validate check digit
  const digits = barcode.split('').map(Number);
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += digits[i] * (i % 2 === 0 ? 1 : 3);
  }
  const checkDigit = (10 - (sum % 10)) % 10;
  
  return checkDigit === digits[12];
}

/**
 * Check if SKU already exists in the database
 */
export async function checkSKUExists(sku: string): Promise<boolean> {
  try {
    const existingProduct = await Product.findOne({ sku });
    return !!existingProduct;
  } catch (error) {
    console.error('Error checking SKU existence:', error);
    return false;
  }
}

/**
 * Check if barcode already exists in the database
 */
export async function checkBarcodeExists(barcode: string): Promise<boolean> {
  try {
    const existingProduct = await Product.findOne({ barcode });
    return !!existingProduct;
  } catch (error) {
    console.error('Error checking barcode existence:', error);
    return false;
  }
}

/**
 * Generate unique SKU by checking against existing ones
 */
export async function generateUniqueSKU(productData: {
  brand?: string;
  category?: string;
  productType?: string;
  name?: string;
}): Promise<string> {
  let sku = generateSKU(productData);
  let attempts = 0;
  const maxAttempts = 10;

  while (await checkSKUExists(sku) && attempts < maxAttempts) {
    sku = generateSKU(productData);
    attempts++;
  }

  if (attempts >= maxAttempts) {
    // Fallback: add timestamp
    const timestamp = Date.now().toString().slice(-6);
    sku = sku.substring(0, sku.length - 6) + timestamp;
  }

  return sku;
}

/**
 * Generate unique barcode by checking against existing ones
 */
export async function generateUniqueBarcode(productData: {
  brand?: string;
  category?: string;
  price?: number;
}): Promise<string> {
  let barcode = generateBarcode(productData);
  let attempts = 0;
  const maxAttempts = 10;

  while (await checkBarcodeExists(barcode) && attempts < maxAttempts) {
    // Add some randomness to avoid infinite loops
    const randomOffset = Math.floor(Math.random() * 1000);
    barcode = generateBarcode({
      ...productData,
      price: (productData.price || 0) + randomOffset
    });
    attempts++;
  }

  if (attempts >= maxAttempts) {
    // Fallback: modify last digits with timestamp
    const timestamp = Date.now().toString().slice(-3);
    barcode = barcode.substring(0, 10) + timestamp;
    
    // Recalculate check digit
    const digits = barcode.substring(0, 12).split('').map(Number);
    let sum = 0;
    for (let i = 0; i < 12; i++) {
      sum += digits[i] * (i % 2 === 0 ? 1 : 3);
    }
    const checkDigit = (10 - (sum % 10)) % 10;
    barcode = barcode.substring(0, 12) + checkDigit;
  }

  return barcode;
}

/**
 * Generate product identifiers (SKU + Barcode) in one call
 */
export async function generateProductIdentifiers(productData: {
  brand?: string;
  category?: string;
  productType?: string;
  name?: string;
  price?: number;
}): Promise<{
  sku: string;
  barcode: string;
}> {
  const [sku, barcode] = await Promise.all([
    generateUniqueSKU(productData),
    generateUniqueBarcode(productData)
  ]);

  return {
    sku,
    barcode
  };
}

/**
 * Format barcode for display (with spaces for readability)
 * Example: 1234567890123 -> 123 4567 890123
 */
export function formatBarcodeDisplay(barcode: string): string {
  if (barcode.length !== 13) return barcode;
  
  return `${barcode.substring(0, 3)} ${barcode.substring(3, 7)} ${barcode.substring(7, 13)}`;
}
