"use client";

import { useState } from "react";

import { Check, Search } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// import { ScrollArea } from "@/components/ui/scroll-area"; // Commented out for now

// Curated list of category-appropriate emojis
const categoryEmojis = [
  // Shopping & Commerce
  "🛒",
  "🛍️",
  "💳",
  "💰",
  "🏪",
  "🏬",
  "🏭",
  "🏢",

  // Electronics & Tech
  "📱",
  "💻",
  "⌚",
  "📺",
  "🎮",
  "📷",
  "🎧",
  "⚡",
  "🔌",
  "💡",

  // Fashion & Clothing
  "👕",
  "👔",
  "👗",
  "👠",
  "👜",
  "🧥",
  "👖",
  "🧦",
  "👒",
  "💄",

  // Home & Garden
  "🏠",
  "🛏️",
  "🪑",
  "🛋️",
  "🚿",
  "🧹",
  "🌱",
  "🌸",
  "🌿",
  "🪴",

  // Food & Beverages
  "🍎",
  "🥖",
  "🍕",
  "🍔",
  "☕",
  "🍷",
  "🧀",
  "🥗",
  "🍰",
  "🍫",

  // Sports & Fitness
  "⚽",
  "🏀",
  "🎾",
  "🏋️",
  "🚴",
  "🏃",
  "🧘",
  "🏊",
  "⛷️",
  "🏆",

  // Health & Beauty
  "💊",
  "🧴",
  "🧼",
  "🪥",
  "💅",
  "💆",
  "🧖",
  "🌟",
  "✨",
  "💎",

  // Books & Education
  "📚",
  "📖",
  "✏️",
  "🖊️",
  "📝",
  "🎓",
  "🔬",
  "🧮",
  "📐",
  "🗂️",

  // Entertainment
  "🎬",
  "🎵",
  "🎪",
  "🎨",
  "🎭",
  "🎲",
  "🧩",
  "🎯",
  "🎪",
  "🎊",

  // Transportation
  "🚗",
  "🚲",
  "🛴",
  "✈️",
  "🚢",
  "🚂",
  "🚌",
  "🏍️",
  "🛻",
  "⛽",

  // Tools & Hardware
  "🔧",
  "🔨",
  "⚒️",
  "🪚",
  "🔩",
  "⚙️",
  "🧰",
  "🪛",
  "📏",
  "🔒",

  // Nature & Animals
  "🌳",
  "🌺",
  "🦋",
  "🐕",
  "🐱",
  "🐠",
  "🌊",
  "⭐",
  "🌙",
  "☀️",

  // General Categories
  "📦",
  "🏷️",
  "📋",
  "📊",
  "🎯",
  "🔥",
  "⭐",
  "💫",
  "🎁",
  "🎉",
];

const emojiCategories = {
  Shopping: ["🛒", "🛍️", "💳", "💰", "🏪", "🏬", "🏭", "🏢"],
  Electronics: ["📱", "💻", "⌚", "📺", "🎮", "📷", "🎧", "⚡", "🔌", "💡"],
  Fashion: ["👕", "👔", "👗", "👠", "👜", "🧥", "👖", "🧦", "👒", "💄"],
  Home: ["🏠", "🛏️", "🪑", "🛋️", "🚿", "🧹", "🌱", "🌸", "🌿", "🪴"],
  Food: ["🍎", "🥖", "🍕", "🍔", "☕", "🍷", "🧀", "🥗", "🍰", "🍫"],
  Sports: ["⚽", "🏀", "🎾", "🏋️", "🚴", "🏃", "🧘", "🏊", "⛷️", "🏆"],
  Health: ["💊", "🧴", "🧼", "🪥", "💅", "💆", "🧖", "🌟", "✨", "💎"],
  Books: ["📚", "📖", "✏️", "🖊️", "📝", "🎓", "🔬", "🧮", "📐", "🗂️"],
  Entertainment: ["🎬", "🎵", "🎪", "🎨", "🎭", "🎲", "🧩", "🎯", "🎪", "🎊"],
  Transport: ["🚗", "🚲", "🛴", "✈️", "🚢", "🚂", "🚌", "🏍️", "🛻", "⛽"],
  Tools: ["🔧", "🔨", "⚒️", "🪚", "🔩", "⚙️", "🧰", "🪛", "📏", "🔒"],
  Nature: ["🌳", "🌺", "🦋", "🐕", "🐱", "🐠", "🌊", "⭐", "🌙", "☀️"],
  General: ["📦", "🏷️", "📋", "📊", "🎯", "🔥", "⭐", "💫", "🎁", "🎉"],
};

type EmojiPickerProps = {
  value?: string;
  onChange: (emoji: string) => void;
  placeholder?: string;
};

export const EmojiPicker = ({
  value,
  onChange,
  placeholder = "Pick an emoji",
}: EmojiPickerProps) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("All");

  const filteredEmojis =
    selectedCategory === "All"
      ? categoryEmojis
      : emojiCategories[selectedCategory as keyof typeof emojiCategories] || [];

  const searchFilteredEmojis = searchQuery
    ? filteredEmojis.filter((emoji) => {
        // You could add emoji names/descriptions here for better search
        return true; // For now, show all in category
      })
    : filteredEmojis;

  const handleEmojiSelect = (emoji: string) => {
    onChange(emoji);
    setOpen(false);
  };

  return (
    <div className="space-y-2">
      <Label>Icon</Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-start text-left font-normal"
          >
            {value ? (
              <div className="flex items-center gap-2">
                <span className="text-2xl">{value}</span>
                <span>Selected</span>
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="space-y-4 p-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search emojis..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Category Tabs */}
            <div className="flex flex-wrap gap-1">
              <Button
                variant={selectedCategory === "All" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("All")}
              >
                All
              </Button>
              {Object.keys(emojiCategories).map((category) => (
                <Button
                  key={category}
                  variant={
                    selectedCategory === category ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>

            {/* Emoji Grid */}
            <div className="h-64 overflow-y-auto rounded-md border">
              <div className="grid grid-cols-8 gap-2 p-2">
                {searchFilteredEmojis.map((emoji, index) => (
                  <Button
                    key={`${emoji}-${index}`}
                    variant="ghost"
                    size="sm"
                    className="h-10 w-10 p-0 hover:bg-muted"
                    onClick={() => handleEmojiSelect(emoji)}
                  >
                    <span className="text-xl">{emoji}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Custom Input */}
            <div className="border-t pt-4">
              <Label className="text-sm text-muted-foreground">
                Or enter custom emoji:
              </Label>
              <Input
                placeholder="🎯"
                value={value}
                onChange={(e) => onChange(e.target.value)}
                className="mt-1"
                maxLength={2}
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
