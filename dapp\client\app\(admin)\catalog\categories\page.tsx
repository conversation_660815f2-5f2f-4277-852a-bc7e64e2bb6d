"use client";

import React, { useState } from "react";
import { ArrowLeft, LayoutGrid } from "lucide-react";
import { useRouter } from "next/navigation";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { CategoryManagerEnhanced } from "@/components/pages/management/CategoryManagerEnhanced";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Category } from "@/lib/api/categories";

export default function CategoriesPage() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);

  const handleCategoriesChange = (updatedCategories: Category[]) => {
    setCategories(updatedCategories);
  };

  return (
    <>
      <PageHeaderWrapper
        title="Category Management"
        description="Organize your products with categories and subcategories for better navigation"
      >
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/admin/catalog")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Catalog
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LayoutGrid className="h-5 w-5 text-blue-600" />
              Category Management
            </CardTitle>
            <p className="text-sm text-gray-600">
              Organize your products with categories and subcategories for better navigation
            </p>
          </CardHeader>
          <CardContent>
            <CategoryManagerEnhanced
              initialCategories={categories}
              onCategoriesChange={handleCategoriesChange}
            />
          </CardContent>
        </Card>
      </div>
    </>
  );
}
