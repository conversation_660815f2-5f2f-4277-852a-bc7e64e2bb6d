import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";

import { BrandService } from "../../services/brand.service";
import { CreateBrandDto, UpdateBrandDto } from "../../types/brand.types";

/**
 * Controller for handling brand-related HTTP requests
 */
export class BrandController {
  private brandService: BrandService;

  constructor() {
    this.brandService = new BrandService();
  }

  /**
   * Create a new brand
   * POST /api/brands
   */
  createBrand = async (req: Request, res: Response): Promise<void> => {
    try {
      const brandData: CreateBrandDto = req.body;
      const brand = await this.brandService.createBrand(brandData);

      res.status(StatusCodes.CREATED).json({
        success: true,
        data: brand,
        message: "Brand created successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create brand";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get all brands with optional filtering
   * GET /api/brands
   */
  getBrands = async (req: Request, res: Response): Promise<void> => {
    try {
      const filters = {
        isActive:
          req.query.isActive === "true"
            ? true
            : req.query.isActive === "false"
              ? false
              : undefined,
        search: req.query.search as string,
      };

      const brands = await this.brandService.getBrands(filters);

      res.status(StatusCodes.OK).json({
        success: true,
        data: brands,
        count: brands.length,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch brands";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a brand by ID
   * GET /api/brands/:id
   */
  getBrandById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const brand = await this.brandService.getBrandById(id);

      if (!brand) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Brand not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: brand,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch brand";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a brand by slug
   * GET /api/brands/slug/:slug
   */
  getBrandBySlug = async (req: Request, res: Response): Promise<void> => {
    try {
      const { slug } = req.params;
      const brand = await this.brandService.getBrandBySlug(slug);

      if (!brand) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Brand not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: brand,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch brand";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Update a brand
   * PUT /api/brands/:id
   */
  updateBrand = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData: UpdateBrandDto = req.body;

      const brand = await this.brandService.updateBrand(id, updateData);

      if (!brand) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Brand not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: brand,
        message: "Brand updated successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update brand";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Delete a brand
   * DELETE /api/brands/:id
   */
  deleteBrand = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.brandService.deleteBrand(id);

      if (!deleted) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Brand not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        message: "Brand deleted successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete brand";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Recalculate product counts for all brands
   * POST /api/brands/recalculate-counts
   */
  recalculateProductCounts = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      await this.brandService.recalculateProductCounts();

      res.status(StatusCodes.OK).json({
        success: true,
        message: "Brand product counts recalculated successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to recalculate brand product counts";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };
}
