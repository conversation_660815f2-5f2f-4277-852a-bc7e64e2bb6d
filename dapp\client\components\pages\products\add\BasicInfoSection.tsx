import { useEffect, useState } from "react";

import {
  ExternalLink,
  Eye,
  EyeOff,
  FileText,
  Hash,
  Info,
  Layers,
  Lock,
  Package,
  Shield,
  Sparkles,
  Tag,
  Users,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { Brand } from "@/components/pages/management/BrandManager";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ProductFormData } from "@/schemas/productSchema";

import { FormField } from "../../../common/FormField";
import { CollapsibleSection } from "../../../ui/collapsible-section";
import { ImportantNotice } from "../../../ui/important-notice";

type BasicInfoSectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
};

export const BasicInfoSection = ({
  register,
  errors,
  setValue,
  watch,
}: BasicInfoSectionProps) => {
  const [brands, setBrands] = useState<Brand[]>([]);

  // Load brands from localStorage
  useEffect(() => {
    try {
      const storedBrands = localStorage.getItem("product-brands");
      if (storedBrands) {
        setBrands(JSON.parse(storedBrands));
      }
    } catch (error) {
      console.error("Error loading brands from localStorage:", error);
    }
  }, []);
  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-blue-100 p-2">
          <Info className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Basic Information
          </h2>
          <p className="text-gray-600">
            Start by adding the essential details about your product
          </p>
        </div>
      </div>

      {/* Important Notice */}
      <ImportantNotice
        description="You must complete all required fields in this section before proceeding to the next step."
        requiredFields={[
          "Product Name",
          "Brand",
          "Product Type",
          "Product Description",
        ]}
        tip="These basic details help customers find and understand your product, and are essential for creating a complete product listing."
        variant="amber"
      />

      {/* Product Name Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Package className="h-5 w-5 text-blue-600" />
            Product Name
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Choose a clear, descriptive name that customers will easily find
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="name"
            label=""
            error={errors.name?.message}
            optional={false}
          >
            <Input
              id="name"
              {...register("name")}
              placeholder="e.g., Wireless Bluetooth Headphones - Premium Quality"
              className="border-2 p-4 text-lg focus:border-blue-500"
            />
          </FormField>

          <div className="mt-3 rounded-lg bg-blue-50 p-3">
            <div className="flex items-start gap-2">
              <Sparkles className="mt-0.5 h-4 w-4 text-blue-600" />
              <div className="text-sm text-blue-800">
                <strong>Pro tip:</strong> Include key features and benefits in
                your product name for better searchability
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Brand Selection Card */}
      <Card className="border-l-4 border-l-green-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Tag className="h-5 w-5 text-green-600" />
            Brand
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Select the brand or manufacturer of this product
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="brand"
            label=""
            error={errors.brand?.message}
            optional={false}
          >
            <Select
              onValueChange={(value) => setValue("brand", value)}
              value={watch("brand")}
            >
              <SelectTrigger className="border-2 p-4 text-lg focus:border-green-500">
                <SelectValue placeholder="Choose a brand..." />
              </SelectTrigger>
              <SelectContent>
                {brands.length === 0 ? (
                  <SelectItem value="no-brands" disabled>
                    No brands available - Create one first
                  </SelectItem>
                ) : (
                  brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.name}>
                      <div className="flex items-center gap-2">
                        <Tag className="h-4 w-4" />
                        {brand.name}
                      </div>
                    </SelectItem>
                  ))
                )}
                <SelectItem value="other">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    Other (Custom Brand)
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {watch("brand") === "other" && (
              <div className="mt-4 rounded-lg border border-amber-200 bg-amber-50 p-4">
                <Input
                  id="custom-brand"
                  {...register("brand")}
                  placeholder="Enter custom brand name"
                  className="border-amber-300 focus:border-amber-500"
                />
                <p className="mt-2 text-xs text-amber-700">
                  This will create a new brand in your catalog
                </p>
              </div>
            )}

            <div className="mt-3 flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => window.open("/admin/brands", "_blank")}
                className="border-green-300 text-green-600 hover:bg-green-50"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Manage Brands
              </Button>
              <span className="text-xs text-gray-500">
                Add new brands or edit existing ones
              </span>
            </div>
          </FormField>
        </CardContent>
      </Card>

      {/* Product Type Selection Card */}
      <Card className="border-l-4 border-l-indigo-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Layers className="h-5 w-5 text-indigo-600" />
            Product Type
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Choose the type of product you&apos;re selling - this affects
            available options
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="productType"
            label=""
            error={errors.productType?.message}
            optional={false}
          >
            <Select
              onValueChange={(value) => {
                setValue("productType", value as any);

                // Auto-configure fields based on product type
                if (value === "digital") {
                  setValue("requiresShipping", false);
                  setValue("virtual", true);
                  setValue("shippingCost", undefined);
                  setValue("yearMade", undefined);
                  setValue("dimensions", undefined);
                  setValue("weight", undefined);
                } else if (value === "service") {
                  setValue("requiresShipping", false);
                  setValue("virtual", true);
                  setValue("stock", 0);
                  setValue("shippingCost", undefined);
                  setValue("yearMade", undefined);
                  setValue("dimensions", undefined);
                  setValue("weight", undefined);
                } else if (value === "physical") {
                  setValue("requiresShipping", true);
                  setValue("virtual", false);
                  setValue("stock", 1);
                }
              }}
              value={watch("productType")}
            >
              <SelectTrigger className="border-2 p-4 text-lg focus:border-indigo-500">
                <SelectValue placeholder="Select product type..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="physical">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    <span className="font-medium">Physical Product</span>
                  </div>
                </SelectItem>
                <SelectItem value="digital">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4" />
                    <span className="font-medium">Digital Product</span>
                  </div>
                </SelectItem>
                <SelectItem value="service">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    <span className="font-medium">Service</span>
                  </div>
                </SelectItem>
                <SelectItem value="subscription">
                  <div className="flex items-center gap-2">
                    <Layers className="h-4 w-4" />
                    <span className="font-medium">Subscription</span>
                  </div>
                </SelectItem>
                <SelectItem value="bundle">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    <span className="font-medium">Bundle</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </FormField>

          {/* Product Type Descriptions */}
          {watch("productType") && (
            <div className="mt-3 rounded-lg border border-indigo-200 bg-indigo-50 p-3">
              <div className="flex items-start gap-2">
                <Layers className="mt-0.5 h-4 w-4 text-indigo-600" />
                <div className="text-sm text-indigo-800">
                  <strong>
                    {watch("productType") === "physical" && "Physical Product:"}
                    {watch("productType") === "digital" && "Digital Product:"}
                    {watch("productType") === "service" && "Service:"}
                    {watch("productType") === "subscription" && "Subscription:"}
                    {watch("productType") === "bundle" && "Bundle:"}
                  </strong>
                  <span className="ml-1">
                    {watch("productType") === "physical" &&
                      "Tangible items that require shipping and inventory tracking"}
                    {watch("productType") === "digital" &&
                      "Downloads, software, digital content - no shipping required"}
                    {watch("productType") === "service" &&
                      "Consultations, appointments, virtual services - no physical delivery"}
                    {watch("productType") === "subscription" &&
                      "Recurring billing products with ongoing access"}
                    {watch("productType") === "bundle" &&
                      "Multiple products sold together as a package"}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="mt-3 rounded-lg bg-blue-50 p-3">
            <div className="flex items-start gap-2">
              <Sparkles className="mt-0.5 h-4 w-4 text-blue-600" />
              <div className="text-sm text-blue-800">
                <strong>Note:</strong> Product type affects which form sections
                are required and available options
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Description Card */}
      <Card className="border-l-4 border-l-orange-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <FileText className="h-5 w-5 text-orange-600" />
            Product Description
            <Badge variant="destructive" className="text-xs">
              Required
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Provide a detailed description that highlights features, benefits,
            and specifications
          </p>
        </CardHeader>
        <CardContent>
          <FormField
            id="description"
            label=""
            error={errors.description?.message}
            optional={false}
          >
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Describe your product in detail. Include features, benefits, materials, dimensions, and any other relevant information that will help customers make a purchasing decision..."
              rows={6}
              className="resize-none border-2 p-4 text-base focus:border-orange-500"
            />
          </FormField>

          <div className="mt-3 rounded-lg bg-orange-50 p-3">
            <div className="flex items-start gap-2">
              <FileText className="mt-0.5 h-4 w-4 text-orange-600" />
              <div className="text-sm text-orange-800">
                <strong>Writing tips:</strong> Use bullet points for features,
                mention materials and quality, include size/compatibility info,
                and highlight unique selling points
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Product Visibility - Collapsible */}
      <CollapsibleSection
        title="Visibility"
        description="Control who can see and purchase this product"
        icon={<Eye className="h-5 w-5 text-cyan-600" />}
        borderColor="border-l-cyan-500"
        defaultOpen={false}
        isOptional={true}
      >
        <FormField id="visibility" label="" optional={true}>
          <Select
            onValueChange={(value) => setValue("visibility", value as any)}
            value={watch("visibility")}
          >
            <SelectTrigger className="border-2 p-4 text-lg focus:border-cyan-500">
              <SelectValue placeholder="Select visibility..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="public">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span className="font-medium">Public</span>
                </div>
              </SelectItem>
              <SelectItem value="private">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">Private</span>
                </div>
              </SelectItem>
              <SelectItem value="hidden">
                <div className="flex items-center gap-2">
                  <EyeOff className="h-4 w-4" />
                  <span className="font-medium">Hidden</span>
                </div>
              </SelectItem>
              <SelectItem value="password-protected">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  <span className="font-medium">Password Protected</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </FormField>

        {/* Visibility Descriptions */}
        {watch("visibility") && (
          <div className="mt-3 rounded-lg border border-cyan-200 bg-cyan-50 p-3">
            <div className="flex items-start gap-2">
              {watch("visibility") === "public" && (
                <Users className="mt-0.5 h-4 w-4 text-cyan-600" />
              )}
              {watch("visibility") === "private" && (
                <Shield className="mt-0.5 h-4 w-4 text-cyan-600" />
              )}
              {watch("visibility") === "hidden" && (
                <EyeOff className="mt-0.5 h-4 w-4 text-cyan-600" />
              )}
              {watch("visibility") === "password-protected" && (
                <Lock className="mt-0.5 h-4 w-4 text-cyan-600" />
              )}
              <div className="text-sm text-cyan-800">
                <strong>
                  {watch("visibility") === "public" && "Public:"}
                  {watch("visibility") === "private" && "Private:"}
                  {watch("visibility") === "hidden" && "Hidden:"}
                  {watch("visibility") === "password-protected" &&
                    "Password Protected:"}
                </strong>
                <span className="ml-1">
                  {watch("visibility") === "public" &&
                    "Visible to everyone in your store and search engines"}
                  {watch("visibility") === "private" &&
                    "Only visible to you and store administrators"}
                  {watch("visibility") === "hidden" &&
                    "Not visible in catalog but accessible via direct link"}
                  {watch("visibility") === "password-protected" &&
                    "Requires password to view - perfect for exclusive products"}
                </span>
              </div>
            </div>
          </div>
        )}
      </CollapsibleSection>

      {/* Model Number - Collapsible */}
      <CollapsibleSection
        title="Model Number"
        description="Add the specific model number or identifier if available"
        icon={<Package className="h-5 w-5 text-purple-600" />}
        borderColor="border-l-purple-500"
        defaultOpen={false}
        isOptional={true}
      >
        <FormField id="model" label="" optional={true}>
          <Input
            id="model"
            {...register("model")}
            placeholder="e.g., WH-1000XM4, iPhone 15 Pro, Model ABC-123"
            className="border-2 p-4 text-lg focus:border-purple-500"
          />
        </FormField>
      </CollapsibleSection>

      {/* Short Description - Collapsible */}
      <CollapsibleSection
        title="Short Description"
        description="Brief summary for product listings and search results"
        icon={<FileText className="h-5 w-5 text-amber-600" />}
        borderColor="border-l-amber-500"
        defaultOpen={false}
        isOptional={true}
      >
        <FormField id="shortDescription" label="" optional={true}>
          <Textarea
            id="shortDescription"
            {...register("shortDescription")}
            placeholder="A brief, compelling summary of your product in 1-2 sentences..."
            rows={3}
            className="resize-none border-2 p-4 text-base focus:border-amber-500"
          />
        </FormField>

        <div className="mt-3 rounded-lg bg-amber-50 p-3">
          <div className="flex items-start gap-2">
            <FileText className="mt-0.5 h-4 w-4 text-amber-600" />
            <div className="text-sm text-amber-800">
              <strong>Tip:</strong> Keep it under 160 characters for optimal
              display in search results
            </div>
          </div>
        </div>
      </CollapsibleSection>
    </div>
  );
};
