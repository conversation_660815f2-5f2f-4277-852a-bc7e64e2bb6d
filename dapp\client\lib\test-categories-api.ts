/**
 * Test script for categories API integration
 * This can be run in the browser console to test the API connection
 */
import { CategoryApiService } from "./api/categoryApi";

export async function testCategoriesApi() {
  console.log("🧪 Testing Categories API Integration...\n");

  try {
    // Test 0: Check if server is running
    console.log("0️⃣ Testing: Server connection");
    const response = await fetch("http://localhost:3010/api");
    if (response.ok) {
      const data = await response.json();
      console.log("✅ Server is running:", data);
    } else {
      console.log("❌ Server not responding:", response.status);
    }

    // Test 1: Fetch all categories
    console.log("\n1️⃣ Testing: Fetch all categories");
    const categories = await CategoryApiService.getCategories();
    console.log("✅ Success! Categories fetched:", categories.length);
    console.log("Categories:", categories);

    // Test 2: Fetch only active categories
    console.log("\n2️⃣ Testing: Fetch active categories only");
    const activeCategories = await CategoryApiService.getCategories({
      isActive: true,
    });
    console.log(
      "✅ Success! Active categories fetched:",
      activeCategories.length
    );

    // Test 3: Create a test category
    console.log("\n3️⃣ Testing: Create a new category");
    const testCategory = {
      name: `Test Category ${Date.now()}`,
      description: "This is a test category created by the API test",
      icon: "🧪",
      color: "#FF6B6B",
      isActive: true,
    };

    const createdCategory =
      await CategoryApiService.createCategory(testCategory);
    console.log("✅ Success! Category created:", createdCategory);

    // Test 4: Update the test category
    console.log("\n4️⃣ Testing: Update the test category");
    const updatedCategory = await CategoryApiService.updateCategory(
      createdCategory.id,
      {
        description: "Updated description for test category",
        color: "#4ECDC4",
      }
    );
    console.log("✅ Success! Category updated:", updatedCategory);

    // Test 5: Get category by ID
    console.log("\n5️⃣ Testing: Get category by ID");
    const fetchedCategory = await CategoryApiService.getCategoryById(
      createdCategory.id
    );
    console.log("✅ Success! Category fetched by ID:", fetchedCategory);

    // Test 6: Delete the test category
    console.log("\n6️⃣ Testing: Delete the test category");
    await CategoryApiService.deleteCategory(createdCategory.id);
    console.log("✅ Success! Category deleted");

    // Test 7: Verify deletion
    console.log("\n7️⃣ Testing: Verify category was deleted");
    try {
      await CategoryApiService.getCategoryById(createdCategory.id);
      console.log("❌ Error: Category should have been deleted");
    } catch (error) {
      console.log("✅ Success! Category was properly deleted (404 expected)");
    }

    console.log("\n🎉 All tests passed! Categories API is working correctly.");
    return true;
  } catch (error) {
    console.error("❌ Test failed:", error);
    console.log("\n🔧 Troubleshooting tips:");
    console.log("1. Make sure the backend server is running on port 3010");
    console.log("2. Check if MongoDB is connected");
    console.log("3. Verify CORS settings allow requests from the frontend");
    console.log("4. Check the browser network tab for failed requests");
    console.log("5. Check if categories exist in the database");
    return false;
  }
}

// Make it available globally for browser console testing
if (typeof window !== "undefined") {
  (window as any).testCategoriesApi = testCategoriesApi;
}
