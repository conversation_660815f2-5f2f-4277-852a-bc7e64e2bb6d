{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/app/admin/products/page.tsx"], "sourcesContent": ["import { redirect } from \"next/navigation\";\r\n\r\n// Redirect to the products list page with preserved query parameters\r\nexport default function AdminProducts({\r\n  searchParams,\r\n}: {\r\n  searchParams: { [key: string]: string | string[] | undefined };\r\n}) {\r\n  // Preserve query parameters in the redirect\r\n  const queryString = new URLSearchParams();\r\n\r\n  Object.entries(searchParams).forEach(([key, value]) => {\r\n    if (value !== undefined) {\r\n      if (Array.isArray(value)) {\r\n        value.forEach((v) => queryString.append(key, v));\r\n      } else {\r\n        queryString.append(key, value);\r\n      }\r\n    }\r\n  });\r\n\r\n  const redirectUrl = queryString.toString()\r\n    ? `/admin/products/list?${queryString.toString()}`\r\n    : \"/admin/products/list\";\r\n\r\n  redirect(redirectUrl);\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGe,SAAS,cAAc,EACpC,YAAY,EAGb;IACC,4CAA4C;IAC5C,MAAM,cAAc,IAAI;IAExB,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAChD,IAAI,UAAU,WAAW;YACvB,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAC,IAAM,YAAY,MAAM,CAAC,KAAK;YAC/C,OAAO;gBACL,YAAY,MAAM,CAAC,KAAK;YAC1B;QACF;IACF;IAEA,MAAM,cAAc,YAAY,QAAQ,KACpC,CAAC,qBAAqB,EAAE,YAAY,QAAQ,IAAI,GAChD;IAEJ,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;AACX", "debugId": null}}]}