"use client";

import React from "react";

import { Product } from "@/types/product";

import { ProductDetailsSection } from "./ProductDetailsSection";
import { ProductImageSection } from "./ProductImageSection";
import { ProductInfoSection } from "./ProductInfoSection";
import { ProductInventorySection } from "./ProductInventorySection";
import { ProductPricingSection } from "./ProductPricingSection";
import { ProductStatusSection } from "./ProductStatusSection";

type ProductDetailsContentProps = {
  product: Product;
  isEditing: boolean;
  onProductUpdate: (product: Product) => void;
  onEditToggle: () => void;
  onSave: () => void;
  onCancel: () => void;
};

type ProductSectionProps = {
  product: Product;
  isEditing: boolean;
  onProductUpdate: (product: Product) => void;
};

export const ProductDetailsContent = ({
  product,
  isEditing,
  onProductUpdate,
  onEditToggle,
  onSave,
  onCancel,
}: ProductDetailsContentProps) => {
  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
      {/* Left Column - Product Image */}
      <div className="lg:col-span-1">
        <ProductImageSection
          product={product}
          isEditing={isEditing}
          onProductUpdate={onProductUpdate}
        />
      </div>

      {/* Right Column - Product Information */}
      <div className="space-y-6 lg:col-span-2">
        {/* Product Info */}
        <ProductInfoSection
          product={product}
          isEditing={isEditing}
          onProductUpdate={onProductUpdate}
        />

        {/* Grid for other sections */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Pricing */}
          <ProductPricingSection
            product={product}
            isEditing={isEditing}
            onProductUpdate={onProductUpdate}
          />

          {/* Inventory */}
          <ProductInventorySection
            product={product}
            isEditing={isEditing}
            onProductUpdate={onProductUpdate}
          />

          {/* Status */}
          <ProductStatusSection
            product={product}
            isEditing={isEditing}
            onProductUpdate={onProductUpdate}
          />

          {/* Details */}
          <ProductDetailsSection
            product={product}
            isEditing={isEditing}
            onProductUpdate={onProductUpdate}
          />
        </div>
      </div>
    </div>
  );
};
