import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { ProductDetailsWrapper } from "@/components/pages/products/details/ProductDetailsWrapper";

type Props = {
  params: {
    id: string;
  };
};

export default function AdminProductDetails({ params }: Props) {
  return (
    <>
      <PageHeaderWrapper
        title="Product Details"
        description="View and edit product information"
      />

      <div className="container mx-auto mt-6">
        <ProductDetailsWrapper productId={params.id} />
      </div>
    </>
  );
}
