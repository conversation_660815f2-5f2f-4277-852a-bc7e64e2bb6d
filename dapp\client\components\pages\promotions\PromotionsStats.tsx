"use client";

import { 
  TrendingUp, 
  TrendingDown, 
  Flame, 
  Calendar, 
  Users, 
  DollarSign,
  Target,
  Clock
} from "lucide-react";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { promotionStats, mockPromotions } from "@/constants/promotions";

export const PromotionsStats = () => {
  const stats = [
    {
      title: "Active Promotions",
      value: promotionStats.activePromotions.toString(),
      change: "+2 this week",
      trend: "up",
      icon: Flame,
      description: "Currently running campaigns",
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Total Usage",
      value: promotionStats.totalUsage.toString(),
      change: "+15% from last month",
      trend: "up",
      icon: Users,
      description: "Times promotions were used",
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Customer Savings",
      value: `€${promotionStats.totalSavings.toFixed(0)}`,
      change: "+€1,250 this month",
      trend: "up",
      icon: DollarSign,
      description: "Total amount saved by customers",
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Scheduled Campaigns",
      value: promotionStats.scheduledPromotions.toString(),
      change: "Starting soon",
      trend: "neutral",
      icon: Calendar,
      description: "Upcoming promotional campaigns",
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return "text-green-600";
      case "down":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <div className={`rounded-full p-2 ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              <div className="flex items-center gap-1 text-xs">
                {getTrendIcon(stat.trend)}
                <span className={getTrendColor(stat.trend)}>
                  {stat.change}
                </span>
              </div>
              <p className="mt-1 text-xs text-gray-500">{stat.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Promotion Performance Overview */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Top Performing Promotions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-orange-600" />
              Top Performing Promotions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockPromotions
                .filter(p => p.status === "active")
                .sort((a, b) => b.usageCount - a.usageCount)
                .slice(0, 3)
                .map((promo) => (
                  <div key={promo.id} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-sm">{promo.name}</div>
                      <div className="text-xs text-gray-500">
                        {promo.type === "percentage" ? `${promo.discountValue}% off` : 
                         promo.type === "fixed-amount" ? `€${promo.discountValue} off` :
                         promo.type === "free-shipping" ? "Free shipping" : "Special offer"}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{promo.usageCount}</div>
                      <div className="text-xs text-gray-500">uses</div>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        {/* Promotion Types Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Flame className="h-5 w-5 text-orange-600" />
              Promotion Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { type: "percentage", label: "Percentage Discounts", color: "bg-blue-500" },
                { type: "fixed-amount", label: "Fixed Amount Off", color: "bg-green-500" },
                { type: "buy-x-get-y", label: "Buy X Get Y", color: "bg-purple-500" },
                { type: "free-shipping", label: "Free Shipping", color: "bg-orange-500" },
              ].map((item) => {
                const count = mockPromotions.filter(p => p.type === item.type).length;
                const percentage = (count / mockPromotions.length) * 100;
                
                return (
                  <div key={item.type} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`h-3 w-3 rounded-full ${item.color}`}></div>
                      <span className="text-sm">{item.label}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{count}</div>
                      <div className="text-xs text-gray-500">{percentage.toFixed(1)}%</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
