import { Review } from "@/types/review";

export const mockReviews: Review[] = [
  {
    id: "REV-001",
    userId: "CUST-001",
    productId: "PROD-001",
    rating: 5,
    title: "Excellent product quality!",
    comment: "I'm absolutely thrilled with this purchase! The wireless headphones exceeded my expectations in every way. The sound quality is crystal clear, the battery life is impressive, and they're incredibly comfortable for long listening sessions. The noise cancellation feature works perfectly, making them ideal for both work and travel. Highly recommend to anyone looking for premium audio experience.",
    images: ["/reviews/review-1-1.jpg", "/reviews/review-1-2.jpg"],
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    id: "REV-002", 
    userId: "CUST-002",
    productId: "PROD-002",
    rating: 4,
    title: "Great smartwatch, minor issues",
    comment: "Overall very satisfied with this smartwatch. The fitness tracking features are accurate and the display is bright and clear. Battery lasts about 2 days with moderate use. The only minor issue is that the heart rate monitor sometimes takes a few seconds to get a reading. The app integration works well and notifications are timely. Good value for the price.",
    images: ["/reviews/review-2-1.jpg"],
    createdAt: "2024-01-14T15:45:00Z",
    updatedAt: "2024-01-14T15:45:00Z"
  },
  {
    id: "REV-003",
    userId: "CUST-003", 
    productId: "PROD-001",
    rating: 5,
    title: "Perfect for work from home",
    comment: "These headphones have been a game-changer for my work-from-home setup. The noise cancellation blocks out all the household distractions, and the microphone quality is excellent for video calls. Colleagues have commented on how clear my audio sounds. The comfort level is outstanding - I can wear them for 8+ hours without any discomfort.",
    createdAt: "2024-01-13T09:20:00Z",
    updatedAt: "2024-01-13T09:20:00Z"
  },
  {
    id: "REV-004",
    userId: "CUST-004",
    productId: "PROD-003",
    rating: 3,
    title: "Decent cable, nothing special",
    comment: "It's a USB-C cable that does what it's supposed to do. Charges my devices and transfers data without issues. The build quality seems solid and the length is convenient. However, for the price, I expected maybe faster charging speeds. It's not bad, just not exceptional either.",
    createdAt: "2024-01-12T14:10:00Z",
    updatedAt: "2024-01-12T14:10:00Z"
  },
  {
    id: "REV-005",
    userId: "CUST-005",
    productId: "PROD-002",
    rating: 2,
    title: "Disappointed with battery life",
    comment: "While the smartwatch has nice features and looks good, the battery life is much shorter than advertised. I'm lucky to get through a full day with basic usage. The fitness tracking seems accurate, but the constant charging is becoming annoying. Customer service was helpful but couldn't resolve the battery issue.",
    createdAt: "2024-01-11T11:30:00Z",
    updatedAt: "2024-01-11T11:30:00Z"
  },
  {
    id: "REV-006",
    userId: "CUST-006",
    productId: "PROD-004",
    rating: 4,
    title: "Stylish and protective",
    comment: "Really happy with this phone case! It fits perfectly and provides good protection without adding too much bulk. The design is sleek and professional-looking. The button responsiveness is maintained and all ports are easily accessible. Only minor complaint is that it attracts fingerprints easily.",
    images: ["/reviews/review-6-1.jpg", "/reviews/review-6-2.jpg", "/reviews/review-6-3.jpg"],
    createdAt: "2024-01-10T16:20:00Z",
    updatedAt: "2024-01-10T16:20:00Z"
  },
  {
    id: "REV-007",
    userId: "CUST-007",
    productId: "PROD-005",
    rating: 5,
    title: "Perfect laptop stand!",
    comment: "This laptop stand has completely transformed my workspace ergonomics. The adjustable height and angle features are smooth and stay in place perfectly. The build quality is excellent - very sturdy and stable. My neck and back pain from hunching over my laptop has significantly improved. Assembly was quick and easy. Highly recommend for anyone working long hours on a laptop.",
    createdAt: "2024-01-09T13:45:00Z",
    updatedAt: "2024-01-09T13:45:00Z"
  },
  {
    id: "REV-008",
    userId: "CUST-008",
    productId: "PROD-001",
    rating: 1,
    title: "Defective product received",
    comment: "Unfortunately, I received a defective unit. The left earcup stopped working after just 2 days of light use. The sound was great when it worked, but the build quality seems inconsistent. I've initiated a return and hoping for a replacement. Will update my review if the replacement works better.",
    createdAt: "2024-01-08T10:15:00Z",
    updatedAt: "2024-01-08T10:15:00Z"
  },
  {
    id: "REV-009",
    userId: "CUST-009",
    productId: "PROD-003",
    rating: 5,
    title: "Fast charging, durable cable",
    comment: "Excellent USB-C cable! Charges my phone and tablet much faster than the original cables that came with them. The braided design feels premium and durable. After 3 months of daily use, there's no sign of wear or fraying. The 6-foot length is perfect for my needs. Great value for money!",
    createdAt: "2024-01-07T08:30:00Z",
    updatedAt: "2024-01-07T08:30:00Z"
  },
  {
    id: "REV-010",
    userId: "CUST-010",
    productId: "PROD-004",
    rating: 4,
    title: "Good protection, easy installation",
    comment: "The phone case provides excellent protection and was very easy to install. The cutouts are precise and don't interfere with any functions. The material feels premium and has a nice grip. The only reason I'm not giving 5 stars is that the color is slightly different from what was shown in the photos - more muted than expected.",
    createdAt: "2024-01-06T19:45:00Z",
    updatedAt: "2024-01-06T19:45:00Z"
  }
];

// Helper function to get reviews by product
export const getReviewsByProduct = (productId: string): Review[] => {
  return mockReviews.filter(review => review.productId === productId);
};

// Helper function to get reviews by user
export const getReviewsByUser = (userId: string): Review[] => {
  return mockReviews.filter(review => review.userId === userId);
};

// Helper function to calculate average rating
export const calculateAverageRating = (reviews: Review[]): number => {
  if (reviews.length === 0) return 0;
  const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
  return Math.round((sum / reviews.length) * 10) / 10;
};

// Helper function to get rating distribution
export const getRatingDistribution = (reviews: Review[]) => {
  const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
  reviews.forEach(review => {
    distribution[review.rating as keyof typeof distribution]++;
  });
  return distribution;
};
