import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { OrderDetailsWrapper } from "@/components/pages/orders/details/OrderDetailsWrapper";

type Props = {
  params: {
    id: string;
  };
};

export default function AdminOrderDetails({ params }: Props) {
  return (
    <>
      <PageHeaderWrapper
        title={`Order #${params.id}`}
        description="View and manage order details"
      />

      <div className="container mx-auto mt-6">
        <OrderDetailsWrapper orderId={params.id} />
      </div>
    </>
  );
}
