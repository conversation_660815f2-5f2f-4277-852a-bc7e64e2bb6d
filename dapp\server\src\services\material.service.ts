import { FilterQuery } from "mongoose";

import { Material } from "../models/material.model";
import {
  MaterialDocument,
  CreateMaterialDto,
  UpdateMaterialDto,
  MaterialFilters,
} from "../types/material.types";

/**
 * Service class for handling material-related business logic
 */
export class MaterialService {
  /**
   * Create a new material
   */
  async createMaterial(materialData: CreateMaterialDto): Promise<MaterialDocument> {
    try {
      // Generate slug if not provided
      const slug = this.generateSlug(materialData.name);

      // Check if slug already exists
      const existingMaterial = await Material.findOne({ slug });
      if (existingMaterial) {
        throw new Error("A material with this name already exists");
      }

      // Get the next sort order
      const maxSortOrder = await Material.findOne(
        {},
        {},
        { sort: { sortOrder: -1 } }
      );
      const sortOrder =
        materialData.sortOrder ?? (maxSortOrder?.sortOrder ?? 0) + 1;

      // Set default properties if not provided
      const defaultProperties = {
        durability: "medium" as const,
        waterResistant: false,
        recyclable: false,
        weight: "medium" as const,
      };

      const material = new Material({
        ...materialData,
        slug,
        sortOrder,
        properties: { ...defaultProperties, ...materialData.properties },
      });

      return await material.save();
    } catch (error) {
      throw this.handleError(error, "Error creating material");
    }
  }

  /**
   * Get all materials with optional filtering
   */
  async getMaterials(filters: MaterialFilters = {}): Promise<MaterialDocument[]> {
    try {
      const query: FilterQuery<MaterialDocument> = {};

      // Apply filters
      if (filters.isActive !== undefined) {
        query.isActive = filters.isActive;
      }

      if (filters.search) {
        query.$text = { $search: filters.search };
      }

      if (filters.durability) {
        query["properties.durability"] = filters.durability;
      }

      if (filters.waterResistant !== undefined) {
        query["properties.waterResistant"] = filters.waterResistant;
      }

      if (filters.recyclable !== undefined) {
        query["properties.recyclable"] = filters.recyclable;
      }

      if (filters.weight) {
        query["properties.weight"] = filters.weight;
      }

      return await Material.find(query).sort({ sortOrder: 1, createdAt: -1 });
    } catch (error) {
      throw this.handleError(error, "Error fetching materials");
    }
  }

  /**
   * Get a material by ID
   */
  async getMaterialById(id: string): Promise<MaterialDocument | null> {
    try {
      return await Material.findById(id);
    } catch (error) {
      throw this.handleError(error, "Error fetching material");
    }
  }

  /**
   * Get a material by slug
   */
  async getMaterialBySlug(slug: string): Promise<MaterialDocument | null> {
    try {
      return await Material.findOne({ slug });
    } catch (error) {
      throw this.handleError(error, "Error fetching material");
    }
  }

  /**
   * Get a material by name
   */
  async getMaterialByName(name: string): Promise<MaterialDocument | null> {
    try {
      return await Material.findOne({ name });
    } catch (error) {
      throw this.handleError(error, "Error fetching material by name");
    }
  }

  /**
   * Update a material
   */
  async updateMaterial(
    id: string,
    updateData: UpdateMaterialDto
  ): Promise<MaterialDocument | null> {
    try {
      // If name is being updated, regenerate slug
      if (updateData.name) {
        const newSlug = this.generateSlug(updateData.name);

        // Check if new slug conflicts with existing materials (excluding current one)
        const existingMaterial = await Material.findOne({
          slug: newSlug,
          _id: { $ne: id },
        });

        if (existingMaterial) {
          throw new Error("A material with this name already exists");
        }

        updateData = { ...updateData, slug: newSlug };
      }

      return await Material.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      });
    } catch (error) {
      throw this.handleError(error, "Error updating material");
    }
  }

  /**
   * Delete a material
   */
  async deleteMaterial(id: string): Promise<boolean> {
    try {
      // Check if material has products
      const material = await Material.findById(id);
      if (material && material.productCount > 0) {
        throw new Error("Cannot delete material with products");
      }

      const result = await Material.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      throw this.handleError(error, "Error deleting material");
    }
  }

  /**
   * Recalculate product counts for all materials
   */
  async recalculateProductCounts(): Promise<void> {
    try {
      console.info("Starting material product count recalculation...");

      const materials = await Material.find({});
      console.info(`Found ${materials.length} materials to process`);

      // Import Product model here to avoid circular dependency
      const { Product } = await import("../models/product.model");

      const totalProducts = await Product.countDocuments();
      console.info(`Found ${totalProducts} total products`);

      for (const material of materials) {
        console.info(`Processing material: "${material.name}" (ID: ${material._id})`);

        // Count products by material name (since products store material as string)
        const countByName = await Product.countDocuments({
          material: material.name,
        });

        console.info(`  - Count by name: ${countByName}`);

        // Update the material with the correct count
        await Material.findByIdAndUpdate(material._id, {
          productCount: countByName,
        });

        console.info(`  ✓ Updated material "${material.name}" product count to ${countByName}`);
      }

      console.info("Material product count recalculation completed successfully");
    } catch (error) {
      console.error("Error recalculating material product counts:", error);
      throw this.handleError(error, "Error recalculating material product counts");
    }
  }

  /**
   * Generate a URL-friendly slug from a string
   */
  private generateSlug(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
  }

  /**
   * Handle and format errors consistently
   */
  private handleError(error: unknown, context: string): Error {
    if (error instanceof Error) {
      return new Error(`${context}: ${error.message}`);
    }
    return new Error(`${context}: Unknown error occurred`);
  }
}
