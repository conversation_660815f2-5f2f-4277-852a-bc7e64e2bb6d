import { FilterQuery } from "mongoose";

import { Color } from "../models/color.model";
import {
  ColorDocument,
  CreateColorDto,
  UpdateColorDto,
  ColorFilters,
  ColorFamily,
} from "../types/color.types";

/**
 * Service class for handling color-related business logic
 */
export class ColorService {
  /**
   * Create a new color
   */
  async createColor(colorData: CreateColorDto): Promise<ColorDocument> {
    try {
      // Generate slug if not provided
      const slug = this.generateSlug(colorData.name);

      // Check if slug already exists
      const existingColor = await Color.findOne({ slug });
      if (existingColor) {
        throw new Error("A color with this name already exists");
      }

      // Check if hex value already exists
      const existingHex = await Color.findOne({ hexValue: colorData.hexValue });
      if (existingHex) {
        throw new Error("A color with this hex value already exists");
      }

      // Get the next sort order
      const maxSortOrder = await Color.findOne(
        {},
        {},
        { sort: { sortOrder: -1 } }
      );
      const sortOrder =
        colorData.sortOrder ?? (maxSortOrder?.sortOrder ?? 0) + 1;

      const color = new Color({
        ...colorData,
        slug,
        sortOrder,
      });

      return await color.save();
    } catch (error) {
      throw this.handleError(error, "Error creating color");
    }
  }

  /**
   * Get all colors with optional filtering
   */
  async getColors(filters: ColorFilters = {}): Promise<ColorDocument[]> {
    try {
      const query: FilterQuery<ColorDocument> = {};

      // Apply filters
      if (filters.isActive !== undefined) {
        query.isActive = filters.isActive;
      }

      if (filters.search) {
        query.$text = { $search: filters.search };
      }

      if (filters.colorFamily) {
        query.colorFamily = filters.colorFamily;
      }

      if (filters.hexValue) {
        query.hexValue = filters.hexValue;
      }

      return await Color.find(query).sort({ colorFamily: 1, sortOrder: 1, createdAt: -1 });
    } catch (error) {
      throw this.handleError(error, "Error fetching colors");
    }
  }

  /**
   * Get a color by ID
   */
  async getColorById(id: string): Promise<ColorDocument | null> {
    try {
      return await Color.findById(id);
    } catch (error) {
      throw this.handleError(error, "Error fetching color");
    }
  }

  /**
   * Get a color by slug
   */
  async getColorBySlug(slug: string): Promise<ColorDocument | null> {
    try {
      return await Color.findOne({ slug });
    } catch (error) {
      throw this.handleError(error, "Error fetching color");
    }
  }

  /**
   * Get a color by name
   */
  async getColorByName(name: string): Promise<ColorDocument | null> {
    try {
      return await Color.findOne({ name });
    } catch (error) {
      throw this.handleError(error, "Error fetching color by name");
    }
  }

  /**
   * Get a color by hex value
   */
  async getColorByHex(hexValue: string): Promise<ColorDocument | null> {
    try {
      return await Color.findOne({ hexValue });
    } catch (error) {
      throw this.handleError(error, "Error fetching color by hex");
    }
  }

  /**
   * Update a color
   */
  async updateColor(
    id: string,
    updateData: UpdateColorDto
  ): Promise<ColorDocument | null> {
    try {
      // If name is being updated, regenerate slug
      if (updateData.name) {
        const newSlug = this.generateSlug(updateData.name);

        // Check if new slug conflicts with existing colors (excluding current one)
        const existingColor = await Color.findOne({
          slug: newSlug,
          _id: { $ne: id },
        });

        if (existingColor) {
          throw new Error("A color with this name already exists");
        }

        updateData = { ...updateData, slug: newSlug };
      }

      // If hex value is being updated, check for conflicts
      if (updateData.hexValue) {
        const existingHex = await Color.findOne({
          hexValue: updateData.hexValue,
          _id: { $ne: id },
        });

        if (existingHex) {
          throw new Error("A color with this hex value already exists");
        }
      }

      return await Color.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      });
    } catch (error) {
      throw this.handleError(error, "Error updating color");
    }
  }

  /**
   * Delete a color
   */
  async deleteColor(id: string): Promise<boolean> {
    try {
      // Check if color has products
      const color = await Color.findById(id);
      if (color && color.productCount > 0) {
        throw new Error("Cannot delete color with products");
      }

      const result = await Color.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      throw this.handleError(error, "Error deleting color");
    }
  }

  /**
   * Recalculate product counts for all colors
   */
  async recalculateProductCounts(): Promise<void> {
    try {
      console.info("Starting color product count recalculation...");

      const colors = await Color.find({});
      console.info(`Found ${colors.length} colors to process`);

      // Import Product model here to avoid circular dependency
      const { Product } = await import("../models/product.model");

      const totalProducts = await Product.countDocuments();
      console.info(`Found ${totalProducts} total products`);

      for (const color of colors) {
        console.info(`Processing color: "${color.name}" (ID: ${color._id})`);

        // Count products by color name (since products store color as string)
        const countByName = await Product.countDocuments({
          color: color.name,
        });

        console.info(`  - Count by name: ${countByName}`);

        // Update the color with the correct count
        await Color.findByIdAndUpdate(color._id, {
          productCount: countByName,
        });

        console.info(`  ✓ Updated color "${color.name}" product count to ${countByName}`);
      }

      console.info("Color product count recalculation completed successfully");
    } catch (error) {
      console.error("Error recalculating color product counts:", error);
      throw this.handleError(error, "Error recalculating color product counts");
    }
  }

  /**
   * Get colors grouped by color family
   */
  async getColorsByFamily(): Promise<Record<ColorFamily, ColorDocument[]>> {
    try {
      const colors = await this.getColors({ isActive: true });
      const grouped: Record<string, ColorDocument[]> = {};

      colors.forEach((color) => {
        if (!grouped[color.colorFamily]) {
          grouped[color.colorFamily] = [];
        }
        grouped[color.colorFamily].push(color);
      });

      return grouped as Record<ColorFamily, ColorDocument[]>;
    } catch (error) {
      throw this.handleError(error, "Error fetching colors by family");
    }
  }

  /**
   * Generate a URL-friendly slug from a string
   */
  private generateSlug(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
  }

  /**
   * Handle and format errors consistently
   */
  private handleError(error: unknown, context: string): Error {
    if (error instanceof Error) {
      return new Error(`${context}: ${error.message}`);
    }
    return new Error(`${context}: Unknown error occurred`);
  }
}
