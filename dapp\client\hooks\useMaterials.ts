"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

import { MaterialApi, Material, MaterialFilters, CreateMaterialDto, UpdateMaterialDto } from "@/lib/api/materials";

/**
 * Custom hook for managing materials data and operations
 */
export const useMaterials = (initialFilters?: MaterialFilters) => {
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<MaterialFilters>(initialFilters || {});

  /**
   * Fetch materials from the API
   */
  const fetchMaterials = useCallback(async (currentFilters?: MaterialFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const filtersToUse = currentFilters || filters;
      const response = await MaterialApi.getMaterials(filtersToUse);
      
      if (response.success) {
        setMaterials(response.data);
      } else {
        throw new Error("Failed to fetch materials");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch materials";
      setError(errorMessage);
      console.error("Error fetching materials:", err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  /**
   * Create a new material
   */
  const createMaterial = useCallback(async (materialData: CreateMaterialDto): Promise<Material | null> => {
    try {
      const response = await MaterialApi.createMaterial(materialData);
      
      if (response.success) {
        setMaterials(prev => [...prev, response.data]);
        toast.success("Material created successfully");
        return response.data;
      } else {
        throw new Error("Failed to create material");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to create material";
      toast.error(errorMessage);
      console.error("Error creating material:", err);
      return null;
    }
  }, []);

  /**
   * Update an existing material
   */
  const updateMaterial = useCallback(async (id: string, updateData: UpdateMaterialDto): Promise<Material | null> => {
    try {
      const response = await MaterialApi.updateMaterial(id, updateData);
      
      if (response.success) {
        setMaterials(prev => 
          prev.map(material => 
            material._id === id ? response.data : material
          )
        );
        toast.success("Material updated successfully");
        return response.data;
      } else {
        throw new Error("Failed to update material");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update material";
      toast.error(errorMessage);
      console.error("Error updating material:", err);
      return null;
    }
  }, []);

  /**
   * Delete a material
   */
  const deleteMaterial = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await MaterialApi.deleteMaterial(id);
      
      if (response.success) {
        setMaterials(prev => prev.filter(material => material._id !== id));
        toast.success("Material deleted successfully");
        return true;
      } else {
        throw new Error("Failed to delete material");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete material";
      toast.error(errorMessage);
      console.error("Error deleting material:", err);
      return false;
    }
  }, []);

  /**
   * Get a material by ID
   */
  const getMaterialById = useCallback(async (id: string): Promise<Material | null> => {
    try {
      const response = await MaterialApi.getMaterialById(id);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error("Failed to fetch material");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch material";
      console.error("Error fetching material by ID:", err);
      return null;
    }
  }, []);

  /**
   * Get a material by slug
   */
  const getMaterialBySlug = useCallback(async (slug: string): Promise<Material | null> => {
    try {
      const response = await MaterialApi.getMaterialBySlug(slug);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error("Failed to fetch material");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch material";
      console.error("Error fetching material by slug:", err);
      return null;
    }
  }, []);

  /**
   * Recalculate product counts for all materials
   */
  const recalculateProductCounts = useCallback(async (): Promise<boolean> => {
    try {
      const response = await MaterialApi.recalculateProductCounts();
      
      if (response.success) {
        // Refresh materials to get updated counts
        await fetchMaterials();
        toast.success("Material product counts recalculated successfully");
        return true;
      } else {
        throw new Error("Failed to recalculate product counts");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to recalculate product counts";
      toast.error(errorMessage);
      console.error("Error recalculating product counts:", err);
      return false;
    }
  }, [fetchMaterials]);

  /**
   * Update filters and refetch data
   */
  const updateFilters = useCallback((newFilters: MaterialFilters) => {
    setFilters(newFilters);
    fetchMaterials(newFilters);
  }, [fetchMaterials]);

  /**
   * Refresh materials data
   */
  const refreshMaterials = useCallback(() => {
    fetchMaterials();
  }, [fetchMaterials]);

  // Initial data fetch
  useEffect(() => {
    fetchMaterials();
  }, [fetchMaterials]);

  return {
    materials,
    loading,
    error,
    filters,
    createMaterial,
    updateMaterial,
    deleteMaterial,
    getMaterialById,
    getMaterialBySlug,
    recalculateProductCounts,
    updateFilters,
    refreshMaterials,
  };
};
