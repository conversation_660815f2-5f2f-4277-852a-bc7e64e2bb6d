import { FilterQuery } from "mongoose";

import { Product, ProductDocument } from "../models/product.model";
import {
  CreateProductDto,
  ProductFilters,
  UpdateProductDto,
} from "../types/product.types";
import { generateProductIdentifiers } from "../utils/product-generators";
import { CategoryService } from "./category.service";

/**
 * Service class for handling product-related business logic
 */
export class ProductService {
  private categoryService: CategoryService;

  constructor() {
    this.categoryService = new CategoryService();
  }
  /**
   * Create a new product
   * @param productData - The product data
   * @returns The created product
   */
  async createProduct(productData: CreateProductDto): Promise<ProductDocument> {
    try {
      // Clean up the product data before saving
      const cleanedData = { ...productData } as any;

      // Auto-generate SKU and barcode if not provided
      if (!cleanedData.sku || cleanedData.sku.trim() === "") {
        console.log("Auto-generating SKU and barcode for product...");

        const identifiers = await generateProductIdentifiers({
          brand: cleanedData.brand,
          category: cleanedData.category,
          productType: cleanedData.productType,
          name: cleanedData.name,
          price: cleanedData.price,
        });

        cleanedData.sku = identifiers.sku;
        cleanedData.barcode = identifiers.barcode;

        console.log(
          `Generated SKU: ${identifiers.sku}, Barcode: ${identifiers.barcode}`
        );
      }

      console.log("Creating product with cleaned data:", cleanedData);

      const product = new Product(cleanedData);
      const savedProduct = await product.save();

      // Update category product count
      if (savedProduct.category) {
        await this.categoryService.updateProductCount(savedProduct.category, 1);
        console.log(
          `Incremented product count for category: ${savedProduct.category}`
        );
      }

      return savedProduct;
    } catch (error) {
      throw this.handleError(error, "Error creating product");
    }
  }

  /**
   * Get a product by ID
   * @param id - The product ID
   * @returns The product or null if not found
   */
  async getProductById(id: string): Promise<ProductDocument | null> {
    try {
      return await Product.findById(id);
    } catch (error) {
      throw this.handleError(error, "Error fetching product");
    }
  }

  /**
   * Get products with optional filtering
   * @param filters - Optional filters for the query
   * @returns Array of products and total count
   */
  async getProducts(
    filters: ProductFilters = {}
  ): Promise<{ products: ProductDocument[]; total: number }> {
    try {
      console.info("Getting products with filters:", JSON.stringify(filters));

      // TEMPORARY FIX: Use a simple query first to check if products exist
      console.info(
        "STEP 1: Checking if any products exist with a simple query"
      );
      const allProducts = await Product.find({});
      console.info(`Simple query found ${allProducts.length} products`);

      if (allProducts.length > 0) {
        console.info("Sample product:", JSON.stringify(allProducts[0]));

        // Log the collection name
        console.info("Collection name:", Product.collection.name);
        console.info("Database name:", Product.db.name);
      } else {
        console.info("No products found in the database at all");
        return { products: [], total: 0 };
      }

      // If we have products, proceed with the filtered query
      console.info("STEP 2: Proceeding with filtered query");

      const {
        search,
        category,
        brand,
        minPrice,
        maxPrice,
        condition,
        inStock,
        tags,
        status,
        sortBy = "createdAt",
        sortOrder = "desc",
        page = 1,
        limit = 10,
      } = filters;

      // Build the filter query - but make it very simple for now
      const query: FilterQuery<ProductDocument> = {};

      // TEMPORARY: Only apply filters that are explicitly provided
      // This helps identify which filter might be causing the issue

      // Text search - disabled for now
      // if (search) {
      //   query.$text = { $search: search };
      // }

      // Category filter - only if explicitly provided
      if (category && category.trim() !== "") {
        query.category = category;
      }

      // Brand filter - only if explicitly provided
      if (brand && brand.trim() !== "") {
        query.brand = brand;
      }

      // Price range filter - only if explicitly provided
      if (minPrice !== undefined || maxPrice !== undefined) {
        query.price = {};
        if (minPrice !== undefined) {
          query.price.$gte = minPrice;
        }
        if (maxPrice !== undefined) {
          query.price.$lte = maxPrice;
        }
      }

      // Condition filter - only if explicitly provided
      if (condition && condition.trim() !== "") {
        query.condition = condition;
      }

      // In stock filter - only if explicitly provided as true
      if (inStock === true) {
        query.stock = { $gt: 0 };
      }

      // Tags filter - only if explicitly provided and not empty
      if (tags && tags.length > 0) {
        query.tags = { $in: tags };
      }

      // Status filter - only if explicitly provided
      if (status && status.trim() !== "") {
        query.status = status;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Build sort object
      const sort: Record<string, 1 | -1> = {
        [sortBy]: sortOrder === "asc" ? 1 : -1,
      };

      // Log the query for debugging
      console.info("MongoDB query:", JSON.stringify(query));
      console.info("Sort:", JSON.stringify(sort));
      console.info("Skip:", skip);
      console.info("Limit:", limit);

      // Execute query with pagination
      const products = await Product.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);

      // Get total count for pagination
      const total = await Product.countDocuments(query);

      console.info(`Found ${products.length} products out of ${total} total`);

      // TEMPORARY: If we still don't have products with the filtered query,
      // but we know products exist, return all products instead
      if (products.length === 0 && allProducts.length > 0) {
        console.info(
          "WARNING: Filtered query returned no results, returning all products instead"
        );
        return {
          products: allProducts.slice(0, limit),
          total: allProducts.length,
        };
      }

      return { products, total };
    } catch (error) {
      throw this.handleError(error, "Error fetching products");
    }
  }

  /**
   * Update a product
   * @param id - The product ID
   * @param updateData - The data to update
   * @returns The updated product
   */
  async updateProduct(
    id: string,
    updateData: UpdateProductDto
  ): Promise<ProductDocument | null> {
    try {
      // Get the current product to check for category changes
      const currentProduct = await Product.findById(id);
      if (!currentProduct) {
        return null;
      }

      // Update the product
      const updatedProduct = await Product.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      });

      // Handle category changes
      if (
        updateData.category &&
        currentProduct.category !== updateData.category
      ) {
        // Decrement count from old category
        if (currentProduct.category) {
          await this.categoryService.updateProductCount(
            currentProduct.category,
            -1
          );
          console.log(
            `Decremented product count for old category: ${currentProduct.category}`
          );
        }

        // Increment count for new category
        await this.categoryService.updateProductCount(updateData.category, 1);
        console.log(
          `Incremented product count for new category: ${updateData.category}`
        );
      }

      return updatedProduct;
    } catch (error) {
      throw this.handleError(error, "Error updating product");
    }
  }

  /**
   * Delete a product
   * @param id - The product ID
   * @returns True if deleted, false if not found
   */
  async deleteProduct(id: string): Promise<boolean> {
    try {
      // First get the product to know which category to update
      const product = await Product.findById(id);
      if (!product) {
        return false;
      }

      // Delete the product
      const result = await Product.findByIdAndDelete(id);

      // Update category product count if product was deleted
      if (result && product.category) {
        await this.categoryService.updateProductCount(product.category, -1);
        console.log(
          `Decremented product count for category: ${product.category}`
        );
      }

      return !!result;
    } catch (error) {
      throw this.handleError(error, "Error deleting product");
    }
  }

  /**
   * Handle errors in a consistent way
   * @param error - The error object
   * @param message - Custom error message
   * @returns A formatted error
   */
  private handleError(error: unknown, message: string): Error {
    console.error(`${message}:`, error);

    // Handle specific MongoDB/Mongoose errors
    if (error && typeof error === "object") {
      if (
        "name" in error &&
        error.name === "ValidationError" &&
        "errors" in error &&
        error.errors
      ) {
        const validationErrors = Object.values(
          error.errors as Record<string, { message: string }>
        )
          .map((err) => err.message)
          .join(", ");
        return new Error(`Validation error: ${validationErrors}`);
      }

      if ("name" in error && error.name === "CastError") {
        return new Error("Invalid ID format");
      }

      if ("code" in error && error.code === 11000) {
        // Handle duplicate key errors with more specific messages
        const errorMessage = error.toString();
        if (errorMessage.includes("sku")) {
          return new Error(
            "A product with this SKU already exists. Please use a different SKU or leave it empty."
          );
        }
        return new Error(
          "Duplicate key error: A product with these details already exists."
        );
      }
    }

    // If it's already an Error instance, return it
    if (error instanceof Error) {
      return error;
    }

    // Default case
    return new Error(message);
  }
}
