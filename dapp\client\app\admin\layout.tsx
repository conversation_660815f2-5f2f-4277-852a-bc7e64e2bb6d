import type { <PERSON>ada<PERSON> } from "next";

import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import { Toaster } from "sonner";

import AdminTemplate from "@/components/layout/Template";

import "./globals.css";
import "./styles.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Admin Dashboard",
  description: "Dapp admin dashboard",
};

export default function AdminLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" suppressHydrationWarning className="h-full bg-gray-50">
      <body
        className={`${geistSans.variable} ${geistMono.variable} h-full bg-gray-50 text-black antialiased dark:bg-gray-900 dark:text-white`}
      >
        <AdminTemplate>{children}</AdminTemplate>

        <Toaster />
      </body>
    </html>
  );
}
