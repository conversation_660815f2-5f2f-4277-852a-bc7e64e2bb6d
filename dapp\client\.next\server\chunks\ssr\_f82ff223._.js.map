{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersListActions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Plus, Download, RefreshCw } from \"lucide-react\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport const OrdersListActions = () => {\r\n  return (\r\n    <div className=\"flex gap-2\">\r\n      <Button variant=\"outline\" size=\"sm\">\r\n        <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n        Refresh\r\n      </Button>\r\n      \r\n      <Button variant=\"outline\" size=\"sm\">\r\n        <Download className=\"mr-2 h-4 w-4\" />\r\n        Export All\r\n      </Button>\r\n      \r\n      <Button size=\"sm\">\r\n        <Plus className=\"mr-2 h-4 w-4\" />\r\n        New Order\r\n      </Button>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAEA;AALA;;;;AAOO,MAAM,oBAAoB;IAC/B,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,2HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;;kCAC7B,6WAAC,oSAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIxC,6WAAC,2HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;;kCAC7B,6WAAC,8RAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIvC,6WAAC,2HAAA,CAAA,SAAM;gBAAC,MAAK;;kCACX,6WAAC,sRAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6WAAC,4QAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,4QAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersFilter.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Search, Filter, Calendar, Download } from \"lucide-react\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\n\r\nexport const OrdersFilter = () => {\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Search and Quick Actions */}\r\n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n        <div className=\"relative flex-1 max-w-md\">\r\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n          <Input\r\n            placeholder=\"Search orders by ID, customer name, or email...\"\r\n            className=\"pl-10\"\r\n          />\r\n        </div>\r\n        \r\n        <div className=\"flex gap-2\">\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <Calendar className=\"mr-2 h-4 w-4\" />\r\n            Date Range\r\n          </Button>\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <Download className=\"mr-2 h-4 w-4\" />\r\n            Export\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"flex flex-wrap gap-4\">\r\n        <Select defaultValue=\"all\">\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <Filter className=\"mr-2 h-4 w-4\" />\r\n            <SelectValue placeholder=\"Status\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Status</SelectItem>\r\n            <SelectItem value=\"pending\">Pending</SelectItem>\r\n            <SelectItem value=\"paid\">Paid</SelectItem>\r\n            <SelectItem value=\"shipped\">Shipped</SelectItem>\r\n            <SelectItem value=\"delivered\">Delivered</SelectItem>\r\n            <SelectItem value=\"cancelled\">Cancelled</SelectItem>\r\n            <SelectItem value=\"refunded\">Refunded</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select defaultValue=\"all\">\r\n          <SelectTrigger className=\"w-[160px]\">\r\n            <SelectValue placeholder=\"Shipping Status\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Shipping</SelectItem>\r\n            <SelectItem value=\"pending\">Pending</SelectItem>\r\n            <SelectItem value=\"processing\">Processing</SelectItem>\r\n            <SelectItem value=\"shipped\">Shipped</SelectItem>\r\n            <SelectItem value=\"delivered\">Delivered</SelectItem>\r\n            <SelectItem value=\"returned\">Returned</SelectItem>\r\n            <SelectItem value=\"cancelled\">Cancelled</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select defaultValue=\"newest\">\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <SelectValue placeholder=\"Sort by\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"newest\">Newest First</SelectItem>\r\n            <SelectItem value=\"oldest\">Oldest First</SelectItem>\r\n            <SelectItem value=\"amount-high\">Amount: High to Low</SelectItem>\r\n            <SelectItem value=\"amount-low\">Amount: Low to High</SelectItem>\r\n            <SelectItem value=\"customer\">Customer Name</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAPA;;;;;;AASO,MAAM,eAAe;IAC1B,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6WAAC,0HAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAId,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,6WAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6WAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,6WAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,2HAAA,CAAA,SAAM;wBAAC,cAAa;;0CACnB,6WAAC,2HAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC,2HAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;;0CAE3B,6WAAC,2HAAA,CAAA,gBAAa;;kDACZ,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAO;;;;;;kDACzB,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;;;;;;;;;;;;;kCAIjC,6WAAC,2HAAA,CAAA,SAAM;wBAAC,cAAa;;0CACnB,6WAAC,2HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6WAAC,2HAAA,CAAA,gBAAa;;kDACZ,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAa;;;;;;kDAC/B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;;;;;;;;;;;;;kCAIlC,6WAAC,2HAAA,CAAA,SAAM;wBAAC,cAAa;;0CACnB,6WAAC,2HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6WAAC,2HAAA,CAAA,gBAAa;;kDACZ,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAc;;;;;;kDAChC,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAa;;;;;;kDAC/B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/constants/products.ts"], "sourcesContent": ["export const products = [\r\n  {\r\n    id: 1,\r\n    name: \"T-shirt for Men\",\r\n    price: \"$90.00\",\r\n    originalPrice: \"$120.00\",\r\n    image: \"/images/t-shirt.jpg\",\r\n    category: \"Clothing\",\r\n    brand: \"Fashion Co\",\r\n    stock: 25,\r\n    status: \"active\" as const,\r\n    rating: 4,\r\n    isOnSale: true,\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"Travel Bag Jeans\",\r\n    price: \"$19.50\",\r\n    image: \"/images/jeans.jpg\",\r\n    category: \"Accessories\",\r\n    brand: \"Travel Pro\",\r\n    stock: 0,\r\n    status: \"active\" as const,\r\n    rating: 3,\r\n    isOnSale: false,\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"Jeans shorts\",\r\n    price: \"$70.00\",\r\n    image: \"/images/shorts.jpg\",\r\n    category: \"Clothing\",\r\n    brand: \"Denim Works\",\r\n    stock: 8,\r\n    status: \"active\" as const,\r\n    rating: 5,\r\n    isOnSale: false,\r\n  },\r\n  {\r\n    id: 4,\r\n    name: \"Sofa for interior\",\r\n    price: \"$375.00\",\r\n    originalPrice: \"$450.00\",\r\n    image: \"/images/sofa.jpg\",\r\n    category: \"Furniture\",\r\n    brand: \"Home Style\",\r\n    stock: 3,\r\n    status: \"active\" as const,\r\n    rating: 4,\r\n    isOnSale: true,\r\n  },\r\n  {\r\n    id: 5,\r\n    name: \"Leather Wallet\",\r\n    price: \"$375.00\",\r\n    image: \"/images/wallet.jpg\",\r\n    category: \"Accessories\",\r\n    brand: \"Leather Craft\",\r\n    stock: 15,\r\n    status: \"draft\" as const,\r\n    rating: 0,\r\n    isOnSale: false,\r\n  },\r\n  {\r\n    id: 6,\r\n    name: \"Warm Hat\",\r\n    price: \"$45.00\",\r\n    image: \"/images/hat.jpg\",\r\n    category: \"Clothing\",\r\n    brand: \"Winter Wear\",\r\n    stock: 50,\r\n    status: \"active\" as const,\r\n    rating: 4,\r\n    isOnSale: false,\r\n  },\r\n  {\r\n    id: 7,\r\n    name: \"Winter Jacket\",\r\n    price: \"$120.00\",\r\n    originalPrice: \"$180.00\",\r\n    image: \"/images/winter-jacket.jpg\",\r\n    category: \"Clothing\",\r\n    brand: \"Winter Wear\",\r\n    stock: 12,\r\n    status: \"active\" as const,\r\n    rating: 5,\r\n    isOnSale: true,\r\n  },\r\n  {\r\n    id: 8,\r\n    name: \"Office Chair\",\r\n    price: \"$210.00\",\r\n    image: \"/images/office-chair.jpg\",\r\n    category: \"Furniture\",\r\n    brand: \"Office Pro\",\r\n    stock: 7,\r\n    status: \"archived\" as const,\r\n    rating: 3,\r\n    isOnSale: false,\r\n  },\r\n  {\r\n    id: 9,\r\n    name: \"Running Shoes\",\r\n    price: \"$89.99\",\r\n    image: \"/images/running-shoes.jpg\",\r\n    category: \"Footwear\",\r\n    brand: \"Sport Pro\",\r\n    stock: 20,\r\n    status: \"active\" as const,\r\n    rating: 5,\r\n    isOnSale: false,\r\n  },\r\n  {\r\n    id: 10,\r\n    name: \"Smart Watch\",\r\n    price: \"$199.00\",\r\n    originalPrice: \"$249.00\",\r\n    image: \"/images/smart-watch.jpg\",\r\n    category: \"Electronics\",\r\n    brand: \"Tech Gear\",\r\n    stock: 2,\r\n    status: \"active\" as const,\r\n    rating: 4,\r\n    isOnSale: true,\r\n  },\r\n  {\r\n    id: 11,\r\n    name: \"Sunglasses\",\r\n    price: \"$59.00\",\r\n    image: \"/images/sunglasses.jpg\",\r\n    category: \"Accessories\",\r\n    brand: \"Style Co\",\r\n    stock: 18,\r\n    status: \"active\" as const,\r\n    rating: 3,\r\n    isOnSale: false,\r\n  },\r\n  {\r\n    id: 12,\r\n    name: \"Backpack\",\r\n    price: \"$85.50\",\r\n    image: \"/images/backpack.jpg\",\r\n    category: \"Accessories\",\r\n    brand: \"Travel Pro\",\r\n    stock: 35,\r\n    status: \"active\" as const,\r\n    rating: 4,\r\n    isOnSale: false,\r\n  },\r\n];\r\n\r\nexport const orderProducts = [\r\n  {\r\n    imageSrc: \"/images/backpack.jpg\",\r\n    name: \"Backpack\",\r\n    quantity: 2,\r\n    unitPrice: \"$43.50\",\r\n    total: \"$87.00\",\r\n  },\r\n  {\r\n    imageSrc: \"/images/smart-watch.jpg\",\r\n    name: \"Smart Watch\",\r\n    quantity: 1,\r\n    unitPrice: \"$43.50\",\r\n    total: \"$87.00\",\r\n  },\r\n  {\r\n    imageSrc: \"/images/sunglasses.jpg\",\r\n    name: \"Sunglasses\",\r\n    quantity: 1,\r\n    unitPrice: \"$43.50\",\r\n    total: \"$87.00\",\r\n  },\r\n  {\r\n    imageSrc: \"/images/running-shoes.jpg\",\r\n    name: \"Running Shoes\",\r\n    quantity: 1,\r\n    unitPrice: \"$81.50\",\r\n    total: \"$87.00\",\r\n  },\r\n];\r\n\r\n// Mock orders data for the orders list\r\nexport const mockOrders = [\r\n  {\r\n    id: \"ORD-001\",\r\n    customer: {\r\n      name: \"John Doe\",\r\n      email: \"<EMAIL>\",\r\n      avatar: \"/images/avatar-1.jpg\",\r\n    },\r\n    items: [\r\n      { name: \"Backpack\", quantity: 2, image: \"/images/backpack.jpg\" },\r\n      { name: \"Smart Watch\", quantity: 1, image: \"/images/smart-watch.jpg\" },\r\n    ],\r\n    totalAmount: 299.99,\r\n    status: \"paid\",\r\n    shippingStatus: \"shipped\",\r\n    placedAt: \"2024-01-15T10:30:00Z\",\r\n    updatedAt: \"2024-01-16T14:20:00Z\",\r\n  },\r\n  {\r\n    id: \"ORD-002\",\r\n    customer: {\r\n      name: \"Jane Smith\",\r\n      email: \"<EMAIL>\",\r\n      avatar: \"/images/avatar-2.jpg\",\r\n    },\r\n    items: [\r\n      { name: \"T-shirt for Men\", quantity: 3, image: \"/images/t-shirt.jpg\" },\r\n    ],\r\n    totalAmount: 270.0,\r\n    status: \"pending\",\r\n    shippingStatus: \"pending\",\r\n    placedAt: \"2024-01-14T16:45:00Z\",\r\n    updatedAt: \"2024-01-14T16:45:00Z\",\r\n  },\r\n  {\r\n    id: \"ORD-003\",\r\n    customer: {\r\n      name: \"Mike Johnson\",\r\n      email: \"<EMAIL>\",\r\n      avatar: \"/images/avatar-3.jpg\",\r\n    },\r\n    items: [\r\n      { name: \"Sofa for interior\", quantity: 1, image: \"/images/sofa.jpg\" },\r\n      { name: \"Office Chair\", quantity: 2, image: \"/images/office-chair.jpg\" },\r\n    ],\r\n    totalAmount: 795.0,\r\n    status: \"delivered\",\r\n    shippingStatus: \"delivered\",\r\n    placedAt: \"2024-01-12T09:15:00Z\",\r\n    updatedAt: \"2024-01-18T11:30:00Z\",\r\n  },\r\n  {\r\n    id: \"ORD-004\",\r\n    customer: {\r\n      name: \"Sarah Wilson\",\r\n      email: \"<EMAIL>\",\r\n      avatar: \"/images/avatar-4.jpg\",\r\n    },\r\n    items: [\r\n      { name: \"Leather Wallet\", quantity: 1, image: \"/images/wallet.jpg\" },\r\n      { name: \"Warm Hat\", quantity: 2, image: \"/images/hat.jpg\" },\r\n    ],\r\n    totalAmount: 465.0,\r\n    status: \"shipped\",\r\n    shippingStatus: \"shipped\",\r\n    placedAt: \"2024-01-13T14:20:00Z\",\r\n    updatedAt: \"2024-01-17T08:45:00Z\",\r\n  },\r\n  {\r\n    id: \"ORD-005\",\r\n    customer: {\r\n      name: \"David Brown\",\r\n      email: \"<EMAIL>\",\r\n      avatar: \"/images/avatar-5.jpg\",\r\n    },\r\n    items: [\r\n      {\r\n        name: \"Winter Jacket\",\r\n        quantity: 1,\r\n        image: \"/images/winter-jacket.jpg\",\r\n      },\r\n      { name: \"Jeans shorts\", quantity: 2, image: \"/images/shorts.jpg\" },\r\n    ],\r\n    totalAmount: 260.0,\r\n    status: \"cancelled\",\r\n    shippingStatus: \"cancelled\",\r\n    placedAt: \"2024-01-11T12:00:00Z\",\r\n    updatedAt: \"2024-01-12T10:15:00Z\",\r\n  },\r\n  {\r\n    id: \"ORD-006\",\r\n    customer: {\r\n      name: \"Emily Davis\",\r\n      email: \"<EMAIL>\",\r\n      avatar: \"/images/avatar-6.jpg\",\r\n    },\r\n    items: [\r\n      {\r\n        name: \"Running Shoes\",\r\n        quantity: 1,\r\n        image: \"/images/running-shoes.jpg\",\r\n      },\r\n      { name: \"Travel Bag Jeans\", quantity: 1, image: \"/images/jeans.jpg\" },\r\n    ],\r\n    totalAmount: 101.5,\r\n    status: \"refunded\",\r\n    shippingStatus: \"returned\",\r\n    placedAt: \"2024-01-10T15:30:00Z\",\r\n    updatedAt: \"2024-01-19T13:20:00Z\",\r\n  },\r\n];\r\n\r\n// Mock customers data for the customers list\r\nexport const mockCustomers = [\r\n  {\r\n    id: \"CUST-001\",\r\n    name: \"John Doe\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"/images/avatar-1.jpg\",\r\n    phone: \"+****************\",\r\n    location: \"New York, NY\",\r\n    joinedAt: \"2023-08-15T10:30:00Z\",\r\n    lastOrderAt: \"2024-01-15T14:20:00Z\",\r\n    totalOrders: 12,\r\n    totalSpent: 2450.75,\r\n    status: \"active\",\r\n    customerType: \"premium\",\r\n    tags: [\"vip\", \"frequent-buyer\"],\r\n  },\r\n  {\r\n    id: \"CUST-002\",\r\n    name: \"Jane Smith\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"/images/avatar-2.jpg\",\r\n    phone: \"+****************\",\r\n    location: \"Los Angeles, CA\",\r\n    joinedAt: \"2023-11-22T16:45:00Z\",\r\n    lastOrderAt: \"2024-01-14T11:30:00Z\",\r\n    totalOrders: 8,\r\n    totalSpent: 1680.5,\r\n    status: \"active\",\r\n    customerType: \"regular\",\r\n    tags: [\"loyal\"],\r\n  },\r\n  {\r\n    id: \"CUST-003\",\r\n    name: \"Mike Johnson\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"/images/avatar-3.jpg\",\r\n    phone: \"+****************\",\r\n    location: \"Chicago, IL\",\r\n    joinedAt: \"2023-05-10T09:15:00Z\",\r\n    lastOrderAt: \"2024-01-18T16:45:00Z\",\r\n    totalOrders: 25,\r\n    totalSpent: 4890.25,\r\n    status: \"active\",\r\n    customerType: \"vip\",\r\n    tags: [\"vip\", \"bulk-buyer\", \"corporate\"],\r\n  },\r\n  {\r\n    id: \"CUST-004\",\r\n    name: \"Sarah Wilson\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"/images/avatar-4.jpg\",\r\n    phone: \"+****************\",\r\n    location: \"Miami, FL\",\r\n    joinedAt: \"2023-12-05T14:20:00Z\",\r\n    lastOrderAt: \"2024-01-17T09:15:00Z\",\r\n    totalOrders: 5,\r\n    totalSpent: 890.0,\r\n    status: \"active\",\r\n    customerType: \"regular\",\r\n    tags: [\"new-customer\"],\r\n  },\r\n  {\r\n    id: \"CUST-005\",\r\n    name: \"David Brown\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"/images/avatar-5.jpg\",\r\n    phone: \"+****************\",\r\n    location: \"Seattle, WA\",\r\n    joinedAt: \"2023-03-18T12:00:00Z\",\r\n    lastOrderAt: \"2023-12-20T15:30:00Z\",\r\n    totalOrders: 3,\r\n    totalSpent: 245.5,\r\n    status: \"inactive\",\r\n    customerType: \"regular\",\r\n    tags: [\"at-risk\"],\r\n  },\r\n  {\r\n    id: \"CUST-006\",\r\n    name: \"Emily Davis\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"/images/avatar-6.jpg\",\r\n    phone: \"+****************\",\r\n    location: \"Austin, TX\",\r\n    joinedAt: \"2024-01-08T15:30:00Z\",\r\n    lastOrderAt: \"2024-01-19T10:45:00Z\",\r\n    totalOrders: 2,\r\n    totalSpent: 156.75,\r\n    status: \"active\",\r\n    customerType: \"regular\",\r\n    tags: [\"new-customer\"],\r\n  },\r\n  {\r\n    id: \"CUST-007\",\r\n    name: \"Robert Garcia\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"/images/avatar-7.jpg\",\r\n    phone: \"+****************\",\r\n    location: \"Phoenix, AZ\",\r\n    joinedAt: \"2023-07-12T11:20:00Z\",\r\n    lastOrderAt: \"2024-01-16T13:25:00Z\",\r\n    totalOrders: 15,\r\n    totalSpent: 3250.8,\r\n    status: \"active\",\r\n    customerType: \"premium\",\r\n    tags: [\"loyal\", \"frequent-buyer\"],\r\n  },\r\n  {\r\n    id: \"CUST-008\",\r\n    name: \"Lisa Anderson\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"/images/avatar-8.jpg\",\r\n    phone: \"+****************\",\r\n    location: \"Denver, CO\",\r\n    joinedAt: \"2023-09-25T08:45:00Z\",\r\n    lastOrderAt: null,\r\n    totalOrders: 0,\r\n    totalSpent: 0,\r\n    status: \"inactive\",\r\n    customerType: \"regular\",\r\n    tags: [\"never-purchased\"],\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,WAAW;IACtB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,eAAe;QACf,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;IACZ;CACD;AAEM,MAAM,gBAAgB;IAC3B;QACE,UAAU;QACV,MAAM;QACN,UAAU;QACV,WAAW;QACX,OAAO;IACT;IACA;QACE,UAAU;QACV,MAAM;QACN,UAAU;QACV,WAAW;QACX,OAAO;IACT;IACA;QACE,UAAU;QACV,MAAM;QACN,UAAU;QACV,WAAW;QACX,OAAO;IACT;IACA;QACE,UAAU;QACV,MAAM;QACN,UAAU;QACV,WAAW;QACX,OAAO;IACT;CACD;AAGM,MAAM,aAAa;IACxB;QACE,IAAI;QACJ,UAAU;YACR,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,OAAO;YACL;gBAAE,MAAM;gBAAY,UAAU;gBAAG,OAAO;YAAuB;YAC/D;gBAAE,MAAM;gBAAe,UAAU;gBAAG,OAAO;YAA0B;SACtE;QACD,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;YACR,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,OAAO;YACL;gBAAE,MAAM;gBAAmB,UAAU;gBAAG,OAAO;YAAsB;SACtE;QACD,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;YACR,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,OAAO;YACL;gBAAE,MAAM;gBAAqB,UAAU;gBAAG,OAAO;YAAmB;YACpE;gBAAE,MAAM;gBAAgB,UAAU;gBAAG,OAAO;YAA2B;SACxE;QACD,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;YACR,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,OAAO;YACL;gBAAE,MAAM;gBAAkB,UAAU;gBAAG,OAAO;YAAqB;YACnE;gBAAE,MAAM;gBAAY,UAAU;gBAAG,OAAO;YAAkB;SAC3D;QACD,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;YACR,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;YACT;YACA;gBAAE,MAAM;gBAAgB,UAAU;gBAAG,OAAO;YAAqB;SAClE;QACD,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;YACR,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;YACT;YACA;gBAAE,MAAM;gBAAoB,UAAU;gBAAG,OAAO;YAAoB;SACrE;QACD,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,UAAU;QACV,WAAW;IACb;CACD;AAGM,MAAM,gBAAgB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,MAAM;YAAC;YAAO;SAAiB;IACjC;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,MAAM;YAAC;SAAQ;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,MAAM;YAAC;YAAO;YAAc;SAAY;IAC1C;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,MAAM;YAAC;SAAe;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,MAAM;YAAC;SAAU;IACnB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,MAAM;YAAC;SAAe;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,MAAM;YAAC;YAAS;SAAiB;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,UAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,MAAM;YAAC;SAAkB;IAC3B;CACD", "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrderRow.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { format } from \"date-fns\";\r\nimport { Edit, Eye, MoreHorizontal, Trash2, User } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\ntype OrderRowProps = {\r\n  order: {\r\n    id: string;\r\n    customer: {\r\n      name: string;\r\n      email: string;\r\n      avatar: string;\r\n    };\r\n    items: Array<{\r\n      name: string;\r\n      quantity: number;\r\n      image: string;\r\n    }>;\r\n    totalAmount: number;\r\n    status: string;\r\n    shippingStatus: string;\r\n    placedAt: string;\r\n    updatedAt: string;\r\n  };\r\n};\r\n\r\nconst getStatusColor = (status: string) => {\r\n  switch (status) {\r\n    case \"paid\":\r\n      return \"bg-green-100 text-green-700 hover:bg-green-200\";\r\n    case \"pending\":\r\n      return \"bg-yellow-100 text-yellow-700 hover:bg-yellow-200\";\r\n    case \"shipped\":\r\n      return \"bg-blue-100 text-blue-700 hover:bg-blue-200\";\r\n    case \"delivered\":\r\n      return \"bg-emerald-100 text-emerald-700 hover:bg-emerald-200\";\r\n    case \"cancelled\":\r\n      return \"bg-red-100 text-red-700 hover:bg-red-200\";\r\n    case \"refunded\":\r\n      return \"bg-purple-100 text-purple-700 hover:bg-purple-200\";\r\n    default:\r\n      return \"bg-gray-100 text-gray-700 hover:bg-gray-200\";\r\n  }\r\n};\r\n\r\nconst getShippingStatusColor = (status: string) => {\r\n  switch (status) {\r\n    case \"shipped\":\r\n      return \"bg-blue-100 text-blue-700 hover:bg-blue-200\";\r\n    case \"delivered\":\r\n      return \"bg-green-100 text-green-700 hover:bg-green-200\";\r\n    case \"pending\":\r\n      return \"bg-yellow-100 text-yellow-700 hover:bg-yellow-200\";\r\n    case \"processing\":\r\n      return \"bg-orange-100 text-orange-700 hover:bg-orange-200\";\r\n    case \"returned\":\r\n      return \"bg-purple-100 text-purple-700 hover:bg-purple-200\";\r\n    case \"cancelled\":\r\n      return \"bg-red-100 text-red-700 hover:bg-red-200\";\r\n    default:\r\n      return \"bg-gray-100 text-gray-700 hover:bg-gray-200\";\r\n  }\r\n};\r\n\r\nexport const OrderRow = ({ order }: OrderRowProps) => {\r\n  const router = useRouter();\r\n  const totalItems = order.items.reduce((sum, item) => sum + item.quantity, 0);\r\n  const displayItems = order.items.slice(0, 3); // Show max 3 items\r\n  const remainingItems = order.items.length - 3;\r\n\r\n  const handleViewDetails = () => {\r\n    router.push(`/admin/orders/${order.id}`);\r\n  };\r\n\r\n  const handleEditOrder = () => {\r\n    // TODO: Implement edit functionality\r\n    console.log(\"Edit order:\", order.id);\r\n  };\r\n\r\n  const handleCancelOrder = () => {\r\n    // TODO: Implement cancel functionality\r\n    console.log(\"Cancel order:\", order.id);\r\n  };\r\n\r\n  return (\r\n    <tr\r\n      className=\"cursor-pointer border-t transition-colors hover:bg-gray-50/50\"\r\n      onClick={handleViewDetails}\r\n    >\r\n      {/* Order ID */}\r\n      <td className=\"p-4\" onClick={(e) => e.stopPropagation()}>\r\n        <Link\r\n          href={`/admin/orders/${order.id}`}\r\n          className=\"font-medium text-blue-600 hover:text-blue-800 hover:underline\"\r\n        >\r\n          #{order.id}\r\n        </Link>\r\n      </td>\r\n\r\n      {/* Customer */}\r\n      <td className=\"p-4\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-gray-100\">\r\n            <User className=\"h-4 w-4 text-gray-500\" />\r\n          </div>\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">\r\n              {order.customer.name}\r\n            </div>\r\n            <div className=\"text-sm text-gray-500\">{order.customer.email}</div>\r\n          </div>\r\n        </div>\r\n      </td>\r\n\r\n      {/* Products */}\r\n      <td className=\"p-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex -space-x-2\">\r\n            {displayItems.map((item, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"flex h-8 w-8 shrink-0 overflow-hidden rounded-full border-2 border-white bg-gray-100\"\r\n              >\r\n                <Image\r\n                  src={item.image}\r\n                  alt={item.name}\r\n                  width={32}\r\n                  height={32}\r\n                  className=\"object-cover\"\r\n                />\r\n              </div>\r\n            ))}\r\n            {remainingItems > 0 && (\r\n              <div className=\"flex h-8 w-8 shrink-0 items-center justify-center rounded-full border-2 border-white bg-gray-200 text-xs font-medium text-gray-600\">\r\n                +{remainingItems}\r\n              </div>\r\n            )}\r\n          </div>\r\n          <span className=\"text-sm text-gray-500\">\r\n            {totalItems} item{totalItems !== 1 ? \"s\" : \"\"}\r\n          </span>\r\n        </div>\r\n      </td>\r\n\r\n      {/* Status */}\r\n      <td className=\"p-4 text-center\">\r\n        <Badge\r\n          className={`cursor-default font-normal ${getStatusColor(order.status)}`}\r\n        >\r\n          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}\r\n        </Badge>\r\n      </td>\r\n\r\n      {/* Shipping Status */}\r\n      <td className=\"p-4 text-center\">\r\n        <Badge\r\n          className={`cursor-default font-normal ${getShippingStatusColor(order.shippingStatus)}`}\r\n        >\r\n          {order.shippingStatus.charAt(0).toUpperCase() +\r\n            order.shippingStatus.slice(1)}\r\n        </Badge>\r\n      </td>\r\n\r\n      {/* Total */}\r\n      <td className=\"p-4 text-right\">\r\n        <div className=\"font-medium text-gray-900\">\r\n          ${order.totalAmount.toFixed(2)}\r\n        </div>\r\n      </td>\r\n\r\n      {/* Date */}\r\n      <td className=\"p-4 text-center\">\r\n        <div className=\"text-sm text-gray-900\">\r\n          {format(new Date(order.placedAt), \"MMM dd, yyyy\")}\r\n        </div>\r\n        <div className=\"text-xs text-gray-500\">\r\n          {format(new Date(order.placedAt), \"HH:mm\")}\r\n        </div>\r\n      </td>\r\n\r\n      {/* Actions */}\r\n      <td className=\"p-4 text-center\" onClick={(e) => e.stopPropagation()}>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              <MoreHorizontal className=\"h-4 w-4\" />\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\">\r\n            <DropdownMenuItem onClick={handleViewDetails}>\r\n              <Eye className=\"mr-2 h-4 w-4\" />\r\n              View Details\r\n            </DropdownMenuItem>\r\n            <DropdownMenuItem onClick={handleEditOrder}>\r\n              <Edit className=\"mr-2 h-4 w-4\" />\r\n              Edit Order\r\n            </DropdownMenuItem>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem\r\n              className=\"text-red-600\"\r\n              onClick={handleCancelOrder}\r\n            >\r\n              <Trash2 className=\"mr-2 h-4 w-4\" />\r\n              Cancel Order\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </td>\r\n    </tr>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAZA;;;;;;;;;;AAyCA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,yBAAyB,CAAC;IAC9B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,WAAW,CAAC,EAAE,KAAK,EAAiB;IAC/C,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;IAC1E,MAAM,eAAe,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,mBAAmB;IACjE,MAAM,iBAAiB,MAAM,KAAK,CAAC,MAAM,GAAG;IAE5C,MAAM,oBAAoB;QACxB,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE;IACzC;IAEA,MAAM,kBAAkB;QACtB,qCAAqC;QACrC,QAAQ,GAAG,CAAC,eAAe,MAAM,EAAE;IACrC;IAEA,MAAM,oBAAoB;QACxB,uCAAuC;QACvC,QAAQ,GAAG,CAAC,iBAAiB,MAAM,EAAE;IACvC;IAEA,qBACE,6WAAC;QACC,WAAU;QACV,SAAS;;0BAGT,6WAAC;gBAAG,WAAU;gBAAM,SAAS,CAAC,IAAM,EAAE,eAAe;0BACnD,cAAA,6WAAC,2RAAA,CAAA,UAAI;oBACH,MAAM,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE;oBACjC,WAAU;;wBACX;wBACG,MAAM,EAAE;;;;;;;;;;;;0BAKd,6WAAC;gBAAG,WAAU;0BACZ,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6WAAC;;8CACC,6WAAC;oCAAI,WAAU;8CACZ,MAAM,QAAQ,CAAC,IAAI;;;;;;8CAEtB,6WAAC;oCAAI,WAAU;8CAAyB,MAAM,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMlE,6WAAC;gBAAG,WAAU;0BACZ,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;gCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6WAAC;wCAEC,WAAU;kDAEV,cAAA,6WAAC,4PAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,KAAK;4CACf,KAAK,KAAK,IAAI;4CACd,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;uCARP;;;;;gCAYR,iBAAiB,mBAChB,6WAAC;oCAAI,WAAU;;wCAAqI;wCAChJ;;;;;;;;;;;;;sCAIR,6WAAC;4BAAK,WAAU;;gCACb;gCAAW;gCAAM,eAAe,IAAI,MAAM;;;;;;;;;;;;;;;;;;0BAMjD,6WAAC;gBAAG,WAAU;0BACZ,cAAA,6WAAC,0HAAA,CAAA,QAAK;oBACJ,WAAW,CAAC,2BAA2B,EAAE,eAAe,MAAM,MAAM,GAAG;8BAEtE,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;0BAK/D,6WAAC;gBAAG,WAAU;0BACZ,cAAA,6WAAC,0HAAA,CAAA,QAAK;oBACJ,WAAW,CAAC,2BAA2B,EAAE,uBAAuB,MAAM,cAAc,GAAG;8BAEtF,MAAM,cAAc,CAAC,MAAM,CAAC,GAAG,WAAW,KACzC,MAAM,cAAc,CAAC,KAAK,CAAC;;;;;;;;;;;0BAKjC,6WAAC;gBAAG,WAAU;0BACZ,cAAA,6WAAC;oBAAI,WAAU;;wBAA4B;wBACvC,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0BAKhC,6WAAC;gBAAG,WAAU;;kCACZ,6WAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,QAAQ,GAAG;;;;;;kCAEpC,6WAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,QAAQ,GAAG;;;;;;;;;;;;0BAKtC,6WAAC;gBAAG,WAAU;gBAAkB,SAAS,CAAC,IAAM,EAAE,eAAe;0BAC/D,cAAA,6WAAC,qIAAA,CAAA,eAAY;;sCACX,6WAAC,qIAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,6WAAC,oSAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG9B,6WAAC,qIAAA,CAAA,sBAAmB;4BAAC,OAAM;;8CACzB,6WAAC,qIAAA,CAAA,mBAAgB;oCAAC,SAAS;;sDACzB,6WAAC,oRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGlC,6WAAC,qIAAA,CAAA,mBAAgB;oCAAC,SAAS;;sDACzB,6WAAC,+RAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;8CACtB,6WAAC,qIAAA,CAAA,mBAAgB;oCACf,WAAU;oCACV,SAAS;;sDAET,6WAAC,8RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersTable.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { More<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Edit, Trash2 } from \"lucide-react\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { mockOrders } from \"@/constants/products\";\r\n\r\nimport { OrderRow } from \"./OrderRow\";\r\n\r\nexport const OrdersTable = () => {\r\n  return (\r\n    <div className=\"rounded-lg border border-gray-200 bg-white\">\r\n      <div className=\"overflow-x-auto\">\r\n        <table className=\"w-full text-sm\">\r\n          <thead>\r\n            <tr className=\"border-b bg-gray-50/50 text-gray-500\">\r\n              <th className=\"p-4 text-left font-medium\">Order</th>\r\n              <th className=\"p-4 text-left font-medium\">Customer</th>\r\n              <th className=\"p-4 text-left font-medium\">Products</th>\r\n              <th className=\"p-4 text-center font-medium\">Status</th>\r\n              <th className=\"p-4 text-center font-medium\">Shipping</th>\r\n              <th className=\"p-4 text-right font-medium\">Total</th>\r\n              <th className=\"p-4 text-center font-medium\">Date</th>\r\n              <th className=\"p-4 text-center font-medium\">Actions</th>\r\n            </tr>\r\n          </thead>\r\n\r\n          <tbody>\r\n            {mockOrders.map((order) => (\r\n              <OrderRow key={order.id} order={order} />\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Empty state - show when no orders */}\r\n      {mockOrders.length === 0 && (\r\n        <div className=\"flex flex-col items-center justify-center py-12 text-center\">\r\n          <div className=\"mb-4 rounded-full bg-gray-100 p-3\">\r\n            <Eye className=\"h-6 w-6 text-gray-400\" />\r\n          </div>\r\n          <h3 className=\"mb-2 text-lg font-medium text-gray-900\">No orders found</h3>\r\n          <p className=\"text-gray-500\">\r\n            No orders match your current filters. Try adjusting your search criteria.\r\n          </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAUA;AAEA;AAfA;;;;;AAiBO,MAAM,cAAc;IACzB,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAM,WAAU;;sCACf,6WAAC;sCACC,cAAA,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6WAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6WAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6WAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6WAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6WAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6WAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6WAAC;wCAAG,WAAU;kDAA8B;;;;;;;;;;;;;;;;;sCAIhD,6WAAC;sCACE,qHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,sBACf,6WAAC,kJAAA,CAAA,WAAQ;oCAAgB,OAAO;mCAAjB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;YAO9B,qHAAA,CAAA,aAAU,CAAC,MAAM,KAAK,mBACrB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,oRAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,6WAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6WAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 1867, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersPagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Chevron<PERSON>eft, ChevronRight } from \"lucide-react\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\n\r\nexport const OrdersPagination = () => {\r\n  return (\r\n    <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n      {/* Results info */}\r\n      <div className=\"text-sm text-gray-500\">\r\n        Showing <span className=\"font-medium text-gray-900\">1-10</span> of{\" \"}\r\n        <span className=\"font-medium text-gray-900\">97</span> orders\r\n      </div>\r\n\r\n      {/* Pagination controls */}\r\n      <div className=\"flex items-center gap-4\">\r\n        {/* Items per page */}\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-gray-500\">Show:</span>\r\n          <Select defaultValue=\"10\">\r\n            <SelectTrigger className=\"w-[70px]\">\r\n              <SelectValue />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"10\">10</SelectItem>\r\n              <SelectItem value=\"25\">25</SelectItem>\r\n              <SelectItem value=\"50\">50</SelectItem>\r\n              <SelectItem value=\"100\">100</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n\r\n        {/* Page navigation */}\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button variant=\"outline\" size=\"sm\" disabled>\r\n            <ChevronLeft className=\"h-4 w-4\" />\r\n            Previous\r\n          </Button>\r\n\r\n          {/* Page numbers */}\r\n          <div className=\"flex items-center gap-1\">\r\n            <Button variant=\"default\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              1\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              2\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              3\r\n            </Button>\r\n            <span className=\"px-2 text-sm text-gray-500\">...</span>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              10\r\n            </Button>\r\n          </div>\r\n\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            Next\r\n            <ChevronRight className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAEA;AACA;AANA;;;;;AAQO,MAAM,mBAAmB;IAC9B,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;oBAAwB;kCAC7B,6WAAC;wBAAK,WAAU;kCAA4B;;;;;;oBAAW;oBAAI;kCACnE,6WAAC;wBAAK,WAAU;kCAA4B;;;;;;oBAAS;;;;;;;0BAIvD,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6WAAC,2HAAA,CAAA,SAAM;gCAAC,cAAa;;kDACnB,6WAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6WAAC,2HAAA,CAAA,gBAAa;;0DACZ,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,QAAQ;;kDAC1C,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAKrC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,6WAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAK9D,6WAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;oCAAK;kDAElC,6WAAC,0SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC", "debugId": null}}, {"offset": {"line": 2116, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersListWrapper.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\n\r\nimport { OrdersFilter } from \"./OrdersFilter\";\r\nimport { OrdersTable } from \"./OrdersTable\";\r\nimport { OrdersPagination } from \"./OrdersPagination\";\r\n\r\nexport const OrdersListWrapper = () => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardContent className=\"p-6\">\r\n          <OrdersFilter />\r\n\r\n          <Separator className=\"my-3 mb-6\" />\r\n\r\n          <OrdersTable />\r\n        </CardContent>\r\n      </Card>\r\n\r\n      <OrdersPagination />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAEA;AACA;AACA;AATA;;;;;;;AAWO,MAAM,oBAAoB;IAC/B,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,sJAAA,CAAA,eAAY;;;;;sCAEb,6WAAC,8HAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCAErB,6WAAC,qJAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0BAIhB,6WAAC,0JAAA,CAAA,mBAAgB;;;;;;;;;;;AAGvB", "debugId": null}}]}