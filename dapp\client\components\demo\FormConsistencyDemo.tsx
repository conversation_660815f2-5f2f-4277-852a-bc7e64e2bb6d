"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { EmojiPicker } from "@/components/ui/emoji-picker";

/**
 * Demo component to showcase form consistency improvements
 * This demonstrates how the EmojiPicker now matches Input styling
 */
export const FormConsistencyDemo = () => {
  const [formData, setFormData] = useState({
    name: "",
    icon: "",
    description: "",
    color: "#3B82F6",
  });

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Form Consistency Demo</CardTitle>
          <p className="text-sm text-muted-foreground">
            Notice how all form elements now have consistent styling and background colors
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Name Input */}
          <div>
            <Label htmlFor="demo-name">Category Name</Label>
            <Input
              id="demo-name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g. Electronics"
              className="mt-1"
            />
          </div>

          {/* Icon Picker - Now matches Input styling */}
          <div>
            <EmojiPicker
              value={formData.icon}
              onChange={(icon) => setFormData({ ...formData, icon })}
              placeholder="Pick an icon for this category"
            />
          </div>

          {/* Description Textarea */}
          <div>
            <Label htmlFor="demo-description">Description</Label>
            <Textarea
              id="demo-description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Describe what products belong in this category..."
              className="mt-1"
              rows={3}
            />
          </div>

          {/* Color Input */}
          <div>
            <Label htmlFor="demo-color">Color</Label>
            <Input
              id="demo-color"
              type="color"
              value={formData.color}
              onChange={(e) => setFormData({ ...formData, color: e.target.value })}
              className="mt-1 h-10"
            />
          </div>

          {/* Preview */}
          <div className="mt-6 p-4 border rounded-lg bg-muted/50">
            <h3 className="font-medium mb-2">Preview:</h3>
            <div className="flex items-center gap-3">
              {formData.icon && <span className="text-2xl">{formData.icon}</span>}
              <div>
                <div className="font-medium">{formData.name || "Category Name"}</div>
                <div className="text-sm text-muted-foreground">
                  {formData.description || "Category description"}
                </div>
              </div>
              <div 
                className="ml-auto w-4 h-4 rounded-full border"
                style={{ backgroundColor: formData.color }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Comparison Card */}
      <Card>
        <CardHeader>
          <CardTitle>Styling Improvements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-green-600 mb-2">✅ After (Consistent)</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Same background color for all inputs</li>
                <li>• Consistent border styling</li>
                <li>• Matching height and padding</li>
                <li>• Unified hover states</li>
                <li>• Proper focus indicators</li>
                <li>• Smooth transitions</li>
                <li>• Keyboard accessibility</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-red-600 mb-2">❌ Before (Inconsistent)</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Button styling for icon picker</li>
                <li>• Different background colors</li>
                <li>• Inconsistent hover effects</li>
                <li>• Mismatched visual hierarchy</li>
                <li>• Poor user experience</li>
                <li>• Confusing interface</li>
                <li>• Accessibility issues</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
