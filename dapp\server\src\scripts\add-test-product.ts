import mongoose from 'mongoose';
import { config } from '../config/load-env.config';
import { Product } from '../models/product.model';

/**
 * <PERSON><PERSON>t to add a test product to the database
 */
async function addTestProduct() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(config.db.connection, {
      dbName: config.db.name,
      retryWrites: true,
      w: 'majority',
    });
    console.log('Connected to MongoDB');

    // Create a test product
    const testProduct = {
      name: 'Test Product',
      brand: 'Test Brand',
      description: 'This is a test product created via script',
      price: 99.99,
      currency: 'EUR',
      stock: 10,
      condition: 'new',
      category: 'Test Category',
      mainImage: 'https://via.placeholder.com/500',
      status: 'published',
      ageRestriction: 'none',
      isPublished: true,
      freeShipping: true,
      tags: ['test', 'sample', 'demo'],
    };

    // Save the product to the database
    console.log('Creating test product...');
    const product = new Product(testProduct);
    const savedProduct = await product.save();
    
    console.log('Test product created successfully:');
    console.log(JSON.stringify(savedProduct, null, 2));
  } catch (error) {
    console.error('Error adding test product:', error);
  } finally {
    // Close the database connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
addTestProduct();
