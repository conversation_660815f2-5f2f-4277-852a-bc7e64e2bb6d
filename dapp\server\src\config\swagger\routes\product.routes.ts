/**
 * @swagger
 * tags:
 *   name: Products
 *   description: Product management endpoints
 */

/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: Get all products
 *     description: Retrieve a list of products with optional filtering
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for product name, description, etc.
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: Filter by brand
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price
 *       - in: query
 *         name: condition
 *         schema:
 *           type: string
 *           enum: [new, like-new, excellent, good, fair, used, refurbished, vintage, antique, damaged]
 *         description: Filter by condition
 *       - in: query
 *         name: inStock
 *         schema:
 *           type: boolean
 *         description: Filter by stock availability
 *       - in: query
 *         name: tags
 *         schema:
 *           type: string
 *         description: Filter by tags (comma-separated)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [in-stock, out-of-stock, coming-soon, archived, draft, suspended]
 *         description: Filter by status
 *       - in: query
 *         name: productType
 *         schema:
 *           type: string
 *           enum: [physical, digital, service, subscription, bundle]
 *         description: Filter by product type
 *       - in: query
 *         name: visibility
 *         schema:
 *           type: string
 *           enum: [public, private, hidden, password-protected]
 *         description: Filter by visibility
 *       - in: query
 *         name: subcategory
 *         schema:
 *           type: string
 *         description: Filter by subcategory
 *       - in: query
 *         name: stockManagement
 *         schema:
 *           type: string
 *           enum: [track, no-track, backorder]
 *         description: Filter by stock management type
 *       - in: query
 *         name: taxStatus
 *         schema:
 *           type: string
 *           enum: [taxable, tax-exempt, shipping-only]
 *         description: Filter by tax status
 *       - in: query
 *         name: shippingClass
 *         schema:
 *           type: string
 *           enum: [standard, express, overnight, international, heavy, fragile, digital-only]
 *         description: Filter by shipping class
 *       - in: query
 *         name: featured
 *         schema:
 *           type: boolean
 *         description: Filter by featured status
 *       - in: query
 *         name: downloadable
 *         schema:
 *           type: boolean
 *         description: Filter by downloadable status
 *       - in: query
 *         name: virtual
 *         schema:
 *           type: boolean
 *         description: Filter by virtual status
 *       - in: query
 *         name: sellerId
 *         schema:
 *           type: string
 *         description: Filter by seller ID
 *       - in: query
 *         name: shopId
 *         schema:
 *           type: string
 *         description: Filter by shop ID
 *       - in: query
 *         name: color
 *         schema:
 *           type: string
 *         description: Filter by color
 *       - in: query
 *         name: size
 *         schema:
 *           type: string
 *         description: Filter by size
 *       - in: query
 *         name: origin
 *         schema:
 *           type: string
 *         description: Filter by country of origin
 *       - in: query
 *         name: hasWarranty
 *         schema:
 *           type: boolean
 *         description: Filter by warranty availability
 *       - in: query
 *         name: returnable
 *         schema:
 *           type: boolean
 *         description: Filter by return policy
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [price, createdAt, name, views, purchases, averageRating, stock, featured]
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort order
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: A list of products
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 meta:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: number
 *                       example: 100
 *                     page:
 *                       type: number
 *                       example: 1
 *                     limit:
 *                       type: number
 *                       example: 10
 *                     pages:
 *                       type: number
 *                       example: 10
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 *
 *   post:
 *     summary: Create a new product
 *     description: Add a new product to the database
 *     tags: [Products]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateProductDto'
 *           examples:
 *             minimal:
 *               summary: "Minimal required fields"
 *               value:
 *                 name: "iPhone 15 Pro Max"
 *                 brand: "Apple"
 *                 description: "Latest iPhone with advanced camera system and A17 Pro chip"
 *                 price: 1199.99
 *                 currency: "EUR"
 *                 stock: 50
 *                 condition: "new"
 *                 category: "Electronics"
 *                 mainImage: "https://example.com/images/iphone-15-pro-max.jpg"
 *                 productType: "physical"
 *                 visibility: "public"
 *                 taxStatus: "taxable"
 *                 stockManagement: "track"
 *                 status: "in-stock"
 *             complete:
 *               summary: "Complete product with optional fields"
 *               value:
 *                 name: "iPhone 15 Pro Max"
 *                 brand: "Apple"
 *                 description: "Latest iPhone with advanced camera system and A17 Pro chip"
 *                 price: 1199.99
 *                 currency: "EUR"
 *                 stock: 50
 *                 condition: "new"
 *                 category: "Electronics"
 *                 mainImage: "https://example.com/images/iphone-15-pro-max.jpg"
 *                 productType: "physical"
 *                 visibility: "public"
 *                 taxStatus: "taxable"
 *                 stockManagement: "track"
 *                 status: "in-stock"
 *                 slug: "iphone-15-pro-max"
 *                 model: "A3108"
 *                 shortDescription: "Pro camera system. Pro performance."
 *                 sku: "IPHONE15PM-256-BLUE"
 *                 originalPrice: 1299.99
 *                 yearMade: 2023
 *                 tags: ["smartphone", "apple", "5g", "pro"]
 *                 color: "Natural Titanium"
 *                 size: "6.7 inch"
 *                 images: ["https://example.com/images/iphone-15-pro-max-2.jpg"]
 *                 location: "Cupertino, CA"
 *                 shippingCost: 0
 *                 shippingTime: "1-2 business days"
 *                 freeShipping: true
 *     responses:
 *       201:
 *         description: Product created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */

/**
 * @swagger
 * /api/products/{id}:
 *   get:
 *     summary: Get a product by ID
 *     description: Retrieve a specific product by its ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Product details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 *
 *   put:
 *     summary: Update a product
 *     description: Update an existing product by its ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Product ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateProductDto'
 *           examples:
 *             basic_update:
 *               summary: "Basic field updates"
 *               value:
 *                 name: "iPhone 15 Pro Max - Updated"
 *                 price: 1099.99
 *                 stock: 25
 *                 description: "Updated description with new features"
 *             price_update:
 *               summary: "Price and sale update"
 *               value:
 *                 price: 999.99
 *                 originalPrice: 1199.99
 *                 saleEndsAt: "2024-12-31T23:59:59Z"
 *             inventory_update:
 *               summary: "Inventory management update"
 *               value:
 *                 stock: 100
 *                 lowStockThreshold: 10
 *                 status: "in-stock"
 *                 trackQuantity: true
 *             complete_update:
 *               summary: "Multiple field update"
 *               value:
 *                 name: "iPhone 15 Pro Max - Special Edition"
 *                 price: 1299.99
 *                 stock: 15
 *                 condition: "new"
 *                 tags: ["smartphone", "apple", "5g", "pro", "special-edition"]
 *                 color: "Deep Purple"
 *                 featured: true
 *                 yearMade: 2024
 *     responses:
 *       200:
 *         description: Product updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 *
 *   delete:
 *     summary: Delete a product
 *     description: Delete a product by its ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Product deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Product deleted successfully
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 *
 * /api/products/{id}/edit:
 *   get:
 *     summary: Get product for editing
 *     description: Retrieve product data in a format suitable for editing forms
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Product ID
 *         example: "682e1358c97ec7b3937a2c40"
 *     responses:
 *       200:
 *         description: Product data for editing
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *                 editableFields:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["name", "price", "stock", "description", "condition", "tags"]
 *                   description: List of commonly edited fields
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 *
 *   patch:
 *     summary: Update product with validation
 *     description: Update product with enhanced validation and field checking
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Product ID
 *         example: "682e1358c97ec7b3937a2c40"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateProductDto'
 *           examples:
 *             safe_update:
 *               summary: "Safe update (no ObjectId fields)"
 *               value:
 *                 name: "iPhone 15 Pro Max - Updated"
 *                 price: 1099.99
 *                 stock: 25
 *                 description: "Updated description with new features"
 *                 condition: "new"
 *                 tags: ["smartphone", "apple", "5g", "pro", "updated"]
 *                 featured: true
 *             inventory_only:
 *               summary: "Inventory update only"
 *               value:
 *                 stock: 50
 *                 lowStockThreshold: 5
 *                 status: "in-stock"
 *             pricing_only:
 *               summary: "Pricing update only"
 *               value:
 *                 price: 999.99
 *                 originalPrice: 1199.99
 *                 saleEndsAt: "2024-12-31T23:59:59Z"
 *     responses:
 *       200:
 *         description: Product updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *                 updatedFields:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["name", "price", "stock"]
 *                   description: List of fields that were actually updated
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
