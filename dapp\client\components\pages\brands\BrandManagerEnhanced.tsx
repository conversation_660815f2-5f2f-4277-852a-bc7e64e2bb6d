"use client";

import React, { useEffect, useState } from "react";

import {
  Archive,
  Check,
  Copy,
  Edit,
  ExternalLink,
  Eye,
  Filter,
  Globe,
  Grid3X3,
  List,
  MoreHorizontal,
  Plus,
  RefreshCw,
  Search,
  Trash2,
  X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useBrandMutations, useBrands } from "@/hooks/useBrands";
import { Brand, CreateBrandDto } from "@/lib/api/brands";

interface BrandManagerEnhancedProps {
  initialBrands?: Brand[];
  onBrandsChange?: (brands: Brand[]) => void;
}

/**
 * Enhanced component for managing product brands with professional UI
 */
export const BrandManagerEnhanced = ({
  initialBrands = [],
  onBrandsChange,
}: BrandManagerEnhancedProps) => {
  const router = useRouter();

  // API hooks
  const { brands: apiBrands, loading, error, refetch } = useBrands();
  const {
    createBrand,
    updateBrand,
    deleteBrand,
    loading: mutationLoading,
  } = useBrandMutations();

  // Local state
  const [brands, setBrands] = useState<Brand[]>(initialBrands);
  const [newBrand, setNewBrand] = useState<Partial<CreateBrandDto>>({
    name: "",
    description: "",
    logo: "",
    website: "",
    color: "#3B82F6",
    isActive: true,
  });
  const [editingBrandId, setEditingBrandId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<Brand>>({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showInactive, setShowInactive] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<"name" | "created" | "products">("name");

  // Sync with API data
  useEffect(() => {
    if (apiBrands.length > 0) {
      setBrands(apiBrands);
      onBrandsChange?.(apiBrands);
    }
  }, [apiBrands, onBrandsChange]);

  // Filter and sort brands
  const filteredAndSortedBrands = brands
    .filter((brand) => {
      const matchesSearch =
        brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        brand.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = showInactive || brand.isActive;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "created":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "products":
          return b.productCount - a.productCount;
        default:
          return 0;
      }
    });

  // Add a new brand
  const handleAddBrand = async () => {
    if (!newBrand.name) {
      toast.error("Brand name is required");
      return;
    }

    try {
      await createBrand({
        name: newBrand.name,
        description: newBrand.description || "",
        logo: newBrand.logo || "",
        website: newBrand.website || "",
        color: newBrand.color || "#3B82F6",
        isActive: newBrand.isActive ?? true,
      });

      // Reset form
      setNewBrand({
        name: "",
        description: "",
        logo: "",
        website: "",
        color: "#3B82F6",
        isActive: true,
      });
      setShowAddForm(false);

      // Refresh brands list
      await refetch();
    } catch (error) {
      // Error is already handled in the hook
      console.error("Failed to create brand:", error);
    }
  };

  // Start editing a brand
  const handleEditStart = (brand: Brand) => {
    setEditingBrandId(brand._id);
    setEditForm({ ...brand });
  };

  // Cancel editing
  const handleEditCancel = () => {
    setEditingBrandId(null);
    setEditForm({});
  };

  // Save edited brand
  const handleEditSave = async () => {
    if (!editForm.name || !editingBrandId) {
      toast.error("Brand name is required");
      return;
    }

    try {
      await updateBrand(editingBrandId, {
        name: editForm.name,
        description: editForm.description || "",
        logo: editForm.logo || "",
        website: editForm.website || "",
        color: editForm.color || "#3B82F6",
        isActive: editForm.isActive ?? true,
      });

      setEditingBrandId(null);
      setEditForm({});

      // Refresh brands list
      await refetch();
    } catch (error) {
      // Error is already handled in the hook
      console.error("Failed to update brand:", error);
    }
  };

  // Delete a brand
  const handleDeleteBrand = async (brand: Brand) => {
    if (brand.productCount > 0) {
      toast.error(
        `Cannot delete "${brand.name}" because it has ${brand.productCount} products. Please remove or reassign the products first.`
      );
      return;
    }

    if (
      confirm(
        `Are you sure you want to delete "${brand.name}"? This action cannot be undone.`
      )
    ) {
      try {
        await deleteBrand(brand._id);
        await refetch();
      } catch (error) {
        // Error is already handled in the hook
        console.error("Failed to delete brand:", error);
      }
    }
  };

  // Archive/Unarchive a brand
  const handleArchiveBrand = async (brand: Brand) => {
    try {
      await updateBrand(brand._id, {
        isActive: !brand.isActive,
      });
      await refetch();
    } catch (error) {
      console.error("Failed to archive brand:", error);
    }
  };

  // Duplicate a brand
  const handleDuplicateBrand = async (brand: Brand) => {
    try {
      await createBrand({
        name: `${brand.name} (Copy)`,
        description: brand.description,
        logo: brand.logo,
        website: brand.website,
        color: brand.color,
        isActive: brand.isActive,
      });
      await refetch();
    } catch (error) {
      console.error("Failed to duplicate brand:", error);
    }
  };

  // View products for a brand
  const handleViewProducts = (brand: Brand) => {
    router.push(`/admin/products/list?brand=${encodeURIComponent(brand.name)}`);
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading brands: {error}</p>
            <Button onClick={refetch} className="mt-2">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h1 className="text-2xl font-bold">Brand Management</h1>
          <p className="text-muted-foreground">
            Manage your product brands and their information
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refetch}
            disabled={loading}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Brand
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Brands</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{brands.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Brands</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {brands.filter((b) => b.isActive).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Brands with Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {brands.filter((b) => b.productCount > 0).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {brands.reduce((sum, b) => sum + b.productCount, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <Input
                  placeholder="Search brands..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select
                value={sortBy}
                onValueChange={(value: any) => setSortBy(value)}
              >
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="created">Created Date</SelectItem>
                  <SelectItem value="products">Product Count</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant={showInactive ? "default" : "outline"}
                onClick={() => setShowInactive(!showInactive)}
              >
                <Filter className="mr-2 h-4 w-4" />
                {showInactive ? "Show All" : "Show Inactive"}
              </Button>
              <div className="flex rounded-md border">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Brand Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Brand</CardTitle>
            <CardDescription>
              Create a new brand for your product catalog
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="brand-name">Brand Name *</Label>
                <Input
                  id="brand-name"
                  placeholder="Enter brand name"
                  value={newBrand.name}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, name: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="brand-color">Brand Color</Label>
                <div className="flex gap-2">
                  <Input
                    id="brand-color"
                    type="color"
                    value={newBrand.color}
                    onChange={(e) =>
                      setNewBrand({ ...newBrand, color: e.target.value })
                    }
                    className="w-16"
                  />
                  <Input
                    placeholder="#3B82F6"
                    value={newBrand.color}
                    onChange={(e) =>
                      setNewBrand({ ...newBrand, color: e.target.value })
                    }
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="brand-website">Website</Label>
                <Input
                  id="brand-website"
                  placeholder="https://example.com"
                  value={newBrand.website}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, website: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="brand-logo">Logo URL</Label>
                <Input
                  id="brand-logo"
                  placeholder="https://example.com/logo.png"
                  value={newBrand.logo}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, logo: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="brand-description">Description</Label>
                <Textarea
                  id="brand-description"
                  placeholder="Enter brand description"
                  value={newBrand.description}
                  onChange={(e) =>
                    setNewBrand({ ...newBrand, description: e.target.value })
                  }
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="brand-active"
                  checked={newBrand.isActive}
                  onCheckedChange={(checked) =>
                    setNewBrand({ ...newBrand, isActive: checked })
                  }
                />
                <Label htmlFor="brand-active">Active</Label>
              </div>
            </div>
            <div className="mt-6 flex gap-2">
              <Button onClick={handleAddBrand} disabled={mutationLoading}>
                <Check className="mr-2 h-4 w-4" />
                Add Brand
              </Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Brands Display */}
      <Card>
        <CardHeader>
          <CardTitle>Brands ({filteredAndSortedBrands.length})</CardTitle>
          <CardDescription>
            Manage your brand catalog and view product associations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : filteredAndSortedBrands.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">No brands found</p>
              {searchTerm && (
                <Button
                  variant="link"
                  onClick={() => setSearchTerm("")}
                  className="mt-2"
                >
                  Clear search
                </Button>
              )}
            </div>
          ) : viewMode === "grid" ? (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredAndSortedBrands.map((brand) => (
                <Card key={brand._id} className="relative">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div
                          className="h-6 w-6 rounded-full border-2"
                          style={{ backgroundColor: brand.color }}
                        />
                        <div>
                          {editingBrandId === brand._id ? (
                            <Input
                              value={editForm.name}
                              onChange={(e) =>
                                setEditForm({
                                  ...editForm,
                                  name: e.target.value,
                                })
                              }
                              className="h-8 text-sm font-medium"
                            />
                          ) : (
                            <CardTitle className="text-lg">
                              {brand.name}
                            </CardTitle>
                          )}
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {editingBrandId === brand._id ? (
                            <>
                              <DropdownMenuItem onClick={handleEditSave}>
                                <Check className="mr-2 h-4 w-4" />
                                Save
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={handleEditCancel}>
                                <X className="mr-2 h-4 w-4" />
                                Cancel
                              </DropdownMenuItem>
                            </>
                          ) : (
                            <>
                              <DropdownMenuItem
                                onClick={() => handleEditStart(brand)}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleViewProducts(brand)}
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                View Products ({brand.productCount})
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDuplicateBrand(brand)}
                              >
                                <Copy className="mr-2 h-4 w-4" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleArchiveBrand(brand)}
                              >
                                <Archive className="mr-2 h-4 w-4" />
                                {brand.isActive ? "Archive" : "Unarchive"}
                              </DropdownMenuItem>
                              {brand.website && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    window.open(brand.website, "_blank")
                                  }
                                >
                                  <ExternalLink className="mr-2 h-4 w-4" />
                                  Visit Website
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteBrand(brand)}
                                className="text-red-600"
                                disabled={brand.productCount > 0}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {editingBrandId === brand._id ? (
                      <div className="space-y-3">
                        <Textarea
                          placeholder="Description"
                          value={editForm.description}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              description: e.target.value,
                            })
                          }
                          className="min-h-[60px]"
                        />
                        <div className="flex gap-2">
                          <Input
                            type="color"
                            value={editForm.color}
                            onChange={(e) =>
                              setEditForm({
                                ...editForm,
                                color: e.target.value,
                              })
                            }
                            className="w-16"
                          />
                          <Input
                            placeholder="Website"
                            value={editForm.website}
                            onChange={(e) =>
                              setEditForm({
                                ...editForm,
                                website: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={editForm.isActive}
                            onCheckedChange={(checked) =>
                              setEditForm({ ...editForm, isActive: checked })
                            }
                          />
                          <Label>Active</Label>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <p className="text-sm text-muted-foreground">
                          {brand.description || "No description"}
                        </p>
                        <div className="flex items-center justify-between">
                          <Badge
                            variant={brand.isActive ? "default" : "secondary"}
                          >
                            {brand.isActive ? "Active" : "Inactive"}
                          </Badge>
                          <Badge variant="outline">
                            {brand.productCount} products
                          </Badge>
                        </div>
                        {brand.website && (
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <Globe className="h-3 w-3" />
                            <span className="truncate">{brand.website}</span>
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground">
                          Created{" "}
                          {new Date(brand.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            // List view would go here - simplified for now
            <div className="py-8 text-center">
              <p className="text-muted-foreground">List view coming soon</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
