"use client";

import React from "react";

import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import { OrdersFilter } from "./OrdersFilter";
import { OrdersTable } from "./OrdersTable";
import { OrdersPagination } from "./OrdersPagination";

export const OrdersListWrapper = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <OrdersFilter />

          <Separator className="my-3 mb-6" />

          <OrdersTable />
        </CardContent>
      </Card>

      <OrdersPagination />
    </div>
  );
};
