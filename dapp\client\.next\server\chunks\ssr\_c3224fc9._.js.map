{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/common/PageHeaderWrapper.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ntype PageHeaderWrapperProps = {\r\n  title: string;\r\n  description: string;\r\n  children?: React.ReactNode;\r\n};\r\n\r\n/**\r\n * A standardized page header component with title and description\r\n * Used across admin pages for consistent UI\r\n */\r\nexport const PageHeaderWrapper = ({\r\n  title,\r\n  description,\r\n  children,\r\n}: PageHeaderWrapperProps) => {\r\n  return (\r\n    <div className=\"container mx-auto py-6\">\r\n      <div className=\"flex flex-col items-start justify-between gap-4 border-b pb-5 sm:flex-row sm:items-center sm:gap-0\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold tracking-tight\">{title}</h1>\r\n          <p className=\"text-muted-foreground\">{description}</p>\r\n        </div>\r\n        {children && <div>{children}</div>}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAYO,MAAM,oBAAoB,CAAC,EAChC,KAAK,EACL,WAAW,EACX,QAAQ,EACe;IACvB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;;sCACC,6WAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6WAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;gBAEvC,0BAAY,6WAAC;8BAAK;;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersListActions.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OrdersListActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrdersListActions() from the server but OrdersListActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/pages/orders/list/OrdersListActions.tsx <module evaluation>\",\n    \"OrdersListActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oFACA", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersListActions.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OrdersListActions = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrdersListActions() from the server but OrdersListActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/pages/orders/list/OrdersListActions.tsx\",\n    \"OrdersListActions\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,gEACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersListWrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OrdersListWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrdersListWrapper() from the server but OrdersListWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/pages/orders/list/OrdersListWrapper.tsx <module evaluation>\",\n    \"OrdersListWrapper\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oFACA", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersListWrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OrdersListWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call OrdersListWrapper() from the server but OrdersListWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/pages/orders/list/OrdersListWrapper.tsx\",\n    \"OrdersListWrapper\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,gEACA", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/app/admin/orders/list/page.tsx"], "sourcesContent": ["import { PageHeaderWrapper } from \"@/components/common/PageHeaderWrapper\";\r\nimport { OrdersListActions } from \"@/components/pages/orders/list/OrdersListActions\";\r\nimport { OrdersListWrapper } from \"@/components/pages/orders/list/OrdersListWrapper\";\r\n\r\nexport default function AdminOrdersList() {\r\n  return (\r\n    <>\r\n      <PageHeaderWrapper\r\n        title=\"Orders\"\r\n        description=\"View and manage all customer orders and fulfillment\"\r\n      >\r\n        <OrdersListActions />\r\n      </PageHeaderWrapper>\r\n\r\n      <div className=\"container mx-auto mt-6\">\r\n        <OrdersListWrapper />\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE;;0BACE,6WAAC,0IAAA,CAAA,oBAAiB;gBAChB,OAAM;gBACN,aAAY;0BAEZ,cAAA,6WAAC,2JAAA,CAAA,oBAAiB;;;;;;;;;;0BAGpB,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,2JAAA,CAAA,oBAAiB;;;;;;;;;;;;AAI1B", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/node_modules/.pnpm/next%4015.2.2_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAQzC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1CJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;;QAIlC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/node_modules/.pnpm/next%4015.2.2_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAEgG,EAAC;;;;;;;;AASvH,OAAO,MAAMG,eAAe,6CAAA;IAC1BC,MAAAA,GAASC;IACTC,EAAAA,OAAAA;IAAAA,CAAWC;IAAAA;QACb,EAAC,UAAA;YAAA;YAAA;gBAED,YAAA;oBAAA,CAAc;oBAAA,iCAA0C;4BAAE,QAAA;4BAAA;4BAAA,OAAwB;gCAAsB,EAAC,UAAA;oCAAA;oCAAA,CAEzG;oCAAA,yDAA4D;wCAC5D,KAAO,KAAA,CAAMC;wCAAAA,QAAc;4CAAA,GAAIX,CAAAA,gBAAmB;4CAAA;yCAAA;;mCAChDY,YAAY;;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;4BAAA;yBAAA;;yBACNC,UAAU;8BACV,IAAA,CAAA;oBAAA;iBAAA,gCAA2C;;iBAC3CC,YAAY;sBACZC,IAAAA,CAAAA,GAAU;gBAAA,UAAA;oBAAA,IAAA;oBAAA;iBAAA;;eACVC,UAAU,EAAE;;KACd;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}