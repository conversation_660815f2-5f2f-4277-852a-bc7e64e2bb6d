// Brand Manager UI Components - Add Form and Display Views

export const BrandAddForm = ({ 
  showAddForm, 
  newBrand, 
  setNewBrand, 
  handleAddBrand, 
  setShowAddForm 
}: any) => {
  if (!showAddForm) return null;

  return (
    <Card className="border-2 border-green-200 bg-green-50/50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="h-5 w-5 text-green-600" />
          Add New Brand
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <Label htmlFor="new-brand-name">Brand Name *</Label>
            <Input
              id="new-brand-name"
              value={newBrand.name}
              onChange={(e) =>
                setNewBrand({ ...newBrand, name: e.target.value })
              }
              placeholder="e.g. Apple"
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="new-brand-logo">Logo (Emoji)</Label>
            <Input
              id="new-brand-logo"
              value={newBrand.logo}
              onChange={(e) =>
                setNewBrand({ ...newBrand, logo: e.target.value })
              }
              placeholder="🍎"
              className="mt-1"
            />
          </div>
          
          <div className="md:col-span-2">
            <Label htmlFor="new-brand-description">Description</Label>
            <Textarea
              id="new-brand-description"
              value={newBrand.description}
              onChange={(e) =>
                setNewBrand({ ...newBrand, description: e.target.value })
              }
              placeholder="Describe the brand, its values, and what makes it unique..."
              className="mt-1"
              rows={3}
            />
          </div>
          
          <div>
            <Label htmlFor="new-brand-website">Website</Label>
            <Input
              id="new-brand-website"
              type="url"
              value={newBrand.website}
              onChange={(e) =>
                setNewBrand({ ...newBrand, website: e.target.value })
              }
              placeholder="https://example.com"
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="new-brand-email">Contact Email</Label>
            <Input
              id="new-brand-email"
              type="email"
              value={newBrand.email}
              onChange={(e) =>
                setNewBrand({ ...newBrand, email: e.target.value })
              }
              placeholder="<EMAIL>"
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="new-brand-country">Country</Label>
            <Input
              id="new-brand-country"
              value={newBrand.country}
              onChange={(e) =>
                setNewBrand({ ...newBrand, country: e.target.value })
              }
              placeholder="United States"
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="new-brand-founded">Founded Year</Label>
            <Input
              id="new-brand-founded"
              type="number"
              min="1800"
              max={new Date().getFullYear()}
              value={newBrand.foundedYear || ""}
              onChange={(e) =>
                setNewBrand({ ...newBrand, foundedYear: parseInt(e.target.value) || undefined })
              }
              placeholder="1976"
              className="mt-1"
            />
          </div>
          
          <div className="flex items-center space-x-2 pt-6">
            <Switch
              id="new-brand-active"
              checked={newBrand.isActive}
              onCheckedChange={(checked) =>
                setNewBrand({ ...newBrand, isActive: checked })
              }
            />
            <Label htmlFor="new-brand-active">Active brand</Label>
          </div>
          
          <div className="flex items-center space-x-2 pt-6">
            <Switch
              id="new-brand-premium"
              checked={newBrand.isPremium}
              onCheckedChange={(checked) =>
                setNewBrand({ ...newBrand, isPremium: checked })
              }
            />
            <Label htmlFor="new-brand-premium">Premium brand</Label>
          </div>
        </div>
        
        <div className="mt-6 flex gap-2">
          <Button onClick={handleAddBrand}>
            <Save className="mr-2 h-4 w-4" />
            Add Brand
          </Button>
          <Button variant="outline" onClick={() => setShowAddForm(false)}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export const BrandGridView = ({ 
  filteredAndSortedBrands, 
  editingBrandId, 
  editForm, 
  setEditForm, 
  handleEditSave, 
  handleEditCancel, 
  handleEditStart, 
  handleDeleteBrand 
}: any) => {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {filteredAndSortedBrands.map((brand: any) => (
        <Card key={brand.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            {editingBrandId === brand.id ? (
              // Edit form
              <div className="space-y-3">
                <Input
                  value={editForm.name || ""}
                  onChange={(e) =>
                    setEditForm({ ...editForm, name: e.target.value })
                  }
                  placeholder="Brand name"
                />
                <Textarea
                  value={editForm.description || ""}
                  onChange={(e) =>
                    setEditForm({ ...editForm, description: e.target.value })
                  }
                  placeholder="Description"
                  rows={2}
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleEditSave}>
                    <Save className="mr-1 h-3 w-3" />
                    Save
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleEditCancel}>
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ) : (
              // Display mode
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-3xl">{brand.logo}</span>
                    <div>
                      <h3 className="font-semibold">{brand.name}</h3>
                      <div className="flex gap-1">
                        <Badge 
                          variant={brand.isActive ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {brand.isActive ? "Active" : "Inactive"}
                        </Badge>
                        {brand.isPremium && (
                          <Badge variant="outline" className="text-xs text-purple-600 border-purple-200">
                            Premium
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditStart(brand)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Products
                      </DropdownMenuItem>
                      {brand.website && (
                        <DropdownMenuItem asChild>
                          <a href={brand.website} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Visit Website
                          </a>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem>
                        <Copy className="mr-2 h-4 w-4" />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Archive className="mr-2 h-4 w-4" />
                        Archive
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDeleteBrand(brand.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                
                <p className="text-sm text-gray-600 line-clamp-2">
                  {brand.description || "No description provided"}
                </p>
                
                <div className="space-y-2 text-xs text-gray-500">
                  <div className="flex items-center justify-between">
                    <span>{brand.productCount} products</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span>{brand.rating.toFixed(1)}</span>
                    </div>
                  </div>
                  
                  {brand.country && (
                    <div className="flex items-center gap-1">
                      <Globe className="h-3 w-3" />
                      <span>{brand.country}</span>
                      {brand.foundedYear && <span>• Est. {brand.foundedYear}</span>}
                    </div>
                  )}
                  
                  {brand.website && (
                    <div className="flex items-center gap-1">
                      <ExternalLink className="h-3 w-3" />
                      <a 
                        href={brand.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline truncate"
                      >
                        {brand.website.replace(/^https?:\/\//, '')}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
