import { Router } from "express";

import { ColorController } from "../controllers/color/color.controller";

const router = Router();
const colorController = new ColorController();

// GET /api/colors - Get all colors with optional filtering
router.get("/", colorController.getColors);

// GET /api/colors/families - Get colors grouped by color family
router.get("/families", colorController.getColorsByFamily);

// GET /api/colors/slug/:slug - Get color by slug
router.get("/slug/:slug", colorController.getColorBySlug);

// GET /api/colors/hex/:hex - Get color by hex value
router.get("/hex/:hex", colorController.getColorByHex);

// POST /api/colors/recalculate-counts - Recalculate product counts
router.post("/recalculate-counts", colorController.recalculateProductCounts);

// GET /api/colors/:id - Get color by ID
router.get("/:id", colorController.getColorById);

// POST /api/colors - Create a new color
router.post("/", colorController.createColor);

// PUT /api/colors/:id - Update a color
router.put("/:id", colorController.updateColor);

// DELETE /api/colors/:id - Delete a color
router.delete("/:id", colorController.deleteColor);

export default router;
