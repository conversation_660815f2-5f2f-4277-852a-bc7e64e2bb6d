"use client";

import { useState } from "react";

import { ChevronDown, Search } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

// Emoji database with search functionality
type EmojiData = {
  emoji: string;
  name: string;
  keywords: string[];
  category: string;
};

const emojiDatabase: EmojiData[] = [
  // Shopping & Commerce
  {
    emoji: "🛒",
    name: "shopping cart",
    keywords: ["cart", "shopping", "buy"],
    category: "Shopping",
  },
  {
    emoji: "🛍️",
    name: "shopping bags",
    keywords: ["bags", "shopping", "retail"],
    category: "Shopping",
  },
  {
    emoji: "💳",
    name: "credit card",
    keywords: ["card", "payment", "money"],
    category: "Shopping",
  },
  {
    emoji: "💰",
    name: "money bag",
    keywords: ["money", "cash", "wealth"],
    category: "Shopping",
  },
  {
    emoji: "🏪",
    name: "store",
    keywords: ["store", "shop", "retail"],
    category: "Shopping",
  },
  {
    emoji: "🏬",
    name: "department store",
    keywords: ["mall", "shopping", "retail"],
    category: "Shopping",
  },

  // Electronics & Tech
  {
    emoji: "📱",
    name: "mobile phone",
    keywords: ["phone", "mobile", "smartphone"],
    category: "Electronics",
  },
  {
    emoji: "💻",
    name: "laptop",
    keywords: ["laptop", "computer", "tech"],
    category: "Electronics",
  },
  {
    emoji: "⌚",
    name: "watch",
    keywords: ["watch", "time", "smartwatch"],
    category: "Electronics",
  },
  {
    emoji: "📺",
    name: "television",
    keywords: ["tv", "television", "screen"],
    category: "Electronics",
  },
  {
    emoji: "🎮",
    name: "video game",
    keywords: ["game", "gaming", "console"],
    category: "Electronics",
  },
  {
    emoji: "📷",
    name: "camera",
    keywords: ["camera", "photo", "photography"],
    category: "Electronics",
  },
  {
    emoji: "🎧",
    name: "headphones",
    keywords: ["headphones", "audio", "music"],
    category: "Electronics",
  },
  {
    emoji: "⚡",
    name: "lightning",
    keywords: ["power", "energy", "electric"],
    category: "Electronics",
  },

  // Fashion & Clothing
  {
    emoji: "👕",
    name: "t-shirt",
    keywords: ["shirt", "clothing", "apparel"],
    category: "Fashion",
  },
  {
    emoji: "👔",
    name: "necktie",
    keywords: ["tie", "formal", "business"],
    category: "Fashion",
  },
  {
    emoji: "👗",
    name: "dress",
    keywords: ["dress", "clothing", "fashion"],
    category: "Fashion",
  },
  {
    emoji: "👠",
    name: "high heel",
    keywords: ["shoes", "heels", "fashion"],
    category: "Fashion",
  },
  {
    emoji: "👜",
    name: "handbag",
    keywords: ["bag", "purse", "fashion"],
    category: "Fashion",
  },
  {
    emoji: "🧥",
    name: "coat",
    keywords: ["coat", "jacket", "outerwear"],
    category: "Fashion",
  },

  // Home & Garden
  {
    emoji: "🏠",
    name: "house",
    keywords: ["house", "home", "building"],
    category: "Home",
  },
  {
    emoji: "🛏️",
    name: "bed",
    keywords: ["bed", "sleep", "bedroom"],
    category: "Home",
  },
  {
    emoji: "🪑",
    name: "chair",
    keywords: ["chair", "seat", "furniture"],
    category: "Home",
  },
  {
    emoji: "🛋️",
    name: "couch",
    keywords: ["couch", "sofa", "furniture"],
    category: "Home",
  },
  {
    emoji: "🌱",
    name: "seedling",
    keywords: ["plant", "garden", "grow"],
    category: "Home",
  },
  {
    emoji: "🌸",
    name: "cherry blossom",
    keywords: ["flower", "blossom", "spring"],
    category: "Home",
  },

  // Food & Beverages
  {
    emoji: "🍎",
    name: "apple",
    keywords: ["apple", "fruit", "food"],
    category: "Food",
  },
  {
    emoji: "🍕",
    name: "pizza",
    keywords: ["pizza", "food", "italian"],
    category: "Food",
  },
  {
    emoji: "🍔",
    name: "hamburger",
    keywords: ["burger", "hamburger", "food"],
    category: "Food",
  },
  {
    emoji: "☕",
    name: "coffee",
    keywords: ["coffee", "drink", "caffeine"],
    category: "Food",
  },
  {
    emoji: "🍷",
    name: "wine",
    keywords: ["wine", "alcohol", "drink"],
    category: "Food",
  },
  {
    emoji: "🧀",
    name: "cheese",
    keywords: ["cheese", "dairy", "food"],
    category: "Food",
  },

  // Sports & Fitness
  {
    emoji: "⚽",
    name: "soccer ball",
    keywords: ["soccer", "football", "sports"],
    category: "Sports",
  },
  {
    emoji: "🏀",
    name: "basketball",
    keywords: ["basketball", "sports", "ball"],
    category: "Sports",
  },
  {
    emoji: "🎾",
    name: "tennis",
    keywords: ["tennis", "sports", "ball"],
    category: "Sports",
  },
  {
    emoji: "🏋️",
    name: "weight lifting",
    keywords: ["gym", "fitness", "workout"],
    category: "Sports",
  },
  {
    emoji: "🚴",
    name: "cycling",
    keywords: ["bike", "cycling", "exercise"],
    category: "Sports",
  },
  {
    emoji: "🏃",
    name: "running",
    keywords: ["running", "exercise", "fitness"],
    category: "Sports",
  },

  // General
  {
    emoji: "📦",
    name: "package",
    keywords: ["box", "package", "delivery"],
    category: "General",
  },
  {
    emoji: "🏷️",
    name: "label",
    keywords: ["tag", "label", "price"],
    category: "General",
  },
  {
    emoji: "📋",
    name: "clipboard",
    keywords: ["list", "clipboard", "notes"],
    category: "General",
  },
  {
    emoji: "📊",
    name: "bar chart",
    keywords: ["chart", "graph", "data"],
    category: "General",
  },
  {
    emoji: "🎯",
    name: "target",
    keywords: ["target", "goal", "aim"],
    category: "General",
  },
  {
    emoji: "🔥",
    name: "fire",
    keywords: ["fire", "hot", "trending"],
    category: "General",
  },
  {
    emoji: "⭐",
    name: "star",
    keywords: ["star", "favorite", "rating"],
    category: "General",
  },
  {
    emoji: "🎁",
    name: "gift",
    keywords: ["gift", "present", "surprise"],
    category: "General",
  },
];

// Extract categories for tabs
const categories = [
  "All",
  ...Array.from(new Set(emojiDatabase.map((item) => item.category))),
];

type EmojiPickerProps = {
  value?: string;
  onChange: (emoji: string) => void;
  placeholder?: string;
};

export const EmojiPicker = ({
  value,
  onChange,
  placeholder = "Pick an emoji",
}: EmojiPickerProps) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("All");

  // Filter emojis based on category and search
  const filteredEmojis =
    selectedCategory === "All"
      ? emojiDatabase
      : emojiDatabase.filter((item) => item.category === selectedCategory);

  const searchFilteredEmojis = searchQuery
    ? filteredEmojis.filter((item) => {
        const query = searchQuery.toLowerCase();
        return (
          item.name.toLowerCase().includes(query) ||
          item.keywords.some((keyword) =>
            keyword.toLowerCase().includes(query)
          ) ||
          item.emoji.includes(query)
        );
      })
    : filteredEmojis;

  const handleEmojiSelect = (emoji: string) => {
    onChange(emoji);
    setOpen(false);
    setSearchQuery(""); // Reset search when emoji is selected
  };

  return (
    <div className="space-y-2">
      <Label>Icon</Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <button
            className={cn(
              // Base input styles - exactly matching Input component
              "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
              "file:border-0 file:bg-transparent file:text-sm file:font-medium",
              "placeholder:text-muted-foreground",
              "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
              "disabled:cursor-not-allowed disabled:opacity-50",
              // Hover and focus states
              "hover:bg-accent hover:text-accent-foreground",
              "transition-colors",
              // Open state
              open && "ring-2 ring-ring ring-offset-2"
            )}
            type="button"
            role="combobox"
            aria-expanded={open}
            aria-haspopup="listbox"
            aria-controls="emoji-listbox"
          >
            {value ? (
              <div className="flex items-center gap-2">
                <span className="text-2xl duration-200 animate-in fade-in">
                  {value}
                </span>
                <span className="font-medium text-foreground">Selected</span>
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <ChevronDown
              className={cn(
                "h-4 w-4 opacity-50 transition-transform",
                open && "rotate-180"
              )}
            />
          </button>
        </PopoverTrigger>

        <PopoverContent className="w-80 p-0" align="start">
          <div className="space-y-4 p-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search emojis..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Category Tabs */}
            <div className="flex flex-wrap gap-1">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={
                    selectedCategory === category ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </Button>
              ))}
            </div>

            {/* Emoji Grid */}
            <ScrollArea className="h-64">
              <div
                className="grid grid-cols-8 gap-2 p-2"
                id="emoji-listbox"
                role="listbox"
              >
                {searchFilteredEmojis.map((item, index) => (
                  <Button
                    key={`${item.emoji}-${index}`}
                    variant="ghost"
                    size="sm"
                    className="h-10 w-10 p-0 hover:bg-muted"
                    onClick={() => handleEmojiSelect(item.emoji)}
                    role="option"
                    aria-selected={value === item.emoji}
                    title={item.name}
                  >
                    <span className="text-xl">{item.emoji}</span>
                  </Button>
                ))}
              </div>
              {searchFilteredEmojis.length === 0 && (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  No emojis found for "{searchQuery}"
                </div>
              )}
            </ScrollArea>

            {/* Custom Input */}
            <div className="border-t pt-4">
              <Label className="text-sm text-muted-foreground">
                Or enter custom emoji:
              </Label>
              <Input
                placeholder="🎯"
                value={value || ""}
                onChange={(e) => onChange(e.target.value)}
                className="mt-1"
                maxLength={4}
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
