import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";

import { MaterialService } from "../../services/material.service";
import { CreateMaterialDto, UpdateMaterialDto } from "../../types/material.types";

/**
 * Controller for handling material-related HTTP requests
 */
export class MaterialController {
  private materialService: MaterialService;

  constructor() {
    this.materialService = new MaterialService();
  }

  /**
   * Create a new material
   * POST /api/materials
   */
  createMaterial = async (req: Request, res: Response): Promise<void> => {
    try {
      const materialData: CreateMaterialDto = req.body;
      const material = await this.materialService.createMaterial(materialData);

      res.status(StatusCodes.CREATED).json({
        success: true,
        data: material,
        message: "Material created successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create material";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get all materials with optional filtering
   * GET /api/materials
   */
  getMaterials = async (req: Request, res: Response): Promise<void> => {
    try {
      const filters = {
        isActive:
          req.query.isActive === "true"
            ? true
            : req.query.isActive === "false"
              ? false
              : undefined,
        search: req.query.search as string,
        durability: req.query.durability as "low" | "medium" | "high",
        waterResistant:
          req.query.waterResistant === "true"
            ? true
            : req.query.waterResistant === "false"
              ? false
              : undefined,
        recyclable:
          req.query.recyclable === "true"
            ? true
            : req.query.recyclable === "false"
              ? false
              : undefined,
        weight: req.query.weight as "light" | "medium" | "heavy",
      };

      const materials = await this.materialService.getMaterials(filters);

      res.status(StatusCodes.OK).json({
        success: true,
        data: materials,
        count: materials.length,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch materials";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a material by ID
   * GET /api/materials/:id
   */
  getMaterialById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const material = await this.materialService.getMaterialById(id);

      if (!material) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Material not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: material,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch material";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a material by slug
   * GET /api/materials/slug/:slug
   */
  getMaterialBySlug = async (req: Request, res: Response): Promise<void> => {
    try {
      const { slug } = req.params;
      const material = await this.materialService.getMaterialBySlug(slug);

      if (!material) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Material not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: material,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch material";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Update a material
   * PUT /api/materials/:id
   */
  updateMaterial = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData: UpdateMaterialDto = req.body;

      const material = await this.materialService.updateMaterial(id, updateData);

      if (!material) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Material not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: material,
        message: "Material updated successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update material";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Delete a material
   * DELETE /api/materials/:id
   */
  deleteMaterial = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.materialService.deleteMaterial(id);

      if (!deleted) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Material not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        message: "Material deleted successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete material";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Recalculate product counts for all materials
   * POST /api/materials/recalculate-counts
   */
  recalculateProductCounts = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      await this.materialService.recalculateProductCounts();

      res.status(StatusCodes.OK).json({
        success: true,
        message: "Material product counts recalculated successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to recalculate material product counts";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };
}
