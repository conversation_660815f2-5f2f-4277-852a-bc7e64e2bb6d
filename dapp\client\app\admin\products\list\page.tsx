"use client";

import { useEffect, useRef, useState } from "react";

import { useSearchParams } from "next/navigation";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { ProductListActions } from "@/components/pages/products/ProductListActions";
import { ProductListWrapper } from "@/components/pages/products/ProductListWrapper";
import { ProductFilters } from "@/lib/api/products";

export default function AdminProductsList() {
  const searchParams = useSearchParams();
  const [initialFilters, setInitialFilters] = useState<ProductFilters>({});
  const wrapperRef = useRef<{ handleRefresh: () => void; loading: boolean }>(
    null
  );

  // Extract filters from URL parameters
  useEffect(() => {
    const filters: ProductFilters = {};

    // Get category from URL
    const category = searchParams.get("category");
    if (category) {
      filters.category = category;
    }

    // Get other potential filters from URL
    const brand = searchParams.get("brand");
    if (brand) {
      filters.brand = brand;
    }

    const search = searchParams.get("search");
    if (search) {
      filters.search = search;
    }

    const status = searchParams.get("status");
    if (status) {
      filters.status = status;
    }

    const minPrice = searchParams.get("minPrice");
    if (minPrice) {
      filters.minPrice = Number(minPrice);
    }

    const maxPrice = searchParams.get("maxPrice");
    if (maxPrice) {
      filters.maxPrice = Number(maxPrice);
    }

    const sortBy = searchParams.get("sortBy");
    if (sortBy) {
      filters.sortBy = sortBy as "price" | "createdAt" | "name";
    }

    const sortOrder = searchParams.get("sortOrder");
    if (sortOrder) {
      filters.sortOrder = sortOrder as "asc" | "desc";
    }

    setInitialFilters(filters);
  }, [searchParams]);

  const handleRefresh = () => {
    wrapperRef.current?.handleRefresh();
  };

  return (
    <>
      <PageHeaderWrapper
        title="Products"
        description="Browse and manage your product catalog"
      >
        <ProductListActions
          onRefresh={handleRefresh}
          loading={wrapperRef.current?.loading || false}
        />
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6">
        <ProductListWrapper
          ref={wrapperRef}
          initialFilters={initialFilters}
          key={JSON.stringify(initialFilters)}
        />
      </div>
    </>
  );
}
