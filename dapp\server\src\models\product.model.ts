import mongoose, { Document, Schema } from "mongoose";

import {
  Dimensions,
  ProductAddress,
  ProductAttribute,
  ProductCompliance,
  ProductOrigin,
  ProductReturn,
  ProductSEO,
  ProductVariant,
  ProductWarranty,
  Weight,
} from "../types/product.types";

// Create a proper document interface that avoids conflicts
export interface ProductDocument extends Omit<Document, "model"> {
  // Basic Info
  name: string;
  slug?: string;
  brand: string;
  model?: string;
  description: string;
  shortDescription?: string;
  sku?: string;
  barcode?: string;
  productType: string;
  visibility: string;

  // Pricing
  price: number;
  originalPrice?: number;
  currency: string;
  saleEndsAt?: Date;
  costPrice?: number;
  taxStatus: string;
  taxClass?: string;
  minimumOrderQuantity?: number;
  maximumOrderQuantity?: number;

  // Inventory
  stock: number;
  condition: string;
  isPublished: boolean;
  stockManagement: string;
  lowStockThreshold?: number;
  backorderAllowed?: boolean;
  trackQuantity?: boolean;
  soldIndividually?: boolean;

  // Details
  category: string;
  subcategory?: string;
  material?: string;
  dimensions?: Dimensions;
  weight?: Weight;
  yearMade?: number;
  tags?: string[];
  color?: string;
  size?: string;
  attributes?: ProductAttribute[];
  variants?: ProductVariant[];
  origin?: ProductOrigin;
  compliance?: ProductCompliance;

  // Images
  mainImage: string;
  images?: string[];
  imageAltTexts?: string[];
  videoUrl?: string;
  threeDModelUrl?: string;

  // Shipping
  location?: string;
  address?: ProductAddress;
  shippingCost?: number;
  shippingTime?: string;
  freeShipping?: boolean;
  shippingClass?: string;
  requiresShipping?: boolean;
  separateShipping?: boolean;
  shippingDimensions?: Dimensions;
  shippingWeight?: Weight;

  // Relations
  sellerId?: string;
  shopId?: string;
  relatedProducts?: string[];
  crossSellProducts?: string[];
  upSellProducts?: string[];
  bundledProducts?: string[];

  // Metrics
  averageRating?: number;
  reviewCount?: number;
  views?: number;
  purchases?: number;
  wishlistCount?: number;
  conversionRate?: number;
  returnRate?: number;
  profitMargin?: number;

  // Availability
  status: string;
  ageRestriction?: string;
  availableFrom?: Date;
  availableUntil?: Date;
  featured?: boolean;
  sticky?: boolean;
  downloadable?: boolean;
  virtual?: boolean;

  // SEO
  seo?: ProductSEO;

  // Warranty & Returns
  warranty?: ProductWarranty;
  returnPolicy?: ProductReturn;

  // Timestamps
  publishedAt?: Date;
  lastModifiedBy?: string;
}

// Define sub-schemas for complex types
const dimensionsSchema = new Schema<Dimensions>(
  {
    width: { type: Number, required: true },
    height: { type: Number, required: true },
    depth: { type: Number, required: true },
    unit: { type: String, enum: ["mm", "cm"], required: true },
  },
  { _id: false }
);

const weightSchema = new Schema<Weight>(
  {
    value: { type: Number, required: true },
    unit: { type: String, enum: ["g", "kg"], required: true },
  },
  { _id: false }
);

const coordinatesSchema = new Schema(
  {
    latitude: { type: Number, required: true },
    longitude: { type: Number, required: true },
  },
  { _id: false }
);

const productAddressSchema = new Schema<ProductAddress>(
  {
    street: { type: String },
    city: { type: String },
    state: { type: String },
    postalCode: { type: String },
    country: { type: String },
    coordinates: { type: coordinatesSchema },
  },
  { _id: false }
);

const productOriginSchema = new Schema<ProductOrigin>(
  {
    country: { type: String, required: true },
    region: { type: String },
    city: { type: String },
    manufacturer: { type: String },
    madeIn: { type: String },
  },
  { _id: false }
);

const productVariantSchema = new Schema<ProductVariant>(
  {
    id: { type: String, required: true },
    name: { type: String, required: true },
    sku: { type: String },
    price: { type: Number },
    stock: { type: Number },
    image: { type: String },
    attributes: { type: Schema.Types.Mixed },
  },
  { _id: false }
);

const productAttributeSchema = new Schema<ProductAttribute>(
  {
    name: { type: String, required: true },
    values: [{ type: String }],
    visible: { type: Boolean, required: true },
    variation: { type: Boolean, required: true },
  },
  { _id: false }
);

const productSEOSchema = new Schema<ProductSEO>(
  {
    metaTitle: { type: String },
    metaDescription: { type: String },
    focusKeyword: { type: String },
    slug: { type: String },
    canonicalUrl: { type: String },
  },
  { _id: false }
);

const productComplianceSchema = new Schema<ProductCompliance>(
  {
    certifications: [{ type: String }],
    warnings: [{ type: String }],
    restrictions: [{ type: String }],
    requiresLicense: { type: Boolean },
    hazardousMaterial: { type: Boolean },
  },
  { _id: false }
);

const productWarrantySchema = new Schema<ProductWarranty>(
  {
    duration: { type: Number },
    type: { type: String, enum: ["manufacturer", "seller", "extended"] },
    terms: { type: String },
    coverage: [{ type: String }],
  },
  { _id: false }
);

const productReturnSchema = new Schema<ProductReturn>(
  {
    returnable: { type: Boolean, required: true },
    returnWindow: { type: Number },
    returnPolicy: { type: String },
    restockingFee: { type: Number },
    returnShippingPaid: { type: String, enum: ["buyer", "seller", "shared"] },
  },
  { _id: false }
);

// Define the main product schema
const productSchema = new Schema<ProductDocument>(
  {
    // Basic Info
    name: { type: String, required: true, index: true },
    brand: { type: String, required: true, index: true },
    model: { type: String },
    description: { type: String, required: true },
    shortDescription: { type: String },
    sku: { type: String, unique: true, sparse: true },
    barcode: { type: String },
    productType: {
      type: String,
      enum: ["physical", "digital", "service", "subscription", "bundle"],
      required: true,
      default: "physical",
    },
    visibility: {
      type: String,
      enum: ["public", "private", "hidden", "password-protected"],
      required: true,
      default: "public",
    },

    // Pricing
    price: { type: Number, required: true },
    originalPrice: { type: Number },
    currency: {
      type: String,
      enum: ["EUR", "USD"],
      required: true,
      default: "EUR",
    },
    saleEndsAt: { type: Date },
    costPrice: { type: Number },
    taxStatus: {
      type: String,
      enum: ["taxable", "tax-exempt", "shipping-only"],
      default: "taxable",
    },
    taxClass: { type: String },
    minimumOrderQuantity: { type: Number, default: 1 },
    maximumOrderQuantity: { type: Number },

    // Inventory
    stock: { type: Number, required: true, default: 0 },
    condition: {
      type: String,
      enum: [
        "new",
        "like-new",
        "excellent",
        "good",
        "fair",
        "used",
        "refurbished",
        "vintage",
        "antique",
        "damaged",
      ],
      required: true,
      default: "new",
    },
    isPublished: { type: Boolean, default: true },
    stockManagement: {
      type: String,
      enum: ["track", "no-track", "backorder"],
      default: "track",
    },
    lowStockThreshold: { type: Number },
    backorderAllowed: { type: Boolean, default: false },
    trackQuantity: { type: Boolean, default: true },
    soldIndividually: { type: Boolean, default: false },

    // Details
    category: { type: String, required: true, index: true },
    subcategory: { type: String },
    material: { type: String },
    dimensions: { type: dimensionsSchema },
    weight: { type: weightSchema },
    yearMade: { type: Number, min: 1800, max: new Date().getFullYear() },
    tags: [{ type: String }],
    color: { type: String },
    size: { type: String },
    attributes: [{ type: productAttributeSchema }],
    variants: [{ type: productVariantSchema }],
    origin: { type: productOriginSchema },
    compliance: { type: productComplianceSchema },

    // Images
    mainImage: { type: String, required: true },
    images: [{ type: String }],
    imageAltTexts: [{ type: String }],
    videoUrl: { type: String },
    threeDModelUrl: { type: String },

    // Shipping
    location: { type: String },
    address: { type: productAddressSchema },
    shippingCost: { type: Number, min: 0 },
    shippingTime: { type: String },
    freeShipping: { type: Boolean, default: false },
    shippingClass: {
      type: String,
      enum: [
        "standard",
        "express",
        "overnight",
        "international",
        "heavy",
        "fragile",
        "digital-only",
      ],
      default: "standard",
    },
    requiresShipping: { type: Boolean, default: true },
    separateShipping: { type: Boolean, default: false },
    shippingDimensions: { type: dimensionsSchema },
    shippingWeight: { type: weightSchema },

    // Relations
    sellerId: { type: Schema.Types.ObjectId, ref: "User" },
    shopId: { type: Schema.Types.ObjectId, ref: "Shop" },
    relatedProducts: [{ type: Schema.Types.ObjectId, ref: "Product" }],
    crossSellProducts: [{ type: Schema.Types.ObjectId, ref: "Product" }],
    upSellProducts: [{ type: Schema.Types.ObjectId, ref: "Product" }],
    bundledProducts: [{ type: Schema.Types.ObjectId, ref: "Product" }],

    // Metrics
    averageRating: { type: Number, min: 0, max: 5 },
    reviewCount: { type: Number, default: 0 },
    views: { type: Number, default: 0 },
    purchases: { type: Number, default: 0 },
    wishlistCount: { type: Number, default: 0 },
    conversionRate: { type: Number, min: 0, max: 100 },
    returnRate: { type: Number, min: 0, max: 100 },
    profitMargin: { type: Number },

    // Availability
    status: {
      type: String,
      enum: [
        "in-stock",
        "out-of-stock",
        "coming-soon",
        "archived",
        "draft",
        "suspended",
      ],
      default: "draft",
      required: true,
    },
    ageRestriction: {
      type: String,
      enum: ["none", "18+", "21+"],
      default: "none",
    },
    availableFrom: { type: Date },
    availableUntil: { type: Date },
    featured: { type: Boolean, default: false },
    sticky: { type: Boolean, default: false },
    downloadable: { type: Boolean, default: false },
    virtual: { type: Boolean, default: false },

    // SEO
    seo: { type: productSEOSchema },

    // Warranty & Returns
    warranty: { type: productWarrantySchema },
    returnPolicy: { type: productReturnSchema },

    // Additional timestamps
    publishedAt: { type: Date },
    lastModifiedBy: { type: Schema.Types.ObjectId, ref: "User" },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt fields
    versionKey: false, // Removes the __v field
  }
);

// Add indexes for common queries
productSchema.index({ price: 1 });
productSchema.index({ createdAt: -1 });
productSchema.index({ brand: 1, name: 1 });
productSchema.index({ tags: 1 });

// Add a text index for search functionality
productSchema.index(
  {
    name: "text",
    description: "text",
    brand: "text",
    category: "text",
    tags: "text",
  },
  {
    weights: {
      name: 10,
      brand: 5,
      category: 3,
      tags: 2,
      description: 1,
    },
    name: "product_text_index",
  }
);

// Create and export the model
export const ProductModel = mongoose.model<ProductDocument>(
  "Product",
  productSchema
);

// Export the model as Product for consistency
export const Product = ProductModel;
