import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  Code,
  Database,
  Eye,
  Globe,
  Layers,
  Lock,
  Settings,
  Shield,
  Star,
  Target,
  Users,
  Zap,
} from "lucide-react";
import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { ProductFormData } from "@/schemas/productSchema";

import { FormField } from "../../../common/FormField";
import { CollapsibleSection } from "../../../ui/collapsible-section";

type AdvancedSectionProps = {
  register: UseFormRegister<ProductFormData>;
  errors: FieldErrors<ProductFormData>;
  setValue: UseFormSetValue<ProductFormData>;
  watch: UseFormWatch<ProductFormData>;
};

const ageRestrictions = [
  { value: "none", label: "No Age Restriction", icon: CheckCircle },
  { value: "18+", label: "18+ Years (Adult)", icon: Shield },
  { value: "21+", label: "21+ Years", icon: Lock },
];

const productStatuses = [
  { value: "draft", label: "Draft", icon: Eye },
  { value: "in-stock", label: "In Stock", icon: CheckCircle },
  { value: "out-of-stock", label: "Out of Stock", icon: Clock },
  { value: "coming-soon", label: "Coming Soon", icon: Clock },
  { value: "archived", label: "Archived", icon: Database },
  { value: "suspended", label: "Suspended", icon: Lock },
];

export const AdvancedSection: React.FC<AdvancedSectionProps> = ({
  register,
  errors,
  setValue,
  watch,
}) => {
  const ageRestriction = watch("ageRestriction");
  const status = watch("status");
  const downloadable = watch("downloadable");
  const virtual = watch("virtual");

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="flex items-center gap-3 border-b border-gray-200 pb-4">
        <div className="rounded-full bg-gray-100 p-2">
          <Settings className="h-5 w-5 text-gray-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Advanced Settings
          </h2>
          <p className="text-gray-600">
            Configure advanced product options and restrictions
          </p>
        </div>
      </div>

      {/* Product Status Card */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Target className="h-5 w-5 text-blue-600" />
            Product Status
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Control the current status and availability of this product
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status Selection */}
            <FormField id="status" label="Current Status" optional={true}>
              <Select
                onValueChange={(value) => setValue("status", value as any)}
                value={status}
              >
                <SelectTrigger className="border-2 focus:border-blue-500">
                  <SelectValue placeholder="Select product status..." />
                </SelectTrigger>
                <SelectContent>
                  {productStatuses.map((statusOption) => (
                    <SelectItem
                      key={statusOption.value}
                      value={statusOption.value}
                    >
                      <div className="flex items-center gap-2">
                        <statusOption.icon className="h-4 w-4" />
                        <span className="font-medium">
                          {statusOption.label}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>

            {/* Status Description */}
            {status && (
              <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
                <div className="flex items-start gap-2">
                  <Target className="mt-0.5 h-4 w-4 text-blue-600" />
                  <div className="text-sm text-blue-800">
                    <strong>
                      {status === "draft" && "Draft:"}
                      {status === "active" && "Active:"}
                      {status === "inactive" && "Inactive:"}
                      {status === "archived" && "Archived:"}
                    </strong>
                    <span className="ml-1">
                      {status === "draft" &&
                        "Product is being prepared and not visible to customers"}
                      {status === "active" &&
                        "Product is live and available for purchase"}
                      {status === "inactive" &&
                        "Product is temporarily unavailable but not deleted"}
                      {status === "archived" &&
                        "Product is permanently removed from active catalog"}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Age Restrictions Card */}
      <Card className="border-l-4 border-l-orange-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Shield className="h-5 w-5 text-orange-600" />
            Age Restrictions
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Set age requirements for purchasing this product
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Age Restriction Selection */}
            <FormField
              id="ageRestriction"
              label="Minimum Age Requirement"
              optional={true}
            >
              <Select
                onValueChange={(value) =>
                  setValue("ageRestriction", value as any)
                }
                value={ageRestriction}
              >
                <SelectTrigger className="border-2 focus:border-orange-500">
                  <SelectValue placeholder="Select age restriction..." />
                </SelectTrigger>
                <SelectContent>
                  {ageRestrictions.map((restriction) => (
                    <SelectItem
                      key={restriction.value}
                      value={restriction.value}
                    >
                      <div className="flex items-center gap-2">
                        <restriction.icon className="h-4 w-4" />
                        <span className="font-medium">{restriction.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>

            {/* Age Restriction Warning */}
            {ageRestriction && ageRestriction !== "none" && (
              <div className="rounded-lg border border-orange-200 bg-orange-50 p-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="mt-0.5 h-4 w-4 text-orange-600" />
                  <div className="text-sm text-orange-800">
                    <strong>Age Verification Required:</strong>
                    <span className="ml-1">
                      Customers will need to verify they meet the minimum age
                      requirement before purchasing.
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Product Type Features Card */}
      <Card className="border-l-4 border-l-purple-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Layers className="h-5 w-5 text-purple-600" />
            Product Type Features
            <Badge variant="secondary" className="text-xs">
              Optional
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Enable special features based on product type
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Downloadable Product */}
            <div className="flex items-center space-x-3">
              <Switch
                id="downloadable"
                checked={downloadable}
                onCheckedChange={(checked) => setValue("downloadable", checked)}
              />
              <Label htmlFor="downloadable" className="text-base font-medium">
                Downloadable Product
              </Label>
            </div>

            {/* Virtual Product */}
            <div className="flex items-center space-x-3">
              <Switch
                id="virtual"
                checked={virtual}
                onCheckedChange={(checked) => setValue("virtual", checked)}
              />
              <Label htmlFor="virtual" className="text-base font-medium">
                Virtual Product (No shipping required)
              </Label>
            </div>

            {/* Download/Virtual Info */}
            {(downloadable || virtual) && (
              <div className="rounded-lg border border-purple-200 bg-purple-50 p-3">
                <div className="flex items-start gap-2">
                  <Zap className="mt-0.5 h-4 w-4 text-purple-600" />
                  <div className="text-sm text-purple-800">
                    <strong>Special Product Features:</strong>
                    <ul className="mt-1 list-inside list-disc space-y-1">
                      {downloadable && (
                        <li>Customers can download files after purchase</li>
                      )}
                      {virtual && <li>No physical shipping required</li>}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Custom Fields - Collapsible */}
      <CollapsibleSection
        title="Custom Fields"
        description="Add custom attributes and metadata for this product"
        icon={<Code className="h-5 w-5 text-green-600" />}
        borderColor="border-l-green-500"
        defaultOpen={false}
        isOptional={true}
      >
        <div className="space-y-4">
          {/* Custom Attributes */}
          <FormField
            id="customAttributes"
            label="Custom Attributes (JSON)"
            optional={true}
          >
            <Textarea
              id="customAttributes"
              {...register("customAttributes")}
              placeholder='{"color": "red", "material": "cotton", "origin": "USA"}'
              rows={3}
              className="resize-none border-2 font-mono text-sm focus:border-green-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Enter custom attributes as JSON format for advanced product data.
            </p>
          </FormField>

          {/* Internal Notes */}
          <FormField id="internalNotes" label="Internal Notes" optional={true}>
            <Textarea
              id="internalNotes"
              {...register("internalNotes")}
              placeholder="Internal notes for staff only - not visible to customers..."
              rows={3}
              className="resize-none border-2 focus:border-green-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              These notes are only visible to store administrators and staff.
            </p>
          </FormField>
        </div>
      </CollapsibleSection>

      {/* System Information Card */}
      <Card className="border-l-4 border-l-gray-500">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Database className="h-5 w-5 text-gray-600" />
            System Information
            <Badge variant="secondary" className="text-xs">
              Read Only
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            System-generated information and timestamps
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="rounded-lg bg-gray-50 p-3">
                <div className="mb-1 flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-600" />
                  <span className="font-medium text-gray-900">Created</span>
                </div>
                <p className="text-sm text-gray-600">Will be set on save</p>
              </div>

              <div className="rounded-lg bg-gray-50 p-3">
                <div className="mb-1 flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-600" />
                  <span className="font-medium text-gray-900">
                    Last Updated
                  </span>
                </div>
                <p className="text-sm text-gray-600">Will be set on save</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings Summary Card */}
      <Card className="border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-800">
            <Settings className="h-5 w-5" />
            Settings Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <Target className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Status</h4>
                <p className="text-sm text-blue-700">{status || "Not set"}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="rounded-full bg-orange-100 p-2">
                <Shield className="h-4 w-4 text-orange-600" />
              </div>
              <div>
                <h4 className="font-medium text-orange-900">Age Restriction</h4>
                <p className="text-sm text-orange-700">
                  {ageRestrictions.find((r) => r.value === ageRestriction)
                    ?.label || "None"}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="rounded-full bg-purple-100 p-2">
                <Layers className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium text-purple-900">
                  Special Features
                </h4>
                <p className="text-sm text-purple-700">
                  {downloadable || virtual
                    ? `${downloadable ? "Downloadable" : ""} ${virtual ? "Virtual" : ""}`.trim()
                    : "None"}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
