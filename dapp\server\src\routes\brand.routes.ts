import { Router } from "express";

import { BrandController } from "../controllers/brand/brand.controller";

const router = Router();
const brandController = new BrandController();

// GET /api/brands - Get all brands with optional filtering
router.get("/", brandController.getBrands);

// GET /api/brands/slug/:slug - Get brand by slug
router.get("/slug/:slug", brandController.getBrandBySlug);

// POST /api/brands/recalculate-counts - Recalculate product counts
router.post("/recalculate-counts", brandController.recalculateProductCounts);

// GET /api/brands/:id - Get brand by ID
router.get("/:id", brandController.getBrandById);

// POST /api/brands - Create a new brand
router.post("/", brandController.createBrand);

// PUT /api/brands/:id - Update a brand
router.put("/:id", brandController.updateBrand);

// DELETE /api/brands/:id - Delete a brand
router.delete("/:id", brandController.deleteBrand);

export default router;
