import { Schema, model } from "mongoose";

import { ColorDocument } from "../types/color.types";

/**
 * MongoDB schema for Color
 */
const colorSchema = new Schema<ColorDocument>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      index: true,
    },
    description: {
      type: String,
      default: "",
      trim: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      index: true,
    },
    hexValue: {
      type: String,
      required: true,
      match: /^#[0-9A-F]{6}$/i, // Hex color validation
      index: true,
    },
    rgbValue: {
      r: {
        type: Number,
        required: true,
        min: 0,
        max: 255,
      },
      g: {
        type: Number,
        required: true,
        min: 0,
        max: 255,
      },
      b: {
        type: Number,
        required: true,
        min: 0,
        max: 255,
      },
    },
    hslValue: {
      h: {
        type: Number,
        required: true,
        min: 0,
        max: 360,
      },
      s: {
        type: Number,
        required: true,
        min: 0,
        max: 100,
      },
      l: {
        type: Number,
        required: true,
        min: 0,
        max: 100,
      },
    },
    colorFamily: {
      type: String,
      enum: ["red", "orange", "yellow", "green", "blue", "purple", "pink", "brown", "gray", "black", "white"],
      required: true,
      index: true,
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    productCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    sortOrder: {
      type: Number,
      default: 0,
      index: true,
    },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt
    versionKey: false, // Removes __v field
  }
);

// Add compound indexes for common queries
colorSchema.index({ isActive: 1, sortOrder: 1 });
colorSchema.index({ colorFamily: 1, sortOrder: 1 });

// Add text index for search functionality
colorSchema.index(
  {
    name: "text",
    description: "text",
  },
  {
    weights: {
      name: 10,
      description: 1,
    },
    name: "color_text_index",
  }
);

// Pre-save middleware to generate slug and color values if not provided
colorSchema.pre("save", function (next) {
  if (this.isModified("name") && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  }

  // Auto-generate RGB and HSL values from hex if they're not provided
  if (this.isModified("hexValue")) {
    const rgb = this.hexToRgb(this.hexValue);
    const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b);
    
    if (!this.rgbValue || this.isModified("hexValue")) {
      this.rgbValue = rgb;
    }
    
    if (!this.hslValue || this.isModified("hexValue")) {
      this.hslValue = hsl;
    }
  }

  next();
});

// Helper methods
colorSchema.methods.hexToRgb = function(hex: string) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : { r: 0, g: 0, b: 0 };
};

colorSchema.methods.rgbToHsl = function(r: number, g: number, b: number) {
  r /= 255;
  g /= 255;
  b /= 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0, l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  };
};

// Create and export the model
export const Color = model<ColorDocument>("Color", colorSchema);
