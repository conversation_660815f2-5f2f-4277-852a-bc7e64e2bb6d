import { products } from "./products";

export type PromotionType = "percentage" | "fixed-amount" | "buy-x-get-y" | "free-shipping";
export type PromotionStatus = "active" | "scheduled" | "paused" | "expired" | "draft";

export type Promotion = {
  id: string;
  name: string;
  description: string;
  type: PromotionType;
  status: PromotionStatus;
  
  // Discount details
  discountValue: number; // percentage or fixed amount
  minimumOrderAmount?: number;
  maximumDiscountAmount?: number;
  
  // Product targeting
  applicableProducts: string[]; // product IDs
  excludedProducts?: string[];
  categories?: string[];
  
  // Timing
  startDate: string;
  endDate?: string;
  
  // Usage limits
  usageLimit?: number;
  usageCount: number;
  perCustomerLimit?: number;
  
  // Conditions
  requiresCouponCode?: boolean;
  couponCode?: string;
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  createdBy: string;
};

export const mockPromotions: Promotion[] = [
  {
    id: "PROMO-001",
    name: "Winter Sale 2024",
    description: "20% off on all winter clothing and accessories",
    type: "percentage",
    status: "active",
    discountValue: 20,
    minimumOrderAmount: 50,
    maximumDiscountAmount: 100,
    applicableProducts: ["1", "3", "5"], // T-shirt, Jeans shorts, Winter Jacket
    categories: ["Clothing"],
    startDate: "2024-01-01T00:00:00Z",
    endDate: "2024-02-29T23:59:59Z",
    usageLimit: 1000,
    usageCount: 245,
    perCustomerLimit: 1,
    requiresCouponCode: false,
    createdAt: "2023-12-15T10:00:00Z",
    updatedAt: "2024-01-15T14:30:00Z",
    createdBy: "admin",
  },
  {
    id: "PROMO-002",
    name: "New Customer Welcome",
    description: "€15 off your first order over €75",
    type: "fixed-amount",
    status: "active",
    discountValue: 15,
    minimumOrderAmount: 75,
    applicableProducts: products.map(p => p.id.toString()),
    startDate: "2024-01-01T00:00:00Z",
    usageLimit: 500,
    usageCount: 89,
    perCustomerLimit: 1,
    requiresCouponCode: true,
    couponCode: "WELCOME15",
    createdAt: "2024-01-01T09:00:00Z",
    updatedAt: "2024-01-10T16:20:00Z",
    createdBy: "admin",
  },
  {
    id: "PROMO-003",
    name: "Flash Sale - Electronics",
    description: "30% off selected electronics for 24 hours",
    type: "percentage",
    status: "expired",
    discountValue: 30,
    applicableProducts: ["4", "6"], // Smart Watch, Running Shoes
    categories: ["Electronics"],
    startDate: "2024-01-10T00:00:00Z",
    endDate: "2024-01-11T00:00:00Z",
    usageLimit: 100,
    usageCount: 67,
    perCustomerLimit: 2,
    requiresCouponCode: false,
    createdAt: "2024-01-09T15:00:00Z",
    updatedAt: "2024-01-11T00:01:00Z",
    createdBy: "admin",
  },
  {
    id: "PROMO-004",
    name: "Buy 2 Get 1 Free - Accessories",
    description: "Buy any 2 accessories and get the cheapest one free",
    type: "buy-x-get-y",
    status: "scheduled",
    discountValue: 100, // 100% off the free item
    applicableProducts: ["2", "7"], // Travel Bag, Leather Wallet
    categories: ["Accessories"],
    startDate: "2024-02-01T00:00:00Z",
    endDate: "2024-02-14T23:59:59Z",
    usageLimit: 200,
    usageCount: 0,
    perCustomerLimit: 3,
    requiresCouponCode: false,
    createdAt: "2024-01-20T11:00:00Z",
    updatedAt: "2024-01-20T11:00:00Z",
    createdBy: "admin",
  },
  {
    id: "PROMO-005",
    name: "Free Shipping Weekend",
    description: "Free shipping on all orders over €25",
    type: "free-shipping",
    status: "paused",
    discountValue: 0,
    minimumOrderAmount: 25,
    applicableProducts: products.map(p => p.id.toString()),
    startDate: "2024-01-19T00:00:00Z",
    endDate: "2024-01-21T23:59:59Z",
    usageLimit: 1000,
    usageCount: 156,
    requiresCouponCode: false,
    createdAt: "2024-01-18T14:00:00Z",
    updatedAt: "2024-01-19T10:30:00Z",
    createdBy: "admin",
  },
  {
    id: "PROMO-006",
    name: "VIP Customer Exclusive",
    description: "25% off for premium customers only",
    type: "percentage",
    status: "draft",
    discountValue: 25,
    minimumOrderAmount: 100,
    maximumDiscountAmount: 200,
    applicableProducts: products.map(p => p.id.toString()),
    startDate: "2024-03-01T00:00:00Z",
    endDate: "2024-03-31T23:59:59Z",
    usageLimit: 50,
    usageCount: 0,
    perCustomerLimit: 1,
    requiresCouponCode: true,
    couponCode: "VIP25",
    createdAt: "2024-01-25T16:00:00Z",
    updatedAt: "2024-01-25T16:00:00Z",
    createdBy: "admin",
  },
];

// Promotion statistics
export const promotionStats = {
  totalPromotions: mockPromotions.length,
  activePromotions: mockPromotions.filter(p => p.status === "active").length,
  scheduledPromotions: mockPromotions.filter(p => p.status === "scheduled").length,
  totalUsage: mockPromotions.reduce((sum, p) => sum + p.usageCount, 0),
  totalSavings: mockPromotions.reduce((sum, p) => {
    // Estimate savings based on usage and discount
    const avgOrderValue = 150; // Estimated average order value
    if (p.type === "percentage") {
      return sum + (p.usageCount * avgOrderValue * (p.discountValue / 100));
    } else if (p.type === "fixed-amount") {
      return sum + (p.usageCount * p.discountValue);
    }
    return sum;
  }, 0),
};

// Product promotion data for easy lookup
export const productPromotions = products.map(product => {
  const activePromotions = mockPromotions.filter(promo => 
    promo.status === "active" && 
    promo.applicableProducts.includes(product.id.toString())
  );
  
  const bestDiscount = activePromotions.reduce((best, current) => {
    if (current.type === "percentage" && current.discountValue > best) {
      return current.discountValue;
    }
    return best;
  }, 0);

  return {
    ...product,
    hasActivePromotion: activePromotions.length > 0,
    activePromotions: activePromotions,
    bestDiscountPercentage: bestDiscount,
    promotionCount: activePromotions.length,
  };
});
