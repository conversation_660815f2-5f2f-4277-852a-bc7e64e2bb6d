import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";

import { ColorService } from "../../services/color.service";
import { CreateColorDto, UpdateColorDto, ColorFamily } from "../../types/color.types";

/**
 * Controller for handling color-related HTTP requests
 */
export class ColorController {
  private colorService: ColorService;

  constructor() {
    this.colorService = new ColorService();
  }

  /**
   * Create a new color
   * POST /api/colors
   */
  createColor = async (req: Request, res: Response): Promise<void> => {
    try {
      const colorData: CreateColorDto = req.body;
      const color = await this.colorService.createColor(colorData);

      res.status(StatusCodes.CREATED).json({
        success: true,
        data: color,
        message: "Color created successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create color";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get all colors with optional filtering
   * GET /api/colors
   */
  getColors = async (req: Request, res: Response): Promise<void> => {
    try {
      const filters = {
        isActive:
          req.query.isActive === "true"
            ? true
            : req.query.isActive === "false"
              ? false
              : undefined,
        search: req.query.search as string,
        colorFamily: req.query.colorFamily as ColorFamily,
        hexValue: req.query.hexValue as string,
      };

      const colors = await this.colorService.getColors(filters);

      res.status(StatusCodes.OK).json({
        success: true,
        data: colors,
        count: colors.length,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch colors";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a color by ID
   * GET /api/colors/:id
   */
  getColorById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const color = await this.colorService.getColorById(id);

      if (!color) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Color not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: color,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch color";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a color by slug
   * GET /api/colors/slug/:slug
   */
  getColorBySlug = async (req: Request, res: Response): Promise<void> => {
    try {
      const { slug } = req.params;
      const color = await this.colorService.getColorBySlug(slug);

      if (!color) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Color not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: color,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch color";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get a color by hex value
   * GET /api/colors/hex/:hex
   */
  getColorByHex = async (req: Request, res: Response): Promise<void> => {
    try {
      const { hex } = req.params;
      // Add # prefix if not present
      const hexValue = hex.startsWith("#") ? hex : `#${hex}`;
      const color = await this.colorService.getColorByHex(hexValue);

      if (!color) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Color not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: color,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch color";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Get colors grouped by color family
   * GET /api/colors/families
   */
  getColorsByFamily = async (req: Request, res: Response): Promise<void> => {
    try {
      const colorsByFamily = await this.colorService.getColorsByFamily();

      res.status(StatusCodes.OK).json({
        success: true,
        data: colorsByFamily,
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch colors by family";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Update a color
   * PUT /api/colors/:id
   */
  updateColor = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData: UpdateColorDto = req.body;

      const color = await this.colorService.updateColor(id, updateData);

      if (!color) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Color not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        data: color,
        message: "Color updated successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update color";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Delete a color
   * DELETE /api/colors/:id
   */
  deleteColor = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.colorService.deleteColor(id);

      if (!deleted) {
        res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: "Color not found",
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        success: true,
        message: "Color deleted successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete color";

      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: errorMessage,
      });
    }
  };

  /**
   * Recalculate product counts for all colors
   * POST /api/colors/recalculate-counts
   */
  recalculateProductCounts = async (
    req: Request,
    res: Response
  ): Promise<void> => {
    try {
      await this.colorService.recalculateProductCounts();

      res.status(StatusCodes.OK).json({
        success: true,
        message: "Color product counts recalculated successfully",
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to recalculate color product counts";

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: errorMessage,
      });
    }
  };
}
