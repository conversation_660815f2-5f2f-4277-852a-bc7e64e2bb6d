import { Schema, model } from "mongoose";

import { BrandDocument } from "../types/brand.types";

/**
 * MongoDB schema for Brand
 */
const brandSchema = new Schema<BrandDocument>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      index: true,
    },
    description: {
      type: String,
      default: "",
      trim: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      index: true,
    },
    logo: {
      type: String,
      default: "",
    },
    website: {
      type: String,
      default: "",
      trim: true,
    },
    color: {
      type: String,
      default: "#3B82F6",
      match: /^#[0-9A-F]{6}$/i, // Hex color validation
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    productCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    sortOrder: {
      type: Number,
      default: 0,
      index: true,
    },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt
    versionKey: false, // Removes __v field
  }
);

// Add compound indexes for common queries
brandSchema.index({ isActive: 1, sortOrder: 1 });

// Add text index for search functionality
brandSchema.index(
  {
    name: "text",
    description: "text",
  },
  {
    weights: {
      name: 10,
      description: 1,
    },
    name: "brand_text_index",
  }
);

// Pre-save middleware to generate slug if not provided
brandSchema.pre("save", function (next) {
  if (this.isModified("name") && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  }
  next();
});

// Create and export the model
export const Brand = model<BrandDocument>("Brand", brandSchema);
