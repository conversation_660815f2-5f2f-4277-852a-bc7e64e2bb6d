"use client";

import { useState } from "react";
import { format } from "date-fns";
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Copy, 
  Pause, 
  Play, 
  Trash2,
  Calendar,
  Users,
  Percent,
  DollarSign,
  Gift,
  Truck
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { mockPromotions, Promotion, PromotionStatus } from "@/constants/promotions";

const getStatusBadge = (status: PromotionStatus) => {
  const statusConfig = {
    active: { variant: "default" as const, className: "bg-green-100 text-green-800 hover:bg-green-100" },
    scheduled: { variant: "secondary" as const, className: "bg-blue-100 text-blue-800 hover:bg-blue-100" },
    paused: { variant: "secondary" as const, className: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100" },
    expired: { variant: "outline" as const, className: "bg-gray-100 text-gray-800 hover:bg-gray-100" },
    draft: { variant: "outline" as const, className: "bg-gray-100 text-gray-600 hover:bg-gray-100" },
  };

  const config = statusConfig[status] || statusConfig.draft;
  
  return (
    <Badge variant={config.variant} className={config.className}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

const getPromotionTypeIcon = (type: string) => {
  switch (type) {
    case "percentage":
      return <Percent className="h-4 w-4 text-blue-600" />;
    case "fixed-amount":
      return <DollarSign className="h-4 w-4 text-green-600" />;
    case "buy-x-get-y":
      return <Gift className="h-4 w-4 text-purple-600" />;
    case "free-shipping":
      return <Truck className="h-4 w-4 text-orange-600" />;
    default:
      return <Percent className="h-4 w-4 text-gray-600" />;
  }
};

const getPromotionTypeLabel = (type: string, value: number) => {
  switch (type) {
    case "percentage":
      return `${value}% off`;
    case "fixed-amount":
      return `€${value} off`;
    case "buy-x-get-y":
      return "Buy X Get Y";
    case "free-shipping":
      return "Free shipping";
    default:
      return "Special offer";
  }
};

export const PromotionsTable = () => {
  const [promotions, setPromotions] = useState(mockPromotions);

  const togglePromotionStatus = (promotionId: string) => {
    setPromotions(prev => prev.map(promo => {
      if (promo.id === promotionId) {
        const newStatus: PromotionStatus = promo.status === "active" ? "paused" : "active";
        return { ...promo, status: newStatus };
      }
      return promo;
    }));
  };

  const getUsageProgress = (used: number, limit?: number) => {
    if (!limit) return 0;
    return Math.min((used / limit) * 100, 100);
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-white">
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b bg-gray-50/50 text-gray-500">
              <th className="p-4 text-left font-medium">Promotion</th>
              <th className="p-4 text-center font-medium">Type & Value</th>
              <th className="p-4 text-center font-medium">Status</th>
              <th className="p-4 text-center font-medium">Usage</th>
              <th className="p-4 text-center font-medium">Duration</th>
              <th className="p-4 text-center font-medium">Quick Toggle</th>
              <th className="p-4 text-center font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {promotions.map((promotion) => (
              <tr key={promotion.id} className="border-b hover:bg-gray-50/50">
                {/* Promotion Info */}
                <td className="p-4">
                  <div className="flex flex-col">
                    <span className="font-medium text-gray-900">
                      {promotion.name}
                    </span>
                    <span className="text-xs text-gray-500 mt-1">
                      {promotion.description}
                    </span>
                    {promotion.couponCode && (
                      <span className="text-xs text-blue-600 mt-1 font-mono">
                        Code: {promotion.couponCode}
                      </span>
                    )}
                  </div>
                </td>

                {/* Type & Value */}
                <td className="p-4 text-center">
                  <div className="flex items-center justify-center gap-2">
                    {getPromotionTypeIcon(promotion.type)}
                    <span className="font-medium">
                      {getPromotionTypeLabel(promotion.type, promotion.discountValue)}
                    </span>
                  </div>
                  {promotion.minimumOrderAmount && (
                    <div className="text-xs text-gray-500 mt-1">
                      Min: €{promotion.minimumOrderAmount}
                    </div>
                  )}
                </td>

                {/* Status */}
                <td className="p-4 text-center">
                  {getStatusBadge(promotion.status)}
                </td>

                {/* Usage */}
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3 text-gray-400" />
                      <span className="font-medium">
                        {promotion.usageCount}
                        {promotion.usageLimit && ` / ${promotion.usageLimit}`}
                      </span>
                    </div>
                    {promotion.usageLimit && (
                      <div className="w-16 bg-gray-200 rounded-full h-1.5 mt-1">
                        <div 
                          className="bg-blue-600 h-1.5 rounded-full transition-all"
                          style={{ width: `${getUsageProgress(promotion.usageCount, promotion.usageLimit)}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                </td>

                {/* Duration */}
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span className="text-xs">
                        {format(new Date(promotion.startDate), "MMM dd")}
                      </span>
                    </div>
                    {promotion.endDate && (
                      <span className="text-xs text-gray-500">
                        to {format(new Date(promotion.endDate), "MMM dd")}
                      </span>
                    )}
                  </div>
                </td>

                {/* Quick Toggle */}
                <td className="p-4 text-center">
                  <div className="flex items-center justify-center">
                    <Switch
                      checked={promotion.status === "active"}
                      onCheckedChange={() => togglePromotionStatus(promotion.id)}
                      disabled={promotion.status === "expired" || promotion.status === "draft"}
                    />
                  </div>
                </td>

                {/* Actions */}
                <td className="p-4 text-center">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Promotion
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Copy className="mr-2 h-4 w-4" />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {promotion.status === "active" ? (
                        <DropdownMenuItem onClick={() => togglePromotionStatus(promotion.id)}>
                          <Pause className="mr-2 h-4 w-4" />
                          Pause Promotion
                        </DropdownMenuItem>
                      ) : promotion.status === "paused" ? (
                        <DropdownMenuItem onClick={() => togglePromotionStatus(promotion.id)}>
                          <Play className="mr-2 h-4 w-4" />
                          Resume Promotion
                        </DropdownMenuItem>
                      ) : null}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Promotion
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
