import { Category } from "@/components/pages/management/CategoryManager";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:3010/api";

export type CreateCategoryDto = {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive?: boolean;
  parentId?: string;
  sortOrder?: number;
};

export type UpdateCategoryDto = Partial<CreateCategoryDto>;

export type CategoryFilters = {
  isActive?: boolean;
  parentId?: string;
  search?: string;
};

export type ApiResponse<T> = {
  success: boolean;
  data?: T;
  message?: string;
  count?: number;
};

/**
 * Category API service for frontend-backend communication
 */
export class CategoryApiService {
  private static baseUrl = `${API_BASE_URL}/api/categories`;

  /**
   * Transform backend category to frontend format
   */
  private static transformCategory(backendCategory: any): Category {
    console.log("Transforming category:", backendCategory); // Debug log
    const transformed = {
      id: backendCategory._id || backendCategory.id,
      name: backendCategory.name,
      description: backendCategory.description,
      slug: backendCategory.slug,
      icon: backendCategory.icon,
      color: backendCategory.color,
      isActive: backendCategory.isActive,
      productCount: backendCategory.productCount,
      parentId: backendCategory.parentId,
      sortOrder: backendCategory.sortOrder,
      createdAt: backendCategory.createdAt,
      updatedAt: backendCategory.updatedAt,
    };
    console.log("Transformed category:", transformed); // Debug log
    return transformed;
  }

  /**
   * Get all categories with optional filtering
   */
  static async getCategories(filters?: CategoryFilters): Promise<Category[]> {
    try {
      const params = new URLSearchParams();

      if (filters?.isActive !== undefined) {
        params.append("isActive", filters.isActive.toString());
      }
      if (filters?.parentId) {
        params.append("parentId", filters.parentId);
      }
      if (filters?.search) {
        params.append("search", filters.search);
      }

      const url = `${this.baseUrl}${params.toString() ? `?${params.toString()}` : ""}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any[]> = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to fetch categories");
      }

      const categories = (result.data || []).map(this.transformCategory);
      return categories;
    } catch (error) {
      console.error("Error fetching categories:", error);
      throw error;
    }
  }

  /**
   * Get a category by ID
   */
  static async getCategoryById(id: string): Promise<Category> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to fetch category");
      }

      if (!result.data) {
        throw new Error("Category not found");
      }

      return this.transformCategory(result.data);
    } catch (error) {
      console.error("Error fetching category:", error);
      throw error;
    }
  }

  /**
   * Get a category by slug
   */
  static async getCategoryBySlug(slug: string): Promise<Category> {
    try {
      const response = await fetch(`${this.baseUrl}/slug/${slug}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to fetch category");
      }

      if (!result.data) {
        throw new Error("Category not found");
      }

      return this.transformCategory(result.data);
    } catch (error) {
      console.error("Error fetching category:", error);
      throw error;
    }
  }

  /**
   * Create a new category
   */
  static async createCategory(
    categoryData: CreateCategoryDto
  ): Promise<Category> {
    try {
      const response = await fetch(this.baseUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(categoryData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to create category");
      }

      if (!result.data) {
        throw new Error("No category data returned");
      }

      return this.transformCategory(result.data);
    } catch (error) {
      console.error("Error creating category:", error);
      throw error;
    }
  }

  /**
   * Update a category
   */
  static async updateCategory(
    id: string,
    updateData: UpdateCategoryDto
  ): Promise<Category> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to update category");
      }

      if (!result.data) {
        throw new Error("No category data returned");
      }

      return this.transformCategory(result.data);
    } catch (error) {
      console.error("Error updating category:", error);
      throw error;
    }
  }

  /**
   * Delete a category
   */
  static async deleteCategory(id: string): Promise<void> {
    try {
      console.log("Deleting category with ID:", id); // Debug log
      if (!id || id === "undefined") {
        throw new Error("Category ID is required for deletion");
      }

      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<null> = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to delete category");
      }
    } catch (error) {
      console.error("Error deleting category:", error);
      throw error;
    }
  }

  /**
   * Recalculate product counts for all categories
   */
  static async recalculateProductCounts(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/recalculate-counts`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(
          result.message || "Failed to recalculate product counts"
        );
      }
    } catch (error) {
      console.error("Error recalculating product counts:", error);
      throw error;
    }
  }
}
