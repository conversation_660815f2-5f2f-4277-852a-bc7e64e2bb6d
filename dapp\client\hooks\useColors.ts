"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

import { ColorApi, Color, ColorFilters, CreateColorDto, UpdateColorDto, ColorFamily } from "@/lib/api/colors";

/**
 * Custom hook for managing colors data and operations
 */
export const useColors = (initialFilters?: ColorFilters) => {
  const [colors, setColors] = useState<Color[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ColorFilters>(initialFilters || {});

  /**
   * Fetch colors from the API
   */
  const fetchColors = useCallback(async (currentFilters?: ColorFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const filtersToUse = currentFilters || filters;
      const response = await ColorApi.getColors(filtersToUse);
      
      if (response.success) {
        setColors(response.data);
      } else {
        throw new Error("Failed to fetch colors");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch colors";
      setError(errorMessage);
      console.error("Error fetching colors:", err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  /**
   * Create a new color
   */
  const createColor = useCallback(async (colorData: CreateColorDto): Promise<Color | null> => {
    try {
      const response = await ColorApi.createColor(colorData);
      
      if (response.success) {
        setColors(prev => [...prev, response.data]);
        toast.success("Color created successfully");
        return response.data;
      } else {
        throw new Error("Failed to create color");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to create color";
      toast.error(errorMessage);
      console.error("Error creating color:", err);
      return null;
    }
  }, []);

  /**
   * Update an existing color
   */
  const updateColor = useCallback(async (id: string, updateData: UpdateColorDto): Promise<Color | null> => {
    try {
      const response = await ColorApi.updateColor(id, updateData);
      
      if (response.success) {
        setColors(prev => 
          prev.map(color => 
            color._id === id ? response.data : color
          )
        );
        toast.success("Color updated successfully");
        return response.data;
      } else {
        throw new Error("Failed to update color");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update color";
      toast.error(errorMessage);
      console.error("Error updating color:", err);
      return null;
    }
  }, []);

  /**
   * Delete a color
   */
  const deleteColor = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await ColorApi.deleteColor(id);
      
      if (response.success) {
        setColors(prev => prev.filter(color => color._id !== id));
        toast.success("Color deleted successfully");
        return true;
      } else {
        throw new Error("Failed to delete color");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete color";
      toast.error(errorMessage);
      console.error("Error deleting color:", err);
      return false;
    }
  }, []);

  /**
   * Get a color by ID
   */
  const getColorById = useCallback(async (id: string): Promise<Color | null> => {
    try {
      const response = await ColorApi.getColorById(id);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error("Failed to fetch color");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch color";
      console.error("Error fetching color by ID:", err);
      return null;
    }
  }, []);

  /**
   * Get a color by slug
   */
  const getColorBySlug = useCallback(async (slug: string): Promise<Color | null> => {
    try {
      const response = await ColorApi.getColorBySlug(slug);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error("Failed to fetch color");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch color";
      console.error("Error fetching color by slug:", err);
      return null;
    }
  }, []);

  /**
   * Get a color by hex value
   */
  const getColorByHex = useCallback(async (hex: string): Promise<Color | null> => {
    try {
      const response = await ColorApi.getColorByHex(hex);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error("Failed to fetch color");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch color";
      console.error("Error fetching color by hex:", err);
      return null;
    }
  }, []);

  /**
   * Get colors grouped by color family
   */
  const getColorsByFamily = useCallback(async (): Promise<Record<ColorFamily, Color[]> | null> => {
    try {
      const response = await ColorApi.getColorsByFamily();
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error("Failed to fetch colors by family");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch colors by family";
      console.error("Error fetching colors by family:", err);
      return null;
    }
  }, []);

  /**
   * Recalculate product counts for all colors
   */
  const recalculateProductCounts = useCallback(async (): Promise<boolean> => {
    try {
      const response = await ColorApi.recalculateProductCounts();
      
      if (response.success) {
        // Refresh colors to get updated counts
        await fetchColors();
        toast.success("Color product counts recalculated successfully");
        return true;
      } else {
        throw new Error("Failed to recalculate product counts");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to recalculate product counts";
      toast.error(errorMessage);
      console.error("Error recalculating product counts:", err);
      return false;
    }
  }, [fetchColors]);

  /**
   * Update filters and refetch data
   */
  const updateFilters = useCallback((newFilters: ColorFilters) => {
    setFilters(newFilters);
    fetchColors(newFilters);
  }, [fetchColors]);

  /**
   * Refresh colors data
   */
  const refreshColors = useCallback(() => {
    fetchColors();
  }, [fetchColors]);

  // Initial data fetch
  useEffect(() => {
    fetchColors();
  }, [fetchColors]);

  return {
    colors,
    loading,
    error,
    filters,
    createColor,
    updateColor,
    deleteColor,
    getColorById,
    getColorBySlug,
    getColorByHex,
    getColorsByFamily,
    recalculateProductCounts,
    updateFilters,
    refreshColors,
  };
};
