import { ApiResponse } from "./types";

/**
 * Material properties interface
 */
export interface MaterialProperties {
  durability: "low" | "medium" | "high";
  waterResistant: boolean;
  recyclable: boolean;
  weight: "light" | "medium" | "heavy";
}

/**
 * Material interface matching the backend Material type
 */
export interface Material {
  _id: string;
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color?: string;
  properties: MaterialProperties;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO for creating a new material
 */
export interface CreateMaterialDto {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  properties?: Partial<MaterialProperties>;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * DTO for updating a material
 */
export interface UpdateMaterialDto {
  name?: string;
  description?: string;
  icon?: string;
  color?: string;
  properties?: Partial<MaterialProperties>;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * Material filters for querying
 */
export interface MaterialFilters {
  isActive?: boolean;
  search?: string;
  durability?: "low" | "medium" | "high";
  waterResistant?: boolean;
  recyclable?: boolean;
  weight?: "light" | "medium" | "heavy";
}

/**
 * API service for material management
 */
export class MaterialApi {
  private static readonly BASE_URL = "/api/materials";

  /**
   * Get all materials with optional filtering
   */
  static async getMaterials(filters?: MaterialFilters): Promise<ApiResponse<Material[]>> {
    try {
      const params = new URLSearchParams();
      
      if (filters?.isActive !== undefined) {
        params.append("isActive", filters.isActive.toString());
      }
      
      if (filters?.search) {
        params.append("search", filters.search);
      }

      if (filters?.durability) {
        params.append("durability", filters.durability);
      }

      if (filters?.waterResistant !== undefined) {
        params.append("waterResistant", filters.waterResistant.toString());
      }

      if (filters?.recyclable !== undefined) {
        params.append("recyclable", filters.recyclable.toString());
      }

      if (filters?.weight) {
        params.append("weight", filters.weight);
      }

      const queryString = params.toString();
      const url = queryString ? `${this.BASE_URL}?${queryString}` : this.BASE_URL;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching materials:", error);
      throw error;
    }
  }

  /**
   * Get a material by ID
   */
  static async getMaterialById(id: string): Promise<ApiResponse<Material>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching material:", error);
      throw error;
    }
  }

  /**
   * Get a material by slug
   */
  static async getMaterialBySlug(slug: string): Promise<ApiResponse<Material>> {
    try {
      const response = await fetch(`${this.BASE_URL}/slug/${slug}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching material by slug:", error);
      throw error;
    }
  }

  /**
   * Create a new material
   */
  static async createMaterial(materialData: CreateMaterialDto): Promise<ApiResponse<Material>> {
    try {
      const response = await fetch(this.BASE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(materialData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error creating material:", error);
      throw error;
    }
  }

  /**
   * Update a material
   */
  static async updateMaterial(id: string, updateData: UpdateMaterialDto): Promise<ApiResponse<Material>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error updating material:", error);
      throw error;
    }
  }

  /**
   * Delete a material
   */
  static async deleteMaterial(id: string): Promise<ApiResponse<null>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`, {
        method: "DELETE",
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error deleting material:", error);
      throw error;
    }
  }

  /**
   * Recalculate product counts for all materials
   */
  static async recalculateProductCounts(): Promise<ApiResponse<null>> {
    try {
      const response = await fetch(`${this.BASE_URL}/recalculate-counts`, {
        method: "POST",
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error recalculating material product counts:", error);
      throw error;
    }
  }
}
