"use client";

import React from "react";

import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  Download,
  Edit,
  Mail,
  MapPin,
  MoreHorizontal,
  Package,
  Phone,
  Printer,
  RefreshCw,
  Truck,
  User,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";

type OrderDetailsWrapperProps = {
  orderId: string;
};

export const OrderDetailsWrapper = ({ orderId }: OrderDetailsWrapperProps) => {
  const router = useRouter();

  const handleGoBack = () => {
    router.push("/admin/orders/list");
  };

  const handleEditOrder = () => {
    console.log("Edit order:", orderId);
  };

  const handlePrintOrder = () => {
    window.print();
  };

  const handleCancelOrder = () => {
    console.log("Cancel order:", orderId);
  };

  const handleRefundOrder = () => {
    console.log("Refund order:", orderId);
  };

  // Mock order data - in real app this would come from props or API
  const orderData = {
    id: orderId,
    status: "processing",
    paymentStatus: "paid",
    shippingStatus: "preparing",
    placedAt: "2024-01-15T10:30:00Z",
    estimatedDelivery: "2024-01-20",
    customer: {
      name: "John Alexander",
      email: "<EMAIL>",
      phone: "+998 99 22123456",
      avatar: "/avatars/customer-1.jpg",
    },
    shippingAddress: {
      street: "Beruniy 369, Block A, House 123",
      city: "Tashkent",
      country: "Uzbekistan",
      zipCode: "100000",
    },
    billingAddress: {
      street: "Beruniy 369, Block A, House 123",
      city: "Tashkent",
      country: "Uzbekistan",
      zipCode: "100000",
    },
    payment: {
      method: "Credit Card",
      cardLast4: "4768",
      cardType: "Mastercard",
    },
    shipping: {
      method: "Fargo Express",
      cost: 10.0,
      trackingNumber: "FE123456789",
    },
    items: [
      {
        id: "1",
        name: "Wireless Bluetooth Headphones",
        image: "/products/headphones.jpg",
        quantity: 2,
        price: 89.99,
        total: 179.98,
      },
      {
        id: "2",
        name: "Smart Watch Series 5",
        image: "/products/smartwatch.jpg",
        quantity: 1,
        price: 299.99,
        total: 299.99,
      },
      {
        id: "3",
        name: "USB-C Charging Cable",
        image: "/products/cable.jpg",
        quantity: 3,
        price: 19.99,
        total: 59.97,
      },
    ],
    subtotal: 539.94,
    shippingCost: 10.0,
    tax: 54.99,
    total: 604.93,
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleGoBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Orders
          </Button>

          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Order #{orderId}
            </h1>
            <p className="text-sm text-gray-500">
              Placed on{" "}
              {new Date(orderData.placedAt).toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              })}
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrintOrder}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            Print
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleEditOrder}
            className="flex items-center gap-2"
          >
            <Edit className="h-4 w-4" />
            Edit Order
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleRefundOrder}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refund Order
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                Download Invoice
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={handleCancelOrder}
              >
                <AlertCircle className="mr-2 h-4 w-4" />
                Cancel Order
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Status Overview Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Order Status
                </p>
                <Badge className={`mt-1 ${getStatusColor(orderData.status)}`}>
                  {orderData.status.charAt(0).toUpperCase() +
                    orderData.status.slice(1)}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <CreditCard className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Payment Status
                </p>
                <Badge
                  className={`mt-1 ${getPaymentStatusColor(orderData.paymentStatus)}`}
                >
                  {orderData.paymentStatus.charAt(0).toUpperCase() +
                    orderData.paymentStatus.slice(1)}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-purple-100 p-2">
                <Truck className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Estimated Delivery
                </p>
                <p className="mt-1 font-semibold text-gray-900">
                  {new Date(orderData.estimatedDelivery).toLocaleDateString(
                    "en-US",
                    {
                      month: "short",
                      day: "numeric",
                    }
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Order Items - Takes 2 columns */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-blue-600" />
                Order Items ({orderData.items.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {orderData.items.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center gap-4 rounded-lg border p-4"
                  >
                    <div className="h-16 w-16 overflow-hidden rounded-lg bg-gray-100">
                      <div className="flex h-full w-full items-center justify-center">
                        <Package className="h-6 w-6 text-gray-400" />
                      </div>
                    </div>

                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.name}</h4>
                      <p className="text-sm text-gray-500">
                        Quantity: {item.quantity}
                      </p>
                    </div>

                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        ${item.total.toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-500">
                        ${item.price.toFixed(2)} each
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-6" />

              {/* Order Summary */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="text-gray-900">
                    ${orderData.subtotal.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span className="text-gray-900">
                    ${orderData.shippingCost.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Tax</span>
                  <span className="text-gray-900">
                    ${orderData.tax.toFixed(2)}
                  </span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between text-lg font-semibold">
                  <span className="text-gray-900">Total</span>
                  <span className="text-gray-900">
                    ${orderData.total.toFixed(2)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - Customer & Shipping Info */}
        <div className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                  <User className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {orderData.customer.name}
                  </p>
                  <p className="text-sm text-gray-500">Customer</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {orderData.customer.email}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {orderData.customer.phone}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-green-600" />
                Shipping Address
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1 text-sm text-gray-600">
                <p className="font-medium text-gray-900">
                  {orderData.customer.name}
                </p>
                <p>{orderData.shippingAddress.street}</p>
                <p>
                  {orderData.shippingAddress.city},{" "}
                  {orderData.shippingAddress.zipCode}
                </p>
                <p>{orderData.shippingAddress.country}</p>
              </div>
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-purple-600" />
                Payment Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-12 items-center justify-center rounded bg-gray-100">
                  <CreditCard className="h-4 w-4 text-gray-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {orderData.payment.cardType} ••••{" "}
                    {orderData.payment.cardLast4}
                  </p>
                  <p className="text-sm text-gray-500">
                    {orderData.payment.method}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5 text-orange-600" />
                Shipping Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="font-medium text-gray-900">
                  {orderData.shipping.method}
                </p>
                <p className="text-sm text-gray-500">
                  Shipping Cost: ${orderData.shippingCost.toFixed(2)}
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700">
                  Tracking Number
                </p>
                <p className="font-mono text-sm text-blue-600">
                  {orderData.shipping.trackingNumber}
                </p>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600">
                  Est. delivery:{" "}
                  {new Date(orderData.estimatedDelivery).toLocaleDateString(
                    "en-US",
                    {
                      month: "long",
                      day: "numeric",
                    }
                  )}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
