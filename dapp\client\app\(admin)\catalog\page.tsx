"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>3,
  Download,
  <PERSON>,
  Layers,
  LayoutGrid,
  Palette,
  Plus,
  Tag,
  TrendingUp,
  Upload,
} from "lucide-react";
import { useRouter } from "next/navigation";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function CatalogOverview() {
  const router = useRouter();

  // Unified stats for all catalog metadata
  const overallStats = [
    {
      label: "Total Categories",
      value: "18",
      change: "+2",
      trend: "up",
      icon: LayoutGrid,
      href: "/admin/catalog/categories",
    },
    {
      label: "Total Brands",
      value: "24",
      change: "+3",
      trend: "up",
      icon: Tag,
      href: "/admin/catalog/brands",
    },
    {
      label: "Total Materials",
      value: "32",
      change: "+4",
      trend: "up",
      icon: Hammer,
      href: "/admin/catalog/materials",
    },
    {
      label: "Total Colors",
      value: "28",
      change: "+2",
      trend: "up",
      icon: Palette,
      href: "/admin/catalog/colors",
    },
  ];

  const quickActions = [
    { label: "Import Data", icon: Upload, action: () => console.log("Import") },
    {
      label: "Export All",
      icon: Download,
      action: () => console.log("Export"),
    },
    {
      label: "Catalog Analytics",
      icon: BarChart3,
      action: () => console.log("Analytics"),
    },
  ];

  // Cross-reference data showing relationships
  const catalogRelationships = [
    {
      category: "Electronics",
      brands: ["Apple", "Samsung", "Sony"],
      materials: ["Aluminum", "Glass", "Plastic"],
      colors: ["Black", "White", "Silver"],
      products: 324,
    },
    {
      category: "Clothing",
      brands: ["Nike", "Adidas", "H&M"],
      materials: ["Cotton", "Polyester", "Wool"],
      colors: ["Red", "Blue", "Green"],
      products: 289,
    },
    {
      category: "Home & Garden",
      brands: ["IKEA", "Home Depot", "Wayfair"],
      materials: ["Wood", "Metal", "Fabric"],
      colors: ["Brown", "White", "Gray"],
      products: 156,
    },
  ];

  const managementSections = [
    {
      title: "Categories",
      description: "Organize your product catalog",
      icon: LayoutGrid,
      color: "blue",
      href: "/admin/catalog/categories",
      stats: "18 categories",
    },
    {
      title: "Brands",
      description: "Add and organize product brands",
      icon: Tag,
      color: "green",
      href: "/admin/catalog/brands",
      stats: "24 brands",
    },
    {
      title: "Materials",
      description: "Define product materials",
      icon: Hammer,
      color: "purple",
      href: "/admin/catalog/materials",
      stats: "32 materials",
    },
    {
      title: "Colors",
      description: "Define product color options",
      icon: Palette,
      color: "pink",
      href: "/admin/catalog/colors",
      stats: "28 colors",
    },
  ];

  return (
    <>
      <PageHeaderWrapper
        title="Catalog Management"
        description="Manage categories, brands, materials, and colors for your product catalog"
      >
        <div className="flex gap-2">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={action.action}
              className="hidden sm:flex"
            >
              <action.icon className="mr-2 h-4 w-4" />
              {action.label}
            </Button>
          ))}
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Quick Add
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6 space-y-6">
        {/* Overall Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {overallStats.map((stat, index) => (
            <Card 
              key={index} 
              className="cursor-pointer transition-shadow hover:shadow-md"
              onClick={() => router.push(stat.href)}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.label}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </div>
                <div className="mt-1 flex items-center text-xs text-gray-500">
                  {stat.trend === "up" && (
                    <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                  )}
                  <span>{stat.change}</span>
                  {stat.trend === "up" && (
                    <span className="ml-1">this month</span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Management Sections */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {managementSections.map((section, index) => (
            <Card
              key={index}
              className="cursor-pointer transition-shadow hover:shadow-md"
              onClick={() => router.push(section.href)}
            >
              <CardContent className="p-6 text-center">
                <section.icon className={`mx-auto mb-3 h-8 w-8 text-${section.color}-600`} />
                <h3 className="mb-2 font-semibold text-gray-900">
                  Manage {section.title}
                </h3>
                <p className="mb-3 text-sm text-gray-600">
                  {section.description}
                </p>
                <Badge variant="secondary" className="mb-3">
                  {section.stats}
                </Badge>
                <div className={`flex items-center justify-center text-sm text-${section.color}-600`}>
                  <span>Go to {section.title}</span>
                  <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Catalog Relationships */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layers className="h-5 w-5 text-blue-600" />
              Catalog Relationships
            </CardTitle>
            <p className="text-sm text-gray-600">
              See how categories, brands, and materials work together in your catalog
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {catalogRelationships.map((item, index) => (
                <div
                  key={index}
                  className="rounded-lg border bg-gray-50 p-4"
                >
                  <div className="mb-3 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <LayoutGrid className="h-5 w-5 text-blue-600" />
                      <span className="font-semibold text-gray-900">
                        {item.category}
                      </span>
                      <Badge variant="secondary">
                        {item.products} products
                      </Badge>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <div className="mb-2 flex items-center gap-2">
                        <Tag className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">
                          Brands
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {item.brands.map((brand, idx) => (
                          <Badge
                            key={idx}
                            variant="outline"
                            className="text-xs cursor-pointer hover:bg-green-50"
                            onClick={() => router.push("/admin/catalog/brands")}
                          >
                            {brand}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <div className="mb-2 flex items-center gap-2">
                        <Hammer className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-gray-700">
                          Materials
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {item.materials.map((material, idx) => (
                          <Badge
                            key={idx}
                            variant="outline"
                            className="text-xs cursor-pointer hover:bg-purple-50"
                            onClick={() => router.push("/admin/catalog/materials")}
                          >
                            {material}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="mb-2 flex items-center gap-2">
                      <Palette className="h-4 w-4 text-pink-600" />
                      <span className="text-sm font-medium text-gray-700">
                        Colors
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {item.colors.map((color, idx) => (
                        <Badge
                          key={idx}
                          variant="outline"
                          className="text-xs cursor-pointer hover:bg-pink-50"
                          onClick={() => router.push("/admin/catalog/colors")}
                        >
                          {color}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
