{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/products.ts"], "sourcesContent": ["/**\r\n * API functions for product operations\r\n * Handles all HTTP requests to the backend product endpoints\r\n */\r\n\r\n// Types for API responses\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  meta?: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    pages: number;\r\n  };\r\n}\r\n\r\nexport interface ProductFilters {\r\n  search?: string;\r\n  category?: string;\r\n  brand?: string;\r\n  minPrice?: number;\r\n  maxPrice?: number;\r\n  condition?: string;\r\n  inStock?: boolean;\r\n  tags?: string[];\r\n  status?: string;\r\n  sortBy?: \"price\" | \"createdAt\" | \"name\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\n// Get API base URL from environment or default to localhost\r\nconst getApiBaseUrl = (): string => {\r\n  if (typeof window !== \"undefined\") {\r\n    // Client-side: use current origin or environment variable\r\n    return (\r\n      process.env.NEXT_PUBLIC_API_URL ||\r\n      `${window.location.protocol}//${window.location.hostname}:3001`\r\n    );\r\n  }\r\n  // Server-side: use environment variable or default\r\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\";\r\n};\r\n\r\nconst API_BASE_URL = getApiBaseUrl();\r\n\r\n/**\r\n * Generic fetch wrapper with error handling\r\n */\r\nasync function apiRequest<T>(\r\n  endpoint: string,\r\n  options: RequestInit = {}\r\n): Promise<ApiResponse<T>> {\r\n  const url = `${API_BASE_URL}/api${endpoint}`;\r\n\r\n  const defaultOptions: RequestInit = {\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      ...options.headers,\r\n    },\r\n    ...options,\r\n  };\r\n\r\n  console.log(`🌐 Making API request to: ${url}`);\r\n  console.log(`🌐 Request options:`, defaultOptions);\r\n\r\n  try {\r\n    const response = await fetch(url, defaultOptions);\r\n\r\n    console.log(`🌐 Response status: ${response.status}`);\r\n    console.log(`🌐 Response ok: ${response.ok}`);\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      console.error(`🌐 Response error text:`, errorText);\r\n      throw new Error(\r\n        `HTTP error! status: ${response.status}, message: ${errorText}`\r\n      );\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(`🌐 Response data:`, data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(`🌐 API request failed for ${endpoint}:`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Get all products with optional filtering\r\n */\r\nexport async function getProducts(\r\n  filters: ProductFilters = {}\r\n): Promise<ApiResponse<any[]>> {\r\n  const searchParams = new URLSearchParams();\r\n\r\n  // Add filters to search params\r\n  Object.entries(filters).forEach(([key, value]) => {\r\n    if (value !== undefined && value !== null && value !== \"\") {\r\n      if (Array.isArray(value)) {\r\n        value.forEach((item) => searchParams.append(key, item.toString()));\r\n      } else {\r\n        searchParams.append(key, value.toString());\r\n      }\r\n    }\r\n  });\r\n\r\n  const queryString = searchParams.toString();\r\n  const endpoint = queryString ? `/products?${queryString}` : \"/products\";\r\n\r\n  return apiRequest<any[]>(endpoint);\r\n}\r\n\r\n/**\r\n * Get a single product by ID\r\n */\r\nexport async function getProductById(id: string): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}`);\r\n}\r\n\r\n/**\r\n * Get a product for editing (with safe field list)\r\n */\r\nexport async function getProductForEdit(id: string): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}/edit`);\r\n}\r\n\r\n/**\r\n * Create a new product\r\n */\r\nexport async function createProduct(\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  console.log(\"🌐 API createProduct called with data:\", productData);\r\n  console.log(\"🌐 API Base URL:\", API_BASE_URL);\r\n\r\n  try {\r\n    const result = await apiRequest<any>(\"/products\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(productData),\r\n    });\r\n    console.log(\"🌐 API createProduct response:\", result);\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"🌐 API createProduct error:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Update an existing product\r\n */\r\nexport async function updateProduct(\r\n  id: string,\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}`, {\r\n    method: \"PUT\",\r\n    body: JSON.stringify(productData),\r\n  });\r\n}\r\n\r\n/**\r\n * Update product with safe fields (PATCH)\r\n */\r\nexport async function updateProductSafe(\r\n  id: string,\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}/edit`, {\r\n    method: \"PATCH\",\r\n    body: JSON.stringify(productData),\r\n  });\r\n}\r\n\r\n/**\r\n * Delete a product\r\n */\r\nexport async function deleteProduct(id: string): Promise<ApiResponse<boolean>> {\r\n  return apiRequest<boolean>(`/products/${id}`, {\r\n    method: \"DELETE\",\r\n  });\r\n}\r\n\r\n/**\r\n * Health check for API connection\r\n */\r\nexport async function checkApiHealth(): Promise<boolean> {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/api`);\r\n    return response.ok;\r\n  } catch (error) {\r\n    console.error(\"API health check failed:\", error);\r\n    return false;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0BAA0B;;;;;;;;;;;AAkCpB;AALN,4DAA4D;AAC5D,MAAM,gBAAgB;IACpB,wCAAmC;QACjC,0DAA0D;QAC1D,OACE,6DACA,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;IAEnE;;AAGF;AAEA,MAAM,eAAe;AAErB;;CAEC,GACD,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,UAAU;IAE5C,MAAM,iBAA8B;QAClC,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,KAAK;IAC9C,QAAQ,GAAG,CAAC,CAAC,mBAAmB,CAAC,EAAE;IAEnC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QACpD,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;QAE5C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,uBAAuB,CAAC,EAAE;YACzC,MAAM,IAAI,MACR,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW;QAEnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;QACjC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC,EAAE;QACxD,MAAM;IACR;AACF;AAKO,eAAe,YACpB,UAA0B,CAAC,CAAC;IAE5B,MAAM,eAAe,IAAI;IAEzB,+BAA+B;IAC/B,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAC,OAAS,aAAa,MAAM,CAAC,KAAK,KAAK,QAAQ;YAChE,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;YACzC;QACF;IACF;IAEA,MAAM,cAAc,aAAa,QAAQ;IACzC,MAAM,WAAW,cAAc,CAAC,UAAU,EAAE,aAAa,GAAG;IAE5D,OAAO,WAAkB;AAC3B;AAKO,eAAe,eAAe,EAAU;IAC7C,OAAO,WAAgB,CAAC,UAAU,EAAE,IAAI;AAC1C;AAKO,eAAe,kBAAkB,EAAU;IAChD,OAAO,WAAgB,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC;AAC/C;AAKO,eAAe,cACpB,WAAgB;IAEhB,QAAQ,GAAG,CAAC,0CAA0C;IACtD,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,IAAI;QACF,MAAM,SAAS,MAAM,WAAgB,aAAa;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAKO,eAAe,cACpB,EAAU,EACV,WAAgB;IAEhB,OAAO,WAAgB,CAAC,UAAU,EAAE,IAAI,EAAE;QACxC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe,kBACpB,EAAU,EACV,WAAgB;IAEhB,OAAO,WAAgB,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC,EAAE;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe,cAAc,EAAU;IAC5C,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI,EAAE;QAC5C,QAAQ;IACV;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,IAAI,CAAC;QAClD,OAAO,SAAS,EAAE;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/categoryApi.ts"], "sourcesContent": ["import { Category } from \"@/components/pages/management/CategoryManager\";\n\nconst API_BASE_URL =\n  process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3010/api\";\n\nexport type CreateCategoryDto = {\n  name: string;\n  description?: string;\n  icon?: string;\n  color?: string;\n  isActive?: boolean;\n  parentId?: string;\n  sortOrder?: number;\n};\n\nexport type UpdateCategoryDto = Partial<CreateCategoryDto>;\n\nexport type CategoryFilters = {\n  isActive?: boolean;\n  parentId?: string;\n  search?: string;\n};\n\nexport type ApiResponse<T> = {\n  success: boolean;\n  data?: T;\n  message?: string;\n  count?: number;\n};\n\n/**\n * Category API service for frontend-backend communication\n */\nexport class CategoryApiService {\n  private static baseUrl = `${API_BASE_URL}/api/categories`;\n\n  /**\n   * Transform backend category to frontend format\n   */\n  private static transformCategory(backendCategory: any): Category {\n    console.log(\"Transforming category:\", backendCategory); // Debug log\n    const transformed = {\n      id: backendCategory._id || backendCategory.id,\n      name: backendCategory.name,\n      description: backendCategory.description,\n      slug: backendCategory.slug,\n      icon: backendCategory.icon,\n      color: backendCategory.color,\n      isActive: backendCategory.isActive,\n      productCount: backendCategory.productCount,\n      parentId: backendCategory.parentId,\n      sortOrder: backendCategory.sortOrder,\n      createdAt: backendCategory.createdAt,\n      updatedAt: backendCategory.updatedAt,\n    };\n    console.log(\"Transformed category:\", transformed); // Debug log\n    return transformed;\n  }\n\n  /**\n   * Get all categories with optional filtering\n   */\n  static async getCategories(filters?: CategoryFilters): Promise<Category[]> {\n    try {\n      const params = new URLSearchParams();\n\n      if (filters?.isActive !== undefined) {\n        params.append(\"isActive\", filters.isActive.toString());\n      }\n      if (filters?.parentId) {\n        params.append(\"parentId\", filters.parentId);\n      }\n      if (filters?.search) {\n        params.append(\"search\", filters.search);\n      }\n\n      const url = `${this.baseUrl}${params.toString() ? `?${params.toString()}` : \"\"}`;\n      const response = await fetch(url);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any[]> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch categories\");\n      }\n\n      const categories = (result.data || []).map(this.transformCategory);\n      return categories;\n    } catch (error) {\n      console.error(\"Error fetching categories:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a category by ID\n   */\n  static async getCategoryById(id: string): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/${id}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"Category not found\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error fetching category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a category by slug\n   */\n  static async getCategoryBySlug(slug: string): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/slug/${slug}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"Category not found\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error fetching category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a new category\n   */\n  static async createCategory(\n    categoryData: CreateCategoryDto\n  ): Promise<Category> {\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(categoryData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to create category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"No category data returned\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error creating category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update a category\n   */\n  static async updateCategory(\n    id: string,\n    updateData: UpdateCategoryDto\n  ): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/${id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to update category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"No category data returned\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error updating category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a category\n   */\n  static async deleteCategory(id: string): Promise<void> {\n    try {\n      console.log(\"Deleting category with ID:\", id); // Debug log\n      if (!id || id === \"undefined\") {\n        throw new Error(\"Category ID is required for deletion\");\n      }\n\n      const response = await fetch(`${this.baseUrl}/${id}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<null> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to delete category\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting category:\", error);\n      throw error;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAGE;AADF,MAAM,eACJ,6DAAmC;AA8B9B,MAAM;IACX,OAAe,UAAU,GAAG,aAAa,eAAe,CAAC,CAAC;IAE1D;;GAEC,GACD,OAAe,kBAAkB,eAAoB,EAAY;QAC/D,QAAQ,GAAG,CAAC,0BAA0B,kBAAkB,YAAY;QACpE,MAAM,cAAc;YAClB,IAAI,gBAAgB,GAAG,IAAI,gBAAgB,EAAE;YAC7C,MAAM,gBAAgB,IAAI;YAC1B,aAAa,gBAAgB,WAAW;YACxC,MAAM,gBAAgB,IAAI;YAC1B,MAAM,gBAAgB,IAAI;YAC1B,OAAO,gBAAgB,KAAK;YAC5B,UAAU,gBAAgB,QAAQ;YAClC,cAAc,gBAAgB,YAAY;YAC1C,UAAU,gBAAgB,QAAQ;YAClC,WAAW,gBAAgB,SAAS;YACpC,WAAW,gBAAgB,SAAS;YACpC,WAAW,gBAAgB,SAAS;QACtC;QACA,QAAQ,GAAG,CAAC,yBAAyB,cAAc,YAAY;QAC/D,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,cAAc,OAAyB,EAAuB;QACzE,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,SAAS,aAAa,WAAW;gBACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;YACrD;YACA,IAAI,SAAS,UAAU;gBACrB,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YAC5C;YACA,IAAI,SAAS,QAAQ;gBACnB,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACxC;YAEA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YAChF,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,MAAM,aAAa,CAAC,OAAO,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,iBAAiB;YACjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,gBAAgB,EAAU,EAAqB;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI;YAEpD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,kBAAkB,IAAY,EAAqB;QAC9D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM;YAE3D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,YAA+B,EACZ;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,EAAU,EACV,UAA6B,EACV;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,EAAU,EAAiB;QACrD,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B,KAAK,YAAY;YAC3D,IAAI,CAAC,MAAM,OAAO,aAAa;gBAC7B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;gBACpD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA4B,MAAM,SAAS,IAAI;YAErD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/uploadthing/utils.ts"], "sourcesContent": ["import { toast } from \"sonner\";\r\n\r\nexport const deleteImageFromServer = async (\r\n  fileKey: string\r\n): Promise<boolean> => {\r\n  try {\r\n    const response = await fetch(`/api/uploadthing/delete`, {\r\n      method: \"DELETE\",\r\n      body: JSON.stringify({ fileKey }),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to delete image\");\r\n    }\r\n\r\n    return true;\r\n  } catch (error) {\r\n    console.error(\"Error deleting image:\", error);\r\n    toast.error(\"Failed to delete image from server\");\r\n\r\n    return false;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,wBAAwB,OACnC;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;YAC/B,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QAEZ,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useProducts.ts"], "sourcesContent": ["/**\r\n * React hooks for product data management\r\n * Provides easy-to-use hooks for CRUD operations on products\r\n */\r\n\r\nimport { useCallback, useEffect, useState } from 'react';\r\nimport { toast } from 'sonner';\r\n\r\nimport {\r\n  createProduct,\r\n  deleteProduct,\r\n  getProductById,\r\n  getProductForEdit,\r\n  getProducts,\r\n  updateProduct,\r\n  updateProductSafe,\r\n  type ApiResponse,\r\n  type ProductFilters,\r\n} from '@/lib/api/products';\r\n\r\n// Hook state types\r\ninterface UseProductsState {\r\n  products: any[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  meta?: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    pages: number;\r\n  };\r\n}\r\n\r\ninterface UseProductState {\r\n  product: any | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\n/**\r\n * Hook for fetching and managing multiple products\r\n */\r\nexport function useProducts(initialFilters: ProductFilters = {}) {\r\n  const [state, setState] = useState<UseProductsState>({\r\n    products: [],\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  const [filters, setFilters] = useState<ProductFilters>(initialFilters);\r\n\r\n  const fetchProducts = useCallback(async (newFilters?: ProductFilters) => {\r\n    setState(prev => ({ ...prev, loading: true, error: null }));\r\n    \r\n    try {\r\n      const filtersToUse = newFilters || filters;\r\n      const response = await getProducts(filtersToUse);\r\n      \r\n      setState({\r\n        products: response.data,\r\n        loading: false,\r\n        error: null,\r\n        meta: response.meta,\r\n      });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch products';\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error('Failed to load products');\r\n    }\r\n  }, [filters]);\r\n\r\n  const updateFilters = useCallback((newFilters: ProductFilters) => {\r\n    setFilters(newFilters);\r\n    fetchProducts(newFilters);\r\n  }, [fetchProducts]);\r\n\r\n  const refreshProducts = useCallback(() => {\r\n    fetchProducts();\r\n  }, [fetchProducts]);\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchProducts();\r\n  }, [fetchProducts]);\r\n\r\n  return {\r\n    ...state,\r\n    filters,\r\n    updateFilters,\r\n    refreshProducts,\r\n    refetch: fetchProducts,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for fetching and managing a single product\r\n */\r\nexport function useProduct(id: string | null) {\r\n  const [state, setState] = useState<UseProductState>({\r\n    product: null,\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  const fetchProduct = useCallback(async () => {\r\n    if (!id) {\r\n      setState({ product: null, loading: false, error: null });\r\n      return;\r\n    }\r\n\r\n    setState(prev => ({ ...prev, loading: true, error: null }));\r\n    \r\n    try {\r\n      const response = await getProductById(id);\r\n      setState({\r\n        product: response.data,\r\n        loading: false,\r\n        error: null,\r\n      });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product';\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error('Failed to load product');\r\n    }\r\n  }, [id]);\r\n\r\n  useEffect(() => {\r\n    fetchProduct();\r\n  }, [fetchProduct]);\r\n\r\n  return {\r\n    ...state,\r\n    refetch: fetchProduct,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for product CRUD operations\r\n */\r\nexport function useProductMutations() {\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const createProductMutation = useCallback(async (productData: any) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await createProduct(productData);\r\n      toast.success('Product created successfully');\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateProductMutation = useCallback(async (id: string, productData: any) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await updateProduct(id, productData);\r\n      toast.success('Product updated successfully');\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateProductSafeMutation = useCallback(async (id: string, productData: any) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await updateProductSafe(id, productData);\r\n      toast.success('Product updated successfully');\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const deleteProductMutation = useCallback(async (id: string) => {\r\n    setLoading(true);\r\n    try {\r\n      await deleteProduct(id);\r\n      toast.success('Product deleted successfully');\r\n      return true;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  return {\r\n    loading,\r\n    createProduct: createProductMutation,\r\n    updateProduct: updateProductMutation,\r\n    updateProductSafe: updateProductSafeMutation,\r\n    deleteProduct: deleteProductMutation,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for fetching product for editing\r\n */\r\nexport function useProductForEdit(id: string | null) {\r\n  const [state, setState] = useState<UseProductState & { editableFields?: string[] }>({\r\n    product: null,\r\n    loading: true,\r\n    error: null,\r\n    editableFields: [],\r\n  });\r\n\r\n  const fetchProductForEdit = useCallback(async () => {\r\n    if (!id) {\r\n      setState({ product: null, loading: false, error: null, editableFields: [] });\r\n      return;\r\n    }\r\n\r\n    setState(prev => ({ ...prev, loading: true, error: null }));\r\n    \r\n    try {\r\n      const response = await getProductForEdit(id);\r\n      setState({\r\n        product: response.data,\r\n        loading: false,\r\n        error: null,\r\n        editableFields: response.editableFields || [],\r\n      });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product for editing';\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error('Failed to load product for editing');\r\n    }\r\n  }, [id]);\r\n\r\n  useEffect(() => {\r\n    fetchProductForEdit();\r\n  }, [fetchProductForEdit]);\r\n\r\n  return {\r\n    ...state,\r\n    refetch: fetchProductForEdit,\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AACA;AAEA;;;;;AAkCO,SAAS,YAAY,iBAAiC,CAAC,CAAC;;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,UAAU,EAAE;QACZ,SAAS;QACT,OAAO;IACT;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvD,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kDAAE,OAAO;YACvC;0DAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK,CAAC;;YAEzD,IAAI;gBACF,MAAM,eAAe,cAAc;gBACnC,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD,EAAE;gBAEnC,SAAS;oBACP,UAAU,SAAS,IAAI;oBACvB,SAAS;oBACT,OAAO;oBACP,MAAM,SAAS,IAAI;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D;8DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO;wBACT,CAAC;;gBACD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;iDAAG;QAAC;KAAQ;IAEZ,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACjC,WAAW;YACX,cAAc;QAChB;iDAAG;QAAC;KAAc;IAElB,MAAM,kBAAkB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;oDAAE;YAClC;QACF;mDAAG;QAAC;KAAc;IAElB,gBAAgB;IAChB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;KAAc;IAElB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA,SAAS;IACX;AACF;GAtDgB;AA2DT,SAAS,WAAW,EAAiB;;IAC1C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,SAAS;QACT,SAAS;QACT,OAAO;IACT;IAEA,MAAM,eAAe,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;gDAAE;YAC/B,IAAI,CAAC,IAAI;gBACP,SAAS;oBAAE,SAAS;oBAAM,SAAS;oBAAO,OAAO;gBAAK;gBACtD;YACF;YAEA;wDAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK,CAAC;;YAEzD,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,EAAE;gBACtC,SAAS;oBACP,SAAS,SAAS,IAAI;oBACtB,SAAS;oBACT,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D;4DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO;wBACT,CAAC;;gBACD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;+CAAG;QAAC;KAAG;IAEP,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG;QAAC;KAAa;IAEjB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;IAzCgB;AA8CT,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,wBAAwB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kEAAE,OAAO;YAC/C,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE;gBACrC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;iEAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kEAAE,OAAO,IAAY;YAC3D,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;gBACzC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;iEAAG,EAAE;IAEL,MAAM,4BAA4B,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;sEAAE,OAAO,IAAY;YAC/D,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI;gBAC7C,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;qEAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kEAAE,OAAO;YAC/C,WAAW;YACX,IAAI;gBACF,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE;gBACpB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;iEAAG,EAAE;IAEL,OAAO;QACL;QACA,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,eAAe;IACjB;AACF;IAtEgB;AA2ET,SAAS,kBAAkB,EAAiB;;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAmD;QAClF,SAAS;QACT,SAAS;QACT,OAAO;QACP,gBAAgB,EAAE;IACpB;IAEA,MAAM,sBAAsB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;8DAAE;YACtC,IAAI,CAAC,IAAI;gBACP,SAAS;oBAAE,SAAS;oBAAM,SAAS;oBAAO,OAAO;oBAAM,gBAAgB,EAAE;gBAAC;gBAC1E;YACF;YAEA;sEAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK,CAAC;;YAEzD,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE;gBACzC,SAAS;oBACP,SAAS,SAAS,IAAI;oBACtB,SAAS;oBACT,OAAO;oBACP,gBAAgB,SAAS,cAAc,IAAI,EAAE;gBAC/C;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D;0EAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO;wBACT,CAAC;;gBACD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;6DAAG;QAAC;KAAG;IAEP,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAoB;IAExB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;IA3CgB", "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useProductFormWithSections.ts"], "sourcesContent": ["import { zodResolver } from \"@hookform/resolvers/zod\";\r\n\r\nimport { useState } from \"react\";\r\n\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Resolver, SubmitHandler, useForm } from \"react-hook-form\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { useProductMutations } from \"@/hooks/useProducts\";\r\nimport { ProductFormData, productSchema } from \"@/schemas/productSchema\";\r\nimport { SectionId } from \"@/types/form-section\";\r\n\r\n/**\r\n * Custom hook for managing a multi-section product form\r\n * Handles form state, validation, and section navigation\r\n */\r\nexport function useProductFormWithSections() {\r\n  const router = useRouter();\r\n  const { createProduct, loading: creating } = useProductMutations();\r\n\r\n  // Section navigation state\r\n  const [currentSection, setCurrentSection] = useState<SectionId>(\"basic-info\");\r\n  const [completedSections, setCompletedSections] = useState<SectionId[]>([]);\r\n\r\n  // Form data state\r\n  const [tags, setTags] = useState<string[]>([]);\r\n  const [images, setImages] = useState<{ key: string; url: string }[]>([]);\r\n  const [imageAltTexts, setImageAltTexts] = useState<string[]>([]);\r\n\r\n  // Initialize react-hook-form\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    setValue,\r\n    watch,\r\n    getValues,\r\n    formState: { errors, isValid },\r\n    trigger,\r\n  } = useForm<ProductFormData>({\r\n    resolver: zodResolver(productSchema) as Resolver<ProductFormData>,\r\n    defaultValues: {\r\n      currency: \"EUR\",\r\n      condition: \"new\",\r\n      stock: 1,\r\n      isPublished: true,\r\n      status: \"draft\",\r\n      ageRestriction: \"none\",\r\n      freeShipping: false,\r\n      productType: \"physical\",\r\n      visibility: \"public\",\r\n      taxStatus: \"taxable\",\r\n      stockManagement: \"track\",\r\n      trackQuantity: true,\r\n      backorderAllowed: false,\r\n      soldIndividually: false,\r\n      requiresShipping: true,\r\n      separateShipping: false,\r\n      shippingClass: \"standard\",\r\n      shippingTime: \"3-5-business-days\",\r\n      featured: false,\r\n      sticky: false,\r\n      downloadable: false,\r\n      virtual: false,\r\n      costPrice: 0,\r\n    },\r\n    mode: \"onChange\",\r\n  });\r\n\r\n  // Define the order of form sections\r\n  const SECTIONS_ORDER: SectionId[] = [\r\n    \"basic-info\",\r\n    \"pricing\",\r\n    \"inventory\",\r\n    \"details\",\r\n    \"media\",\r\n    \"shipping\",\r\n    \"availability\",\r\n    \"seo\",\r\n    \"warranty\",\r\n    \"advanced\",\r\n  ];\r\n\r\n  // Calculate progress percentage\r\n  const progress = Math.round(\r\n    (completedSections.length / SECTIONS_ORDER.length) * 100\r\n  );\r\n\r\n  /**\r\n   * Validate the current section and navigate to the next if valid\r\n   */\r\n  const validateAndNavigate = async (nextSection: SectionId) => {\r\n    console.log(\"🔍 validateAndNavigate called\");\r\n    console.log(\"🔍 Current section:\", currentSection);\r\n    console.log(\"🔍 Next section:\", nextSection);\r\n\r\n    let fieldsToValidate: (keyof ProductFormData)[] = [];\r\n\r\n    // Determine which fields to validate based on current section\r\n    switch (currentSection) {\r\n      case \"basic-info\":\r\n        fieldsToValidate = [\"name\", \"brand\", \"description\", \"productType\"];\r\n        break;\r\n      case \"pricing\":\r\n        fieldsToValidate = [\"price\", \"currency\", \"taxStatus\", \"costPrice\"];\r\n        break;\r\n      case \"inventory\":\r\n        fieldsToValidate = [\"stock\", \"condition\", \"stockManagement\"];\r\n        break;\r\n      case \"details\":\r\n        fieldsToValidate = [\"category\", \"material\", \"tags\", \"color\"];\r\n        break;\r\n      case \"media\":\r\n        fieldsToValidate = [\"mainImage\"];\r\n        // Additional validation for alt texts will be handled separately\r\n        break;\r\n      case \"shipping\":\r\n        // Only validate shipping fields if shipping is required\r\n        const requiresShipping = getValues(\"requiresShipping\");\r\n        if (requiresShipping) {\r\n          const freeShipping = getValues(\"freeShipping\");\r\n          // Always require shipping class and shipping time\r\n          // Only require shipping cost if not free shipping\r\n          fieldsToValidate = freeShipping\r\n            ? [\"shippingClass\", \"shippingTime\"]\r\n            : [\"shippingClass\", \"shippingCost\", \"shippingTime\"];\r\n        } else {\r\n          fieldsToValidate = [];\r\n        }\r\n        break;\r\n      case \"availability\":\r\n        fieldsToValidate = [\"status\"];\r\n        break;\r\n      // SEO, warranty, and advanced are optional sections\r\n      case \"seo\":\r\n      case \"warranty\":\r\n      case \"advanced\":\r\n        fieldsToValidate = []; // No required fields for optional sections\r\n        break;\r\n    }\r\n\r\n    console.log(\"🔍 Fields to validate:\", fieldsToValidate);\r\n\r\n    // Trigger validation for the specified fields\r\n    let isValid = await trigger(fieldsToValidate as any);\r\n\r\n    // Additional validation for media section (alt texts)\r\n    if (currentSection === \"media\" && isValid) {\r\n      if (images.length > 0) {\r\n        const missingAltTexts = images.some(\r\n          (_, index) =>\r\n            !imageAltTexts[index] || imageAltTexts[index].trim() === \"\"\r\n        );\r\n        if (missingAltTexts) {\r\n          isValid = false;\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log(\"🔍 Validation result:\", isValid);\r\n\r\n    if (isValid) {\r\n      console.log(\"🔍 Validation passed, navigating to next section\");\r\n      // Add current section to completed sections if not already there\r\n      if (!completedSections.includes(currentSection)) {\r\n        console.log(\"🔍 Adding current section to completed:\", currentSection);\r\n        setCompletedSections([...completedSections, currentSection]);\r\n      }\r\n\r\n      // Navigate to next section\r\n      console.log(\"🔍 Setting current section to:\", nextSection);\r\n      setCurrentSection(nextSection);\r\n    } else {\r\n      console.log(\"🔍 Validation failed, showing error message\");\r\n      // Show specific error messages for each section\r\n      switch (currentSection) {\r\n        case \"basic-info\":\r\n          toast.error(\r\n            \"Please complete all required basic information: Product Name, Brand, Description, and Product Type\"\r\n          );\r\n          break;\r\n        case \"pricing\":\r\n          toast.error(\r\n            \"Please complete all required pricing fields: Regular Price, Currency, Tax Status, and Cost Price for business intelligence\"\r\n          );\r\n          break;\r\n        case \"inventory\":\r\n          toast.error(\r\n            \"Please complete all required inventory fields: Stock Quantity, Condition, and Stock Management\"\r\n          );\r\n          break;\r\n        case \"details\":\r\n          toast.error(\r\n            \"Please complete all required details: Category, Material, Product Tags, and Color are required\"\r\n          );\r\n          break;\r\n        case \"media\":\r\n          if (images.length === 0) {\r\n            toast.error(\"Please upload at least one product image\");\r\n          } else {\r\n            const missingAltTexts = images.some(\r\n              (_, index) =>\r\n                !imageAltTexts[index] || imageAltTexts[index].trim() === \"\"\r\n            );\r\n            if (missingAltTexts) {\r\n              toast.error(\r\n                \"Please add alt text for all images. Alt text is required for accessibility and SEO.\"\r\n              );\r\n            } else {\r\n              toast.error(\"Please complete all required media fields\");\r\n            }\r\n          }\r\n          break;\r\n        case \"shipping\":\r\n          const requiresShippingForError = getValues(\"requiresShipping\");\r\n          if (requiresShippingForError) {\r\n            const freeShippingForError = getValues(\"freeShipping\");\r\n            if (freeShippingForError) {\r\n              toast.error(\r\n                \"Please complete all required shipping information: Shipping Class and Estimated Shipping Time are required.\"\r\n              );\r\n            } else {\r\n              toast.error(\r\n                \"Please complete all required shipping information: Shipping Class, Shipping Cost, and Estimated Shipping Time are required.\"\r\n              );\r\n            }\r\n          }\r\n          break;\r\n        case \"availability\":\r\n          toast.error(\r\n            \"Please complete all required availability: Product Status is required\"\r\n          );\r\n          break;\r\n        default:\r\n          toast.error(\"Please fill in all required fields correctly\");\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle next button click\r\n   */\r\n  const handleNext = async () => {\r\n    console.log(\"🔍 handleNext called\");\r\n    console.log(\"🔍 Current section:\", currentSection);\r\n    const currentIndex = SECTIONS_ORDER.indexOf(currentSection);\r\n    console.log(\"🔍 Current index:\", currentIndex);\r\n    console.log(\"🔍 Total sections:\", SECTIONS_ORDER.length);\r\n    console.log(\r\n      \"🔍 Is last section check:\",\r\n      currentIndex >= SECTIONS_ORDER.length - 1\r\n    );\r\n\r\n    if (currentIndex < SECTIONS_ORDER.length - 1) {\r\n      const nextSection = SECTIONS_ORDER[currentIndex + 1];\r\n      console.log(\"🔍 Next section:\", nextSection);\r\n      console.log(\"🔍 Calling validateAndNavigate...\");\r\n      validateAndNavigate(nextSection);\r\n    } else {\r\n      console.log(\"🔍 Already on last section, cannot navigate further\");\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle previous button click\r\n   */\r\n  const handlePrevious = () => {\r\n    const currentIndex = SECTIONS_ORDER.indexOf(currentSection);\r\n    if (currentIndex > 0) {\r\n      setCurrentSection(SECTIONS_ORDER[currentIndex - 1]);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle section selection from navigator\r\n   */\r\n  const handleSectionChange = (sectionId: SectionId) => {\r\n    // Only allow navigation to completed sections or the next section\r\n    if (\r\n      completedSections.includes(sectionId) ||\r\n      sectionId === currentSection ||\r\n      SECTIONS_ORDER.indexOf(sectionId) === completedSections.length\r\n    ) {\r\n      setCurrentSection(sectionId);\r\n    } else {\r\n      toast.info(\"Please complete the current section first\");\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Validate all sections before submission\r\n   */\r\n  const validateAllSections = async (): Promise<boolean> => {\r\n    console.log(\"🔍 Validating all sections...\");\r\n\r\n    // Trigger validation for all fields\r\n    const isFormValid = await trigger();\r\n\r\n    if (!isFormValid) {\r\n      console.error(\"❌ Form validation failed\");\r\n      const errorFields = Object.keys(errors);\r\n      console.error(\"Fields with errors:\", errorFields);\r\n      toast.error(`Please fix errors in: ${errorFields.join(\", \")}`);\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  /**\r\n   * Handle form submission\r\n   */\r\n  const onSubmit: SubmitHandler<ProductFormData> = async (data) => {\r\n    console.log(\"🚀 Form submission started...\");\r\n    console.log(\"📍 Current section:\", currentSection);\r\n    console.log(\"✅ Completed sections:\", completedSections);\r\n    console.log(\"📊 Progress:\", progress + \"%\");\r\n\r\n    // Debug: Check what watch() returns for price\r\n    const watchedPrice = watch(\"price\");\r\n    console.log(\r\n      \"👀 Watched price value:\",\r\n      watchedPrice,\r\n      \"Type:\",\r\n      typeof watchedPrice\r\n    );\r\n\r\n    console.log(\"Form data received:\", data);\r\n    console.log(\"Tags:\", tags);\r\n    console.log(\"Images:\", images);\r\n    console.log(\"Creating state:\", creating);\r\n    console.log(\"Form errors:\", errors);\r\n    console.log(\"Form is valid:\", isValid);\r\n\r\n    try {\r\n      // FIRST: Fix missing price field issue before validation\r\n      if (!data.price) {\r\n        const watchedPrice = watch(\"price\");\r\n        console.log(\r\n          \"🔧 Fixing missing price field. Watched value:\",\r\n          watchedPrice\r\n        );\r\n\r\n        if (watchedPrice !== undefined && !isNaN(watchedPrice)) {\r\n          console.log(\"✅ Applied price fallback:\", watchedPrice);\r\n          data.price = watchedPrice;\r\n        } else {\r\n          console.error(\r\n            \"❌ Cannot fix price field - watched value is invalid:\",\r\n            watchedPrice\r\n          );\r\n        }\r\n      }\r\n\r\n      // Validate all sections before submission\r\n      const isAllValid = await validateAllSections();\r\n      if (!isAllValid) {\r\n        return null;\r\n      }\r\n\r\n      // Validate that we have minimum required data\r\n      if (!data.name || !data.price) {\r\n        toast.error(\"Product name and price are required\");\r\n        console.error(\"❌ Missing required fields:\", {\r\n          name: data.name,\r\n          price: data.price,\r\n        });\r\n        return null;\r\n      }\r\n\r\n      // Validate required fields based on current form state\r\n      if (!data.brand) {\r\n        toast.error(\"Brand is required\");\r\n        return null;\r\n      }\r\n\r\n      if (!data.description || data.description.length < 10) {\r\n        toast.error(\"Description must be at least 10 characters long\");\r\n        return null;\r\n      }\r\n\r\n      if (!data.category) {\r\n        toast.error(\"Category is required\");\r\n        return null;\r\n      }\r\n\r\n      if (!data.material) {\r\n        toast.error(\"Material is required\");\r\n        return null;\r\n      }\r\n\r\n      if (!data.tags || data.tags.length === 0) {\r\n        toast.error(\"At least one product tag is required\");\r\n        return null;\r\n      }\r\n\r\n      if (!data.color) {\r\n        toast.error(\"Color is required\");\r\n        return null;\r\n      }\r\n\r\n      if (!data.mainImage && images.length === 0) {\r\n        toast.error(\"At least one product image is required\");\r\n        return null;\r\n      }\r\n\r\n      // Validate that all images have alt text\r\n      if (images.length > 0) {\r\n        const missingAltTexts = images.some(\r\n          (_, index) =>\r\n            !imageAltTexts[index] || imageAltTexts[index].trim() === \"\"\r\n        );\r\n        if (missingAltTexts) {\r\n          toast.error(\r\n            \"All images must have alt text for accessibility and SEO\"\r\n          );\r\n          return null;\r\n        }\r\n      }\r\n\r\n      // Clean up form data - convert empty strings to undefined for optional fields\r\n      const cleanedData = { ...data };\r\n\r\n      // Debug: Log the original data to see what we're working with\r\n      console.log(\"🔍 Original form data:\", data);\r\n      console.log(\"🔍 All form data keys:\", Object.keys(data));\r\n      console.log(\"🔍 Price field exists in data:\", \"price\" in data);\r\n      console.log(\r\n        \"🔍 Price field type:\",\r\n        typeof data.price,\r\n        \"Value:\",\r\n        data.price\r\n      );\r\n      console.log(\"🔍 Name field:\", data.name);\r\n\r\n      // Check if price is undefined, null, or NaN\r\n      if (data.price === undefined) {\r\n        console.error(\"❌ Price is undefined in form data!\");\r\n\r\n        // Try to get price from watch() as fallback\r\n        const watchedPrice = watch(\"price\");\r\n        console.log(\"🔄 Trying fallback - watched price:\", watchedPrice);\r\n\r\n        if (watchedPrice !== undefined && !isNaN(watchedPrice)) {\r\n          console.log(\"✅ Using watched price as fallback\");\r\n          cleanedData.price = watchedPrice;\r\n        } else {\r\n          console.error(\"❌ Watched price is also invalid:\", watchedPrice);\r\n        }\r\n      } else if (data.price === null) {\r\n        console.error(\"❌ Price is null!\");\r\n      } else if (isNaN(data.price)) {\r\n        console.error(\"❌ Price is NaN!\");\r\n      } else {\r\n        console.log(\"✅ Price is valid:\", data.price);\r\n      }\r\n\r\n      // Remove empty SKU to avoid duplicate key issues\r\n      if (\r\n        cleanedData.sku === \"\" ||\r\n        cleanedData.sku === null ||\r\n        cleanedData.sku === undefined\r\n      ) {\r\n        delete cleanedData.sku;\r\n      }\r\n\r\n      // Handle required number fields (convert strings to numbers, but don't delete if invalid)\r\n      const requiredNumberFields = [\"price\", \"stock\"];\r\n      requiredNumberFields.forEach((field) => {\r\n        if (\r\n          cleanedData[field] !== undefined &&\r\n          typeof cleanedData[field] === \"string\"\r\n        ) {\r\n          const numValue = parseFloat(cleanedData[field]);\r\n          if (!isNaN(numValue)) {\r\n            cleanedData[field] = numValue;\r\n          }\r\n          // Don't delete required fields even if conversion fails\r\n        }\r\n      });\r\n\r\n      // Convert empty strings to undefined for optional number fields\r\n      const optionalNumberFields = [\r\n        \"lowStockThreshold\",\r\n        \"yearMade\",\r\n        \"shippingCost\",\r\n        \"costPrice\",\r\n        \"originalPrice\",\r\n        \"minimumOrderQuantity\",\r\n        \"maximumOrderQuantity\",\r\n        \"salePrice\",\r\n      ];\r\n      optionalNumberFields.forEach((field) => {\r\n        if (cleanedData[field] === \"\" || cleanedData[field] === null) {\r\n          delete cleanedData[field];\r\n        } else if (\r\n          cleanedData[field] !== undefined &&\r\n          typeof cleanedData[field] === \"string\"\r\n        ) {\r\n          // Convert string numbers to actual numbers\r\n          const numValue = parseFloat(cleanedData[field]);\r\n          if (!isNaN(numValue)) {\r\n            cleanedData[field] = numValue;\r\n          } else {\r\n            delete cleanedData[field];\r\n          }\r\n        }\r\n      });\r\n\r\n      // Convert empty strings to undefined for optional URL fields\r\n      const urlFields = [\"videoUrl\", \"threeDModelUrl\", \"canonicalUrl\"];\r\n      urlFields.forEach((field) => {\r\n        if (cleanedData[field] === \"\" || cleanedData[field] === null) {\r\n          delete cleanedData[field];\r\n        }\r\n      });\r\n\r\n      // Convert empty strings to undefined for optional string fields\r\n      const optionalStringFields = [\r\n        \"model\",\r\n        \"shortDescription\",\r\n        \"barcode\",\r\n        \"subcategory\",\r\n        \"color\",\r\n        \"size\",\r\n        \"location\",\r\n        \"shippingTime\",\r\n        \"taxClass\",\r\n        \"metaTitle\",\r\n        \"metaDescription\",\r\n        \"focusKeyword\",\r\n        \"slug\",\r\n      ];\r\n      optionalStringFields.forEach((field) => {\r\n        if (cleanedData[field] === \"\" || cleanedData[field] === null) {\r\n          delete cleanedData[field];\r\n        }\r\n      });\r\n\r\n      // Convert empty strings to undefined for optional date fields\r\n      const dateFields = [\"saleEndsAt\", \"availableFrom\", \"availableUntil\"];\r\n      dateFields.forEach((field) => {\r\n        if (cleanedData[field] === \"\" || cleanedData[field] === null) {\r\n          delete cleanedData[field];\r\n        }\r\n      });\r\n\r\n      // Debug: Log cleaned data before final preparation\r\n      console.log(\"🧹 Cleaned data:\", cleanedData);\r\n      console.log(\r\n        \"🧹 Cleaned price field type:\",\r\n        typeof cleanedData.price,\r\n        \"Value:\",\r\n        cleanedData.price\r\n      );\r\n\r\n      // Prepare the final data with images and tags\r\n      const mainImageUrl =\r\n        images.length > 0 ? images[0].url : data.mainImage || \"\";\r\n      const additionalImages =\r\n        images.length > 1 ? images.slice(1).map((img) => img.url) : [];\r\n\r\n      const finalProductData = {\r\n        ...cleanedData,\r\n        tags: tags || [],\r\n        images: additionalImages, // Only additional images, not including main image\r\n        mainImage: mainImageUrl,\r\n        imageAltTexts: imageAltTexts || [],\r\n      };\r\n\r\n      console.log(\"📦 Final product data to be sent:\", finalProductData);\r\n      console.log(\r\n        \"📦 Final price field type:\",\r\n        typeof finalProductData.price,\r\n        \"Value:\",\r\n        finalProductData.price\r\n      );\r\n\r\n      // Show loading toast\r\n      toast.loading(\"Creating product...\", { id: \"create-product\" });\r\n\r\n      // Create product via API\r\n      console.log(\"📡 Calling API...\");\r\n      const createdProduct = await createProduct(finalProductData);\r\n\r\n      // Dismiss loading toast\r\n      toast.dismiss(\"create-product\");\r\n\r\n      // Display the created product in the console\r\n      console.log(\"✅ PRODUCT CREATED SUCCESSFULLY!\");\r\n      console.log(\"Product Details:\", createdProduct);\r\n\r\n      // Show success message\r\n      toast.success(\"Product created successfully!\");\r\n\r\n      // Navigate to the created product's detail page\r\n      if (createdProduct && (createdProduct._id || createdProduct.id)) {\r\n        const productId = createdProduct._id || createdProduct.id;\r\n        console.log(`🔗 Navigating to product: ${productId}`);\r\n        router.push(`/admin/products/${productId}`);\r\n      } else {\r\n        // Fallback to products list\r\n        console.log(\"🔗 Navigating to products list\");\r\n        router.push(\"/admin/products/list\");\r\n      }\r\n\r\n      return createdProduct;\r\n    } catch (err) {\r\n      // Dismiss loading toast\r\n      toast.dismiss(\"create-product\");\r\n\r\n      console.error(\"❌ Failed to create product:\", err);\r\n\r\n      // Show user-friendly error messages\r\n      if (err instanceof Error) {\r\n        let errorMessage = err.message;\r\n\r\n        // Handle specific error types\r\n        if (errorMessage.includes(\"Duplicate key error\")) {\r\n          errorMessage =\r\n            \"A product with this SKU already exists. Please use a different SKU or leave it empty.\";\r\n        } else if (errorMessage.includes(\"ValidationError\")) {\r\n          errorMessage = \"Please check all required fields and try again.\";\r\n        } else if (errorMessage.includes(\"HTTP error! status: 400\")) {\r\n          // Extract the actual error message from the API response\r\n          const match = errorMessage.match(/message: (.+)/);\r\n          if (match) {\r\n            const apiError = match[1];\r\n            if (apiError.includes(\"Duplicate key error\")) {\r\n              errorMessage =\r\n                \"A product with this SKU already exists. Please use a different SKU or leave it empty.\";\r\n            } else {\r\n              errorMessage = `Error: ${apiError}`;\r\n            }\r\n          }\r\n        }\r\n\r\n        toast.error(errorMessage);\r\n        console.error(\"Error details:\", err);\r\n      } else {\r\n        toast.error(\"Failed to create product: Unknown error\");\r\n        console.error(\"Unknown error:\", err);\r\n      }\r\n\r\n      return null;\r\n    }\r\n  };\r\n\r\n  return {\r\n    // Form state\r\n    register,\r\n    handleSubmit,\r\n    setValue,\r\n    watch,\r\n    errors,\r\n    isValid,\r\n\r\n    // Section navigation\r\n    currentSection,\r\n    completedSections,\r\n    SECTIONS_ORDER,\r\n    progress,\r\n    handleNext,\r\n    handlePrevious,\r\n    handleSectionChange,\r\n\r\n    // Additional state\r\n    tags,\r\n    setTags,\r\n    images,\r\n    setImages,\r\n    imageAltTexts,\r\n    setImageAltTexts,\r\n\r\n    // Form submission\r\n    onSubmit,\r\n    creating,\r\n    validateAllSections,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;AAEA;AACA;AACA;AAEA;AACA;;;;;;;;;AAOO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,SAAS,QAAQ,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD;IAE/D,2BAA2B;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAa;IAChE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAE1E,kBAAkB;IAClB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkC,EAAE;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,6BAA6B;IAC7B,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,SAAS,EACT,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC9B,OAAO,EACR,GAAG,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,gBAAa;QACnC,eAAe;YACb,UAAU;YACV,WAAW;YACX,OAAO;YACP,aAAa;YACb,QAAQ;YACR,gBAAgB;YAChB,cAAc;YACd,aAAa;YACb,YAAY;YACZ,WAAW;YACX,iBAAiB;YACjB,eAAe;YACf,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,eAAe;YACf,cAAc;YACd,UAAU;YACV,QAAQ;YACR,cAAc;YACd,SAAS;YACT,WAAW;QACb;QACA,MAAM;IACR;IAEA,oCAAoC;IACpC,MAAM,iBAA8B;QAClC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,gCAAgC;IAChC,MAAM,WAAW,KAAK,KAAK,CACzB,AAAC,kBAAkB,MAAM,GAAG,eAAe,MAAM,GAAI;IAGvD;;GAEC,GACD,MAAM,sBAAsB,OAAO;QACjC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,uBAAuB;QACnC,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,IAAI,mBAA8C,EAAE;QAEpD,8DAA8D;QAC9D,OAAQ;YACN,KAAK;gBACH,mBAAmB;oBAAC;oBAAQ;oBAAS;oBAAe;iBAAc;gBAClE;YACF,KAAK;gBACH,mBAAmB;oBAAC;oBAAS;oBAAY;oBAAa;iBAAY;gBAClE;YACF,KAAK;gBACH,mBAAmB;oBAAC;oBAAS;oBAAa;iBAAkB;gBAC5D;YACF,KAAK;gBACH,mBAAmB;oBAAC;oBAAY;oBAAY;oBAAQ;iBAAQ;gBAC5D;YACF,KAAK;gBACH,mBAAmB;oBAAC;iBAAY;gBAEhC;YACF,KAAK;gBACH,wDAAwD;gBACxD,MAAM,mBAAmB,UAAU;gBACnC,IAAI,kBAAkB;oBACpB,MAAM,eAAe,UAAU;oBAC/B,kDAAkD;oBAClD,kDAAkD;oBAClD,mBAAmB,eACf;wBAAC;wBAAiB;qBAAe,GACjC;wBAAC;wBAAiB;wBAAgB;qBAAe;gBACvD,OAAO;oBACL,mBAAmB,EAAE;gBACvB;gBACA;YACF,KAAK;gBACH,mBAAmB;oBAAC;iBAAS;gBAC7B;YACF,oDAAoD;YACpD,KAAK;YACL,KAAK;YACL,KAAK;gBACH,mBAAmB,EAAE,EAAE,2CAA2C;gBAClE;QACJ;QAEA,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,8CAA8C;QAC9C,IAAI,UAAU,MAAM,QAAQ;QAE5B,sDAAsD;QACtD,IAAI,mBAAmB,WAAW,SAAS;YACzC,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,MAAM,kBAAkB,OAAO,IAAI,CACjC,CAAC,GAAG,QACF,CAAC,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,OAAO;gBAE7D,IAAI,iBAAiB;oBACnB,UAAU;gBACZ;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,yBAAyB;QAErC,IAAI,SAAS;YACX,QAAQ,GAAG,CAAC;YACZ,iEAAiE;YACjE,IAAI,CAAC,kBAAkB,QAAQ,CAAC,iBAAiB;gBAC/C,QAAQ,GAAG,CAAC,2CAA2C;gBACvD,qBAAqB;uBAAI;oBAAmB;iBAAe;YAC7D;YAEA,2BAA2B;YAC3B,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,kBAAkB;QACpB,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,gDAAgD;YAChD,OAAQ;gBACN,KAAK;oBACH,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT;oBAEF;gBACF,KAAK;oBACH,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT;oBAEF;gBACF,KAAK;oBACH,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT;oBAEF;gBACF,KAAK;oBACH,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT;oBAEF;gBACF,KAAK;oBACH,IAAI,OAAO,MAAM,KAAK,GAAG;wBACvB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd,OAAO;wBACL,MAAM,kBAAkB,OAAO,IAAI,CACjC,CAAC,GAAG,QACF,CAAC,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,OAAO;wBAE7D,IAAI,iBAAiB;4BACnB,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT;wBAEJ,OAAO;4BACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACd;oBACF;oBACA;gBACF,KAAK;oBACH,MAAM,2BAA2B,UAAU;oBAC3C,IAAI,0BAA0B;wBAC5B,MAAM,uBAAuB,UAAU;wBACvC,IAAI,sBAAsB;4BACxB,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT;wBAEJ,OAAO;4BACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT;wBAEJ;oBACF;oBACA;gBACF,KAAK;oBACH,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT;oBAEF;gBACF;oBACE,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ;YACJ;QACF;IACF;IAEA;;GAEC,GACD,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,uBAAuB;QACnC,MAAM,eAAe,eAAe,OAAO,CAAC;QAC5C,QAAQ,GAAG,CAAC,qBAAqB;QACjC,QAAQ,GAAG,CAAC,sBAAsB,eAAe,MAAM;QACvD,QAAQ,GAAG,CACT,6BACA,gBAAgB,eAAe,MAAM,GAAG;QAG1C,IAAI,eAAe,eAAe,MAAM,GAAG,GAAG;YAC5C,MAAM,cAAc,cAAc,CAAC,eAAe,EAAE;YACpD,QAAQ,GAAG,CAAC,oBAAoB;YAChC,QAAQ,GAAG,CAAC;YACZ,oBAAoB;QACtB,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB;QACrB,MAAM,eAAe,eAAe,OAAO,CAAC;QAC5C,IAAI,eAAe,GAAG;YACpB,kBAAkB,cAAc,CAAC,eAAe,EAAE;QACpD;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,CAAC;QAC3B,kEAAkE;QAClE,IACE,kBAAkB,QAAQ,CAAC,cAC3B,cAAc,kBACd,eAAe,OAAO,CAAC,eAAe,kBAAkB,MAAM,EAC9D;YACA,kBAAkB;QACpB,OAAO;YACL,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACb;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB;QAC1B,QAAQ,GAAG,CAAC;QAEZ,oCAAoC;QACpC,MAAM,cAAc,MAAM;QAE1B,IAAI,CAAC,aAAa;YAChB,QAAQ,KAAK,CAAC;YACd,MAAM,cAAc,OAAO,IAAI,CAAC;YAChC,QAAQ,KAAK,CAAC,uBAAuB;YACrC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,sBAAsB,EAAE,YAAY,IAAI,CAAC,OAAO;YAC7D,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,WAA2C,OAAO;QACtD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,uBAAuB;QACnC,QAAQ,GAAG,CAAC,yBAAyB;QACrC,QAAQ,GAAG,CAAC,gBAAgB,WAAW;QAEvC,8CAA8C;QAC9C,MAAM,eAAe,MAAM;QAC3B,QAAQ,GAAG,CACT,2BACA,cACA,SACA,OAAO;QAGT,QAAQ,GAAG,CAAC,uBAAuB;QACnC,QAAQ,GAAG,CAAC,SAAS;QACrB,QAAQ,GAAG,CAAC,WAAW;QACvB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,IAAI;YACF,yDAAyD;YACzD,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,MAAM,eAAe,MAAM;gBAC3B,QAAQ,GAAG,CACT,iDACA;gBAGF,IAAI,iBAAiB,aAAa,CAAC,MAAM,eAAe;oBACtD,QAAQ,GAAG,CAAC,6BAA6B;oBACzC,KAAK,KAAK,GAAG;gBACf,OAAO;oBACL,QAAQ,KAAK,CACX,wDACA;gBAEJ;YACF;YAEA,0CAA0C;YAC1C,MAAM,aAAa,MAAM;YACzB,IAAI,CAAC,YAAY;gBACf,OAAO;YACT;YAEA,8CAA8C;YAC9C,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,EAAE;gBAC7B,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,QAAQ,KAAK,CAAC,8BAA8B;oBAC1C,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;gBACnB;gBACA,OAAO;YACT;YAEA,uDAAuD;YACvD,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,IAAI,CAAC,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,IAAI;gBACrD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAClB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAClB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,GAAG;gBACxC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,IAAI,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,KAAK,GAAG;gBAC1C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,yCAAyC;YACzC,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,MAAM,kBAAkB,OAAO,IAAI,CACjC,CAAC,GAAG,QACF,CAAC,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,OAAO;gBAE7D,IAAI,iBAAiB;oBACnB,2QAAA,CAAA,QAAK,CAAC,KAAK,CACT;oBAEF,OAAO;gBACT;YACF;YAEA,8EAA8E;YAC9E,MAAM,cAAc;gBAAE,GAAG,IAAI;YAAC;YAE9B,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,0BAA0B;YACtC,QAAQ,GAAG,CAAC,0BAA0B,OAAO,IAAI,CAAC;YAClD,QAAQ,GAAG,CAAC,kCAAkC,WAAW;YACzD,QAAQ,GAAG,CACT,wBACA,OAAO,KAAK,KAAK,EACjB,UACA,KAAK,KAAK;YAEZ,QAAQ,GAAG,CAAC,kBAAkB,KAAK,IAAI;YAEvC,4CAA4C;YAC5C,IAAI,KAAK,KAAK,KAAK,WAAW;gBAC5B,QAAQ,KAAK,CAAC;gBAEd,4CAA4C;gBAC5C,MAAM,eAAe,MAAM;gBAC3B,QAAQ,GAAG,CAAC,uCAAuC;gBAEnD,IAAI,iBAAiB,aAAa,CAAC,MAAM,eAAe;oBACtD,QAAQ,GAAG,CAAC;oBACZ,YAAY,KAAK,GAAG;gBACtB,OAAO;oBACL,QAAQ,KAAK,CAAC,oCAAoC;gBACpD;YACF,OAAO,IAAI,KAAK,KAAK,KAAK,MAAM;gBAC9B,QAAQ,KAAK,CAAC;YAChB,OAAO,IAAI,MAAM,KAAK,KAAK,GAAG;gBAC5B,QAAQ,KAAK,CAAC;YAChB,OAAO;gBACL,QAAQ,GAAG,CAAC,qBAAqB,KAAK,KAAK;YAC7C;YAEA,iDAAiD;YACjD,IACE,YAAY,GAAG,KAAK,MACpB,YAAY,GAAG,KAAK,QACpB,YAAY,GAAG,KAAK,WACpB;gBACA,OAAO,YAAY,GAAG;YACxB;YAEA,0FAA0F;YAC1F,MAAM,uBAAuB;gBAAC;gBAAS;aAAQ;YAC/C,qBAAqB,OAAO,CAAC,CAAC;gBAC5B,IACE,WAAW,CAAC,MAAM,KAAK,aACvB,OAAO,WAAW,CAAC,MAAM,KAAK,UAC9B;oBACA,MAAM,WAAW,WAAW,WAAW,CAAC,MAAM;oBAC9C,IAAI,CAAC,MAAM,WAAW;wBACpB,WAAW,CAAC,MAAM,GAAG;oBACvB;gBACA,wDAAwD;gBAC1D;YACF;YAEA,gEAAgE;YAChE,MAAM,uBAAuB;gBAC3B;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,qBAAqB,OAAO,CAAC,CAAC;gBAC5B,IAAI,WAAW,CAAC,MAAM,KAAK,MAAM,WAAW,CAAC,MAAM,KAAK,MAAM;oBAC5D,OAAO,WAAW,CAAC,MAAM;gBAC3B,OAAO,IACL,WAAW,CAAC,MAAM,KAAK,aACvB,OAAO,WAAW,CAAC,MAAM,KAAK,UAC9B;oBACA,2CAA2C;oBAC3C,MAAM,WAAW,WAAW,WAAW,CAAC,MAAM;oBAC9C,IAAI,CAAC,MAAM,WAAW;wBACpB,WAAW,CAAC,MAAM,GAAG;oBACvB,OAAO;wBACL,OAAO,WAAW,CAAC,MAAM;oBAC3B;gBACF;YACF;YAEA,6DAA6D;YAC7D,MAAM,YAAY;gBAAC;gBAAY;gBAAkB;aAAe;YAChE,UAAU,OAAO,CAAC,CAAC;gBACjB,IAAI,WAAW,CAAC,MAAM,KAAK,MAAM,WAAW,CAAC,MAAM,KAAK,MAAM;oBAC5D,OAAO,WAAW,CAAC,MAAM;gBAC3B;YACF;YAEA,gEAAgE;YAChE,MAAM,uBAAuB;gBAC3B;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,qBAAqB,OAAO,CAAC,CAAC;gBAC5B,IAAI,WAAW,CAAC,MAAM,KAAK,MAAM,WAAW,CAAC,MAAM,KAAK,MAAM;oBAC5D,OAAO,WAAW,CAAC,MAAM;gBAC3B;YACF;YAEA,8DAA8D;YAC9D,MAAM,aAAa;gBAAC;gBAAc;gBAAiB;aAAiB;YACpE,WAAW,OAAO,CAAC,CAAC;gBAClB,IAAI,WAAW,CAAC,MAAM,KAAK,MAAM,WAAW,CAAC,MAAM,KAAK,MAAM;oBAC5D,OAAO,WAAW,CAAC,MAAM;gBAC3B;YACF;YAEA,mDAAmD;YACnD,QAAQ,GAAG,CAAC,oBAAoB;YAChC,QAAQ,GAAG,CACT,gCACA,OAAO,YAAY,KAAK,EACxB,UACA,YAAY,KAAK;YAGnB,8CAA8C;YAC9C,MAAM,eACJ,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,SAAS,IAAI;YACxD,MAAM,mBACJ,OAAO,MAAM,GAAG,IAAI,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,MAAQ,IAAI,GAAG,IAAI,EAAE;YAEhE,MAAM,mBAAmB;gBACvB,GAAG,WAAW;gBACd,MAAM,QAAQ,EAAE;gBAChB,QAAQ;gBACR,WAAW;gBACX,eAAe,iBAAiB,EAAE;YACpC;YAEA,QAAQ,GAAG,CAAC,qCAAqC;YACjD,QAAQ,GAAG,CACT,8BACA,OAAO,iBAAiB,KAAK,EAC7B,UACA,iBAAiB,KAAK;YAGxB,qBAAqB;YACrB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;gBAAE,IAAI;YAAiB;YAE5D,yBAAyB;YACzB,QAAQ,GAAG,CAAC;YACZ,MAAM,iBAAiB,MAAM,cAAc;YAE3C,wBAAwB;YACxB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,6CAA6C;YAC7C,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,uBAAuB;YACvB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,gDAAgD;YAChD,IAAI,kBAAkB,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,GAAG;gBAC/D,MAAM,YAAY,eAAe,GAAG,IAAI,eAAe,EAAE;gBACzD,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,WAAW;gBACpD,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,WAAW;YAC5C,OAAO;gBACL,4BAA4B;gBAC5B,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd;YAEA,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,wBAAwB;YACxB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,oCAAoC;YACpC,IAAI,eAAe,OAAO;gBACxB,IAAI,eAAe,IAAI,OAAO;gBAE9B,8BAA8B;gBAC9B,IAAI,aAAa,QAAQ,CAAC,wBAAwB;oBAChD,eACE;gBACJ,OAAO,IAAI,aAAa,QAAQ,CAAC,oBAAoB;oBACnD,eAAe;gBACjB,OAAO,IAAI,aAAa,QAAQ,CAAC,4BAA4B;oBAC3D,yDAAyD;oBACzD,MAAM,QAAQ,aAAa,KAAK,CAAC;oBACjC,IAAI,OAAO;wBACT,MAAM,WAAW,KAAK,CAAC,EAAE;wBACzB,IAAI,SAAS,QAAQ,CAAC,wBAAwB;4BAC5C,eACE;wBACJ,OAAO;4BACL,eAAe,CAAC,OAAO,EAAE,UAAU;wBACrC;oBACF;gBACF;gBAEA,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,QAAQ,KAAK,CAAC,kBAAkB;YAClC,OAAO;gBACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,QAAQ,KAAK,CAAC,kBAAkB;YAClC;YAEA,OAAO;QACT;IACF;IAEA,OAAO;QACL,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QAEA,qBAAqB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;QACA;QACA;QACA;QAEA,kBAAkB;QAClB;QACA;QACA;IACF;AACF;GAvpBgB;;QACC,oQAAA,CAAA,YAAS;QACqB,uHAAA,CAAA,sBAAmB;QAoB5D,0PAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useCategories.ts"], "sourcesContent": ["/**\n * React hooks for category data management\n * Provides easy-to-use hooks for CRUD operations on categories\n */\n\nimport { useCallback, useEffect, useState } from 'react';\nimport { toast } from 'sonner';\n\nimport { Category } from '@/components/pages/management/CategoryManager';\nimport {\n  CategoryApiService,\n  type CategoryFilters,\n  type CreateCategoryDto,\n  type UpdateCategoryDto,\n} from '@/lib/api/categoryApi';\n\n// Hook state types\ninterface UseCategoriesState {\n  categories: Category[];\n  loading: boolean;\n  error: string | null;\n}\n\ninterface UseCategoryState {\n  category: Category | null;\n  loading: boolean;\n  error: string | null;\n}\n\n/**\n * Hook for fetching and managing categories list\n */\nexport function useCategories(initialFilters: CategoryFilters = {}) {\n  const [state, setState] = useState<UseCategoriesState>({\n    categories: [],\n    loading: true,\n    error: null,\n  });\n\n  const [filters, setFilters] = useState<CategoryFilters>(initialFilters);\n\n  const fetchCategories = useCallback(async (newFilters?: CategoryFilters) => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n    \n    try {\n      const filtersToUse = newFilters || filters;\n      const categories = await CategoryApiService.getCategories(filtersToUse);\n      \n      setState({\n        categories,\n        loading: false,\n        error: null,\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch categories';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      toast.error('Failed to load categories');\n    }\n  }, [filters]);\n\n  // Initial fetch\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  const updateFilters = useCallback((newFilters: CategoryFilters) => {\n    setFilters(newFilters);\n    fetchCategories(newFilters);\n  }, [fetchCategories]);\n\n  return {\n    ...state,\n    filters,\n    updateFilters,\n    refetch: fetchCategories,\n  };\n}\n\n/**\n * Hook for fetching and managing a single category\n */\nexport function useCategory(id: string | null) {\n  const [state, setState] = useState<UseCategoryState>({\n    category: null,\n    loading: true,\n    error: null,\n  });\n\n  const fetchCategory = useCallback(async () => {\n    if (!id) {\n      setState({ category: null, loading: false, error: null });\n      return;\n    }\n\n    setState(prev => ({ ...prev, loading: true, error: null }));\n    \n    try {\n      const category = await CategoryApiService.getCategoryById(id);\n      setState({\n        category,\n        loading: false,\n        error: null,\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch category';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      toast.error('Failed to load category');\n    }\n  }, [id]);\n\n  useEffect(() => {\n    fetchCategory();\n  }, [fetchCategory]);\n\n  return {\n    ...state,\n    refetch: fetchCategory,\n  };\n}\n\n/**\n * Hook for category CRUD operations\n */\nexport function useCategoryMutations() {\n  const [loading, setLoading] = useState(false);\n\n  const createCategory = useCallback(async (categoryData: CreateCategoryDto) => {\n    setLoading(true);\n    try {\n      const category = await CategoryApiService.createCategory(categoryData);\n      toast.success('Category created successfully');\n      return category;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const updateCategory = useCallback(async (id: string, updateData: UpdateCategoryDto) => {\n    setLoading(true);\n    try {\n      const category = await CategoryApiService.updateCategory(id, updateData);\n      toast.success('Category updated successfully');\n      return category;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const deleteCategory = useCallback(async (id: string) => {\n    setLoading(true);\n    try {\n      await CategoryApiService.deleteCategory(id);\n      toast.success('Category deleted successfully');\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    createCategory,\n    updateCategory,\n    deleteCategory,\n    loading,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AAGA;;;;;AAuBO,SAAS,cAAc,iBAAkC,CAAC,CAAC;;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,YAAY,EAAE;QACd,SAAS;QACT,OAAO;IACT;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAmB;IAExD,MAAM,kBAAkB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;sDAAE,OAAO;YACzC;8DAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK,CAAC;;YAEzD,IAAI;gBACF,MAAM,eAAe,cAAc;gBACnC,MAAM,aAAa,MAAM,4HAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;gBAE1D,SAAS;oBACP;oBACA,SAAS;oBACT,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D;kEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO;wBACT,CAAC;;gBACD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;qDAAG;QAAC;KAAQ;IAEZ,gBAAgB;IAChB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAgB;IAEpB,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACjC,WAAW;YACX,gBAAgB;QAClB;mDAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA,SAAS;IACX;AACF;GAhDgB;AAqDT,SAAS,YAAY,EAAiB;;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,UAAU;QACV,SAAS;QACT,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kDAAE;YAChC,IAAI,CAAC,IAAI;gBACP,SAAS;oBAAE,UAAU;oBAAM,SAAS;oBAAO,OAAO;gBAAK;gBACvD;YACF;YAEA;0DAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK,CAAC;;YAEzD,IAAI;gBACF,MAAM,WAAW,MAAM,4HAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;gBAC1D,SAAS;oBACP;oBACA,SAAS;oBACT,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D;8DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO;wBACT,CAAC;;gBACD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;iDAAG;QAAC;KAAG;IAEP,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;KAAc;IAElB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;IAzCgB;AA8CT,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,iBAAiB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YACxC,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,4HAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;gBACzD,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4DAAE,OAAO,IAAY;YACpD,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,4HAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,IAAI;gBAC7D,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YACxC,WAAW;YACX,IAAI;gBACF,MAAM,4HAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;gBACxC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IArDgB", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/schemas/productSchema.ts"], "sourcesContent": ["// schemas/productSchema.ts\r\nimport { z } from \"zod\";\r\n\r\n// Enhanced constants for enum values\r\nexport const conditionValues = [\r\n  \"new\",\r\n  \"like-new\",\r\n  \"excellent\",\r\n  \"good\",\r\n  \"fair\",\r\n  \"used\",\r\n  \"refurbished\",\r\n  \"vintage\",\r\n  \"antique\",\r\n  \"damaged\",\r\n] as const;\r\n\r\nexport const productStatusValues = [\r\n  \"in-stock\",\r\n  \"out-of-stock\",\r\n  \"coming-soon\",\r\n  \"archived\",\r\n  \"draft\",\r\n  \"suspended\",\r\n] as const;\r\n\r\nexport const productTypeValues = [\r\n  \"physical\",\r\n  \"digital\",\r\n  \"service\",\r\n  \"subscription\",\r\n  \"bundle\",\r\n] as const;\r\n\r\nexport const visibilityValues = [\r\n  \"public\",\r\n  \"private\",\r\n  \"hidden\",\r\n  \"password-protected\",\r\n] as const;\r\n\r\nexport const taxStatusValues = [\r\n  \"taxable\",\r\n  \"tax-exempt\",\r\n  \"shipping-only\",\r\n] as const;\r\n\r\nexport const stockManagementValues = [\r\n  \"track\",\r\n  \"no-track\",\r\n  \"backorder\",\r\n] as const;\r\n\r\nexport const shippingClassValues = [\r\n  \"standard\",\r\n  \"express\",\r\n  \"overnight\",\r\n  \"international\",\r\n  \"heavy\",\r\n  \"fragile\",\r\n  \"digital-only\",\r\n] as const;\r\n\r\nexport const shippingTimeValues = [\r\n  \"same-day\",\r\n  \"1-2-business-days\",\r\n  \"2-3-business-days\",\r\n  \"3-5-business-days\",\r\n  \"5-7-business-days\",\r\n  \"7-10-business-days\",\r\n  \"10-14-business-days\",\r\n  \"2-3-weeks\",\r\n  \"3-4-weeks\",\r\n  \"4-6-weeks\",\r\n] as const;\r\n\r\nexport const currencyValues = [\"EUR\", \"USD\"] as const;\r\nexport const weightUnitValues = [\"g\", \"kg\"] as const;\r\nexport const dimensionUnitValues = [\"mm\", \"cm\"] as const;\r\nexport const ageRestrictionValues = [\"none\", \"18+\", \"21+\"] as const;\r\n\r\n// Schema definitions\r\nconst dimensionsSchema = z.object({\r\n  width: z.number().positive(\"Width must be positive\"),\r\n  height: z.number().positive(\"Height must be positive\"),\r\n  depth: z.number().positive(\"Depth must be positive\"),\r\n  unit: z.enum(dimensionUnitValues),\r\n});\r\n\r\nconst weightSchema = z.object({\r\n  value: z.number().positive(\"Weight must be positive\"),\r\n  unit: z.enum(weightUnitValues),\r\n});\r\n\r\n// Enhanced address schema\r\nconst addressSchema = z\r\n  .object({\r\n    street: z.string().optional(),\r\n    city: z.string().optional(),\r\n    state: z.string().optional(),\r\n    postalCode: z.string().optional(),\r\n    country: z.string().optional(),\r\n    coordinates: z\r\n      .object({\r\n        latitude: z.number(),\r\n        longitude: z.number(),\r\n      })\r\n      .optional(),\r\n  })\r\n  .optional();\r\n\r\n// Enhanced sections of the product schema\r\nexport const basicInfoSchema = z.object({\r\n  name: z.string().min(1, \"Name is required\"),\r\n  brand: z.string().min(1, \"Brand is required\"),\r\n  model: z.string().optional(),\r\n  description: z\r\n    .string()\r\n    .min(10, \"Description should be at least 10 characters\"),\r\n  shortDescription: z.string().optional(),\r\n  productType: z.enum(productTypeValues).default(\"physical\"),\r\n  visibility: z.enum(visibilityValues).default(\"public\"),\r\n});\r\n\r\n// Base pricing schema without refinements (for use in productSchema.shape)\r\nexport const basePricingSchema = z.object({\r\n  price: z.number().positive(\"Price must be a positive number\"),\r\n  originalPrice: z.preprocess((val) => {\r\n    if (val === \"\" || val === null || val === undefined || Number.isNaN(val)) {\r\n      return undefined;\r\n    }\r\n    const numVal = Number(val);\r\n    return Number.isNaN(numVal) ? undefined : numVal;\r\n  }, z.number().positive(\"Original price must be a positive number\").optional()),\r\n  salePrice: z.preprocess((val) => {\r\n    if (val === \"\" || val === null || val === undefined || Number.isNaN(val)) {\r\n      return undefined;\r\n    }\r\n    const numVal = Number(val);\r\n    return Number.isNaN(numVal) ? undefined : numVal;\r\n  }, z.number().positive(\"Sale price must be a positive number\").optional()),\r\n  currency: z.enum(currencyValues),\r\n  isOnSale: z.boolean().default(false),\r\n  saleEndsAt: z.string().optional(),\r\n  costPrice: z\r\n    .number()\r\n    .positive(\"Cost price is required for business intelligence\"),\r\n  taxStatus: z.enum(taxStatusValues).default(\"taxable\"),\r\n  taxClass: z.string().optional(),\r\n  minimumOrderQuantity: z.preprocess((val) => {\r\n    if (val === \"\" || val === null || val === undefined || Number.isNaN(val)) {\r\n      return undefined;\r\n    }\r\n    const numVal = Number(val);\r\n    return Number.isNaN(numVal) ? undefined : numVal;\r\n  }, z.number().int().positive().optional()),\r\n  maximumOrderQuantity: z.preprocess((val) => {\r\n    if (val === \"\" || val === null || val === undefined || Number.isNaN(val)) {\r\n      return undefined;\r\n    }\r\n    const numVal = Number(val);\r\n    return Number.isNaN(numVal) ? undefined : numVal;\r\n  }, z.number().int().positive().optional()),\r\n});\r\n\r\n// Pricing schema with refinements (for standalone use)\r\nexport const pricingSchema = basePricingSchema\r\n  .refine(\r\n    (data) => {\r\n      // If on sale, sale price must be lower than regular price\r\n      if (data.isOnSale && data.salePrice) {\r\n        return data.salePrice < data.price;\r\n      }\r\n      return true;\r\n    },\r\n    {\r\n      message: \"Sale price must be lower than regular price\",\r\n      path: [\"salePrice\"],\r\n    }\r\n  )\r\n  .refine(\r\n    (data) => {\r\n      // If on sale, original price should equal regular price (for backwards compatibility)\r\n      if (data.isOnSale && data.originalPrice) {\r\n        return data.originalPrice >= data.price;\r\n      }\r\n      return true;\r\n    },\r\n    {\r\n      message: \"Original price should be at least the regular price\",\r\n      path: [\"originalPrice\"],\r\n    }\r\n  );\r\n\r\nexport const inventorySchema = z.object({\r\n  stock: z\r\n    .number()\r\n    .int()\r\n    .nonnegative(\"Stock must be zero or a positive number\"),\r\n  condition: z.enum(conditionValues),\r\n  isPublished: z.boolean().default(true),\r\n  stockManagement: z.enum(stockManagementValues).default(\"track\"),\r\n  lowStockThreshold: z.preprocess((val) => {\r\n    if (val === \"\" || val === null || val === undefined || Number.isNaN(val)) {\r\n      return undefined;\r\n    }\r\n    const numVal = Number(val);\r\n    return Number.isNaN(numVal) ? undefined : numVal;\r\n  }, z.number().int().nonnegative().optional()),\r\n  backorderAllowed: z.boolean().default(false),\r\n  trackQuantity: z.boolean().default(true),\r\n  soldIndividually: z.boolean().default(false),\r\n});\r\n\r\nexport const detailsSchema = z.object({\r\n  category: z.string().min(1, \"Category is required\"),\r\n  subcategory: z.string().optional(),\r\n  material: z.string().min(1, \"Material is required\"),\r\n  dimensions: dimensionsSchema.optional(),\r\n  weight: weightSchema.optional(),\r\n  yearMade: z.preprocess((val) => {\r\n    if (val === \"\" || val === null || val === undefined || Number.isNaN(val)) {\r\n      return undefined;\r\n    }\r\n    const numVal = Number(val);\r\n    return Number.isNaN(numVal) ? undefined : numVal;\r\n  }, z.number().int(\"Year must be a whole number\").min(1800).max(new Date().getFullYear()).optional()),\r\n  tags: z.array(z.string()).min(1, \"At least one tag is required\"),\r\n  color: z.string().min(1, \"Color is required\"),\r\n  size: z.string().optional(),\r\n});\r\n\r\nexport const imagesSchema = z.object({\r\n  mainImage: z.string().url(\"Main image must be a valid URL\"),\r\n  images: z.array(z.string().url(\"Image must be a valid URL\")).optional(),\r\n  imageAltTexts: z.array(z.string()).optional(),\r\n  videoUrl: z.preprocess(\r\n    (val) =>\r\n      val === \"\" || val === null || val === undefined ? undefined : val,\r\n    z.string().url().optional()\r\n  ),\r\n  threeDModelUrl: z.preprocess(\r\n    (val) =>\r\n      val === \"\" || val === null || val === undefined ? undefined : val,\r\n    z.string().url().optional()\r\n  ),\r\n});\r\n\r\nexport const shippingSchema = z.object({\r\n  location: z.string().optional(),\r\n  address: addressSchema,\r\n  shippingCost: z.preprocess((val) => {\r\n    if (val === \"\" || val === null || val === undefined || Number.isNaN(val)) {\r\n      return undefined;\r\n    }\r\n    const numVal = Number(val);\r\n    return Number.isNaN(numVal) ? undefined : numVal;\r\n  }, z.number().nonnegative(\"Shipping cost must be a non-negative number\")),\r\n  shippingTime: z.enum(shippingTimeValues, {\r\n    required_error: \"Estimated shipping time is required\",\r\n  }),\r\n  freeShipping: z.boolean().default(false),\r\n  shippingClass: z.enum(shippingClassValues, {\r\n    required_error: \"Shipping class is required\",\r\n  }),\r\n  requiresShipping: z.boolean().default(true),\r\n  separateShipping: z.boolean().default(false),\r\n  shippingDimensions: dimensionsSchema.optional(),\r\n  shippingWeight: weightSchema.optional(),\r\n});\r\n\r\nexport const availabilitySchema = z.object({\r\n  status: z.enum(productStatusValues).default(\"draft\"),\r\n  ageRestriction: z.enum(ageRestrictionValues).default(\"none\"),\r\n  availableFrom: z.string().optional(),\r\n  availableUntil: z.string().optional(),\r\n  featured: z.boolean().default(false),\r\n  sticky: z.boolean().default(false),\r\n  downloadable: z.boolean().default(false),\r\n  virtual: z.boolean().default(false),\r\n});\r\n\r\n// New SEO schema\r\nexport const seoSchema = z.object({\r\n  metaTitle: z.string().optional(),\r\n  metaDescription: z.string().optional(),\r\n  focusKeyword: z.string().optional(),\r\n  slug: z.string().optional(),\r\n  canonicalUrl: z.preprocess(\r\n    (val) =>\r\n      val === \"\" || val === null || val === undefined ? undefined : val,\r\n    z.string().url().optional()\r\n  ),\r\n});\r\n\r\n// New warranty schema\r\nexport const warrantySchema = z.object({\r\n  duration: z.preprocess((val) => {\r\n    if (val === \"\" || val === null || val === undefined || Number.isNaN(val)) {\r\n      return undefined;\r\n    }\r\n    const numVal = Number(val);\r\n    return Number.isNaN(numVal) ? undefined : numVal;\r\n  }, z.number().int().positive().optional()),\r\n  type: z.enum([\"manufacturer\", \"seller\", \"extended\"]).optional(),\r\n  terms: z.string().optional(),\r\n  coverage: z.array(z.string()).optional(),\r\n});\r\n\r\n// Return policy schema\r\nexport const returnPolicySchema = z.object({\r\n  allowed: z.boolean().default(true),\r\n  period: z.number().int().positive().optional(),\r\n  conditions: z.array(z.string()).optional(),\r\n  restockingFee: z.number().nonnegative().optional(),\r\n});\r\n\r\n// Enhanced product schema with conditional fields based on product type\r\nexport const productSchema = z\r\n  .object({\r\n    ...basicInfoSchema.shape,\r\n    ...basePricingSchema.shape,\r\n    ...inventorySchema.shape,\r\n    ...detailsSchema.shape,\r\n    ...imagesSchema.shape,\r\n    ...shippingSchema.shape,\r\n    ...availabilitySchema.shape,\r\n    ...seoSchema.shape,\r\n    ...warrantySchema.shape,\r\n    ...returnPolicySchema.shape,\r\n  })\r\n  .refine(\r\n    (data) => {\r\n      // If on sale, sale price must be lower than regular price\r\n      if (data.isOnSale && data.salePrice) {\r\n        return data.salePrice < data.price;\r\n      }\r\n      return true;\r\n    },\r\n    {\r\n      message: \"Sale price must be lower than regular price\",\r\n      path: [\"salePrice\"],\r\n    }\r\n  )\r\n  .refine(\r\n    (data) => {\r\n      // If on sale, original price should equal regular price (for backwards compatibility)\r\n      if (data.isOnSale && data.originalPrice) {\r\n        return data.originalPrice >= data.price;\r\n      }\r\n      return true;\r\n    },\r\n    {\r\n      message: \"Original price should be at least the regular price\",\r\n      path: [\"originalPrice\"],\r\n    }\r\n  )\r\n  .refine(\r\n    (data) => {\r\n      // Conditional validation based on product type\r\n      if (data.productType === \"digital\") {\r\n        return data.requiresShipping === false;\r\n      }\r\n      if (data.productType === \"service\") {\r\n        return data.virtual === true;\r\n      }\r\n      return true;\r\n    },\r\n    {\r\n      message: \"Product configuration doesn't match the selected product type\",\r\n    }\r\n  )\r\n  .transform((data) => {\r\n    // Clean up fields that don't apply to certain product types\r\n    if (data.productType === \"digital\") {\r\n      // For digital products, remove physical-only fields\r\n      return {\r\n        ...data,\r\n        yearMade: undefined,\r\n        shippingCost: undefined,\r\n        dimensions: undefined,\r\n        weight: undefined,\r\n        requiresShipping: false,\r\n        virtual: true,\r\n      };\r\n    }\r\n    if (data.productType === \"service\") {\r\n      // For services, remove physical-only fields\r\n      return {\r\n        ...data,\r\n        yearMade: undefined,\r\n        shippingCost: undefined,\r\n        dimensions: undefined,\r\n        weight: undefined,\r\n        requiresShipping: false,\r\n        virtual: true,\r\n        stock: 0, // Services don't have stock\r\n      };\r\n    }\r\n    return data;\r\n  });\r\n\r\nexport type ProductFormData = z.infer<typeof productSchema>;\r\n\r\n// Individual section types for component props\r\nexport type BasicInfoFormData = z.infer<typeof basicInfoSchema>;\r\nexport type PricingFormData = z.infer<typeof basePricingSchema>;\r\nexport type InventoryFormData = z.infer<typeof inventorySchema>;\r\nexport type DetailsFormData = z.infer<typeof detailsSchema>;\r\nexport type ImagesFormData = z.infer<typeof imagesSchema>;\r\nexport type ShippingFormData = z.infer<typeof shippingSchema>;\r\nexport type AvailabilityFormData = z.infer<typeof availabilitySchema>;\r\nexport type SEOFormData = z.infer<typeof seoSchema>;\r\nexport type WarrantyFormData = z.infer<typeof warrantySchema>;\r\nexport type ReturnPolicyFormData = z.infer<typeof returnPolicySchema>;\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;AAC3B;;AAGO,MAAM,kBAAkB;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,oBAAoB;IAC/B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;CACD;AAEM,MAAM,kBAAkB;IAC7B;IACA;IACA;CACD;AAEM,MAAM,wBAAwB;IACnC;IACA;IACA;CACD;AAEM,MAAM,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,iBAAiB;IAAC;IAAO;CAAM;AACrC,MAAM,mBAAmB;IAAC;IAAK;CAAK;AACpC,MAAM,sBAAsB;IAAC;IAAM;CAAK;AACxC,MAAM,uBAAuB;IAAC;IAAQ;IAAO;CAAM;AAE1D,qBAAqB;AACrB,MAAM,mBAAmB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,QAAQ,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5B,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,MAAM,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;AACf;AAEA,MAAM,eAAe,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,MAAM,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;AACf;AAEA,0BAA0B;AAC1B,MAAM,gBAAgB,wLAAA,CAAA,IAAC,CACpB,MAAM,CAAC;IACN,QAAQ,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,MAAM,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzB,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,YAAY,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,SAAS,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,aAAa,wLAAA,CAAA,IAAC,CACX,MAAM,CAAC;QACN,UAAU,wLAAA,CAAA,IAAC,CAAC,MAAM;QAClB,WAAW,wLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,GACC,QAAQ;AACb,GACC,QAAQ;AAGJ,MAAM,kBAAkB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,MAAM,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,aAAa,wLAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,IAAI;IACX,kBAAkB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,aAAa,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,mBAAmB,OAAO,CAAC;IAC/C,YAAY,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,kBAAkB,OAAO,CAAC;AAC/C;AAGO,MAAM,oBAAoB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC3B,eAAe,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,CAAC;QAC3B,IAAI,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,aAAa,OAAO,KAAK,CAAC,MAAM;YACxE,OAAO;QACT;QACA,MAAM,SAAS,OAAO;QACtB,OAAO,OAAO,KAAK,CAAC,UAAU,YAAY;IAC5C,GAAG,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,4CAA4C,QAAQ;IAC3E,WAAW,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,CAAC;QACvB,IAAI,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,aAAa,OAAO,KAAK,CAAC,MAAM;YACxE,OAAO;QACT;QACA,MAAM,SAAS,OAAO;QACtB,OAAO,OAAO,KAAK,CAAC,UAAU,YAAY;IAC5C,GAAG,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,wCAAwC,QAAQ;IACvE,UAAU,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACjB,UAAU,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,YAAY,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,WAAW,wLAAA,CAAA,IAAC,CACT,MAAM,GACN,QAAQ,CAAC;IACZ,WAAW,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,iBAAiB,OAAO,CAAC;IAC3C,UAAU,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,sBAAsB,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,aAAa,OAAO,KAAK,CAAC,MAAM;YACxE,OAAO;QACT;QACA,MAAM,SAAS,OAAO;QACtB,OAAO,OAAO,KAAK,CAAC,UAAU,YAAY;IAC5C,GAAG,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;IACvC,sBAAsB,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,aAAa,OAAO,KAAK,CAAC,MAAM;YACxE,OAAO;QACT;QACA,MAAM,SAAS,OAAO;QACtB,OAAO,OAAO,KAAK,CAAC,UAAU,YAAY;IAC5C,GAAG,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;AACzC;AAGO,MAAM,gBAAgB,kBAC1B,MAAM,CACL,CAAC;IACC,0DAA0D;IAC1D,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,EAAE;QACnC,OAAO,KAAK,SAAS,GAAG,KAAK,KAAK;IACpC;IACA,OAAO;AACT,GACA;IACE,SAAS;IACT,MAAM;QAAC;KAAY;AACrB,GAED,MAAM,CACL,CAAC;IACC,sFAAsF;IACtF,IAAI,KAAK,QAAQ,IAAI,KAAK,aAAa,EAAE;QACvC,OAAO,KAAK,aAAa,IAAI,KAAK,KAAK;IACzC;IACA,OAAO;AACT,GACA;IACE,SAAS;IACT,MAAM;QAAC;KAAgB;AACzB;AAGG,MAAM,kBAAkB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,wLAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,GACH,WAAW,CAAC;IACf,WAAW,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAClB,aAAa,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACjC,iBAAiB,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,uBAAuB,OAAO,CAAC;IACvD,mBAAmB,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,CAAC;QAC/B,IAAI,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,aAAa,OAAO,KAAK,CAAC,MAAM;YACxE,OAAO;QACT;QACA,MAAM,SAAS,OAAO;QACtB,OAAO,OAAO,KAAK,CAAC,UAAU,YAAY;IAC5C,GAAG,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,WAAW,GAAG,QAAQ;IAC1C,kBAAkB,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACtC,eAAe,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACnC,kBAAkB,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACxC;AAEO,MAAM,gBAAgB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,UAAU,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,aAAa,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,YAAY,iBAAiB,QAAQ;IACrC,QAAQ,aAAa,QAAQ;IAC7B,UAAU,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,CAAC;QACtB,IAAI,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,aAAa,OAAO,KAAK,CAAC,MAAM;YACxE,OAAO;QACT;QACA,MAAM,SAAS,OAAO;QACtB,OAAO,OAAO,KAAK,CAAC,UAAU,YAAY;IAC5C,GAAG,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,+BAA+B,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,WAAW,IAAI,QAAQ;IACjG,MAAM,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG;IACjC,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,MAAM,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC3B;AAEO,MAAM,eAAe,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,WAAW,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC1B,QAAQ,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,8BAA8B,QAAQ;IACrE,eAAe,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IAC3C,UAAU,wLAAA,CAAA,IAAC,CAAC,UAAU,CACpB,CAAC,MACC,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,YAAY,YAAY,KAChE,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IAE3B,gBAAgB,wLAAA,CAAA,IAAC,CAAC,UAAU,CAC1B,CAAC,MACC,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,YAAY,YAAY,KAChE,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;AAE7B;AAEO,MAAM,iBAAiB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,UAAU,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,SAAS;IACT,cAAc,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,CAAC;QAC1B,IAAI,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,aAAa,OAAO,KAAK,CAAC,MAAM;YACxE,OAAO;QACT;QACA,MAAM,SAAS,OAAO;QACtB,OAAO,OAAO,KAAK,CAAC,UAAU,YAAY;IAC5C,GAAG,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,WAAW,CAAC;IAC1B,cAAc,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,oBAAoB;QACvC,gBAAgB;IAClB;IACA,cAAc,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,eAAe,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,qBAAqB;QACzC,gBAAgB;IAClB;IACA,kBAAkB,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACtC,kBAAkB,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACtC,oBAAoB,iBAAiB,QAAQ;IAC7C,gBAAgB,aAAa,QAAQ;AACvC;AAEO,MAAM,qBAAqB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,QAAQ,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,qBAAqB,OAAO,CAAC;IAC5C,gBAAgB,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,sBAAsB,OAAO,CAAC;IACrD,eAAe,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,gBAAgB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,UAAU,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,QAAQ,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,cAAc,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,SAAS,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B;AAGO,MAAM,YAAY,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,WAAW,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,iBAAiB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,cAAc,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,MAAM,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzB,cAAc,wLAAA,CAAA,IAAC,CAAC,UAAU,CACxB,CAAC,MACC,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,YAAY,YAAY,KAChE,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;AAE7B;AAGO,MAAM,iBAAiB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,UAAU,wLAAA,CAAA,IAAC,CAAC,UAAU,CAAC,CAAC;QACtB,IAAI,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,aAAa,OAAO,KAAK,CAAC,MAAM;YACxE,OAAO;QACT;QACA,MAAM,SAAS,OAAO;QACtB,OAAO,OAAO,KAAK,CAAC,UAAU,YAAY;IAC5C,GAAG,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;IACvC,MAAM,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAgB;QAAU;KAAW,EAAE,QAAQ;IAC7D,OAAO,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;AACxC;AAGO,MAAM,qBAAqB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,SAAS,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,QAAQ,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;IAC5C,YAAY,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IACxC,eAAe,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,WAAW,GAAG,QAAQ;AAClD;AAGO,MAAM,gBAAgB,wLAAA,CAAA,IAAC,CAC3B,MAAM,CAAC;IACN,GAAG,gBAAgB,KAAK;IACxB,GAAG,kBAAkB,KAAK;IAC1B,GAAG,gBAAgB,KAAK;IACxB,GAAG,cAAc,KAAK;IACtB,GAAG,aAAa,KAAK;IACrB,GAAG,eAAe,KAAK;IACvB,GAAG,mBAAmB,KAAK;IAC3B,GAAG,UAAU,KAAK;IAClB,GAAG,eAAe,KAAK;IACvB,GAAG,mBAAmB,KAAK;AAC7B,GACC,MAAM,CACL,CAAC;IACC,0DAA0D;IAC1D,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,EAAE;QACnC,OAAO,KAAK,SAAS,GAAG,KAAK,KAAK;IACpC;IACA,OAAO;AACT,GACA;IACE,SAAS;IACT,MAAM;QAAC;KAAY;AACrB,GAED,MAAM,CACL,CAAC;IACC,sFAAsF;IACtF,IAAI,KAAK,QAAQ,IAAI,KAAK,aAAa,EAAE;QACvC,OAAO,KAAK,aAAa,IAAI,KAAK,KAAK;IACzC;IACA,OAAO;AACT,GACA;IACE,SAAS;IACT,MAAM;QAAC;KAAgB;AACzB,GAED,MAAM,CACL,CAAC;IACC,+CAA+C;IAC/C,IAAI,KAAK,WAAW,KAAK,WAAW;QAClC,OAAO,KAAK,gBAAgB,KAAK;IACnC;IACA,IAAI,KAAK,WAAW,KAAK,WAAW;QAClC,OAAO,KAAK,OAAO,KAAK;IAC1B;IACA,OAAO;AACT,GACA;IACE,SAAS;AACX,GAED,SAAS,CAAC,CAAC;IACV,4DAA4D;IAC5D,IAAI,KAAK,WAAW,KAAK,WAAW;QAClC,oDAAoD;QACpD,OAAO;YACL,GAAG,IAAI;YACP,UAAU;YACV,cAAc;YACd,YAAY;YACZ,QAAQ;YACR,kBAAkB;YAClB,SAAS;QACX;IACF;IACA,IAAI,KAAK,WAAW,KAAK,WAAW;QAClC,4CAA4C;QAC5C,OAAO;YACL,GAAG,IAAI;YACP,UAAU;YACV,cAAc;YACd,YAAY;YACZ,QAAQ;YACR,kBAAkB;YAClB,SAAS;YACT,OAAO;QACT;IACF;IACA,OAAO;AACT", "debugId": null}}]}