import React from "react";

import {
  BarChart2,
  Blocks,
  CreditCard,
  Flame,
  Hammer,
  LayoutDashboard,
  List,
  Package,
  Palette,
  PlusCircle,
  Settings,
  Star,
  Store,
  Tag,
  Users,
} from "lucide-react";

export type NavItemType = {
  label: string;
  icon: React.ReactNode;
  href?: string;
  subItems?: {
    label: string;
    icon?: React.ReactNode;
    href: string;
    badge?: number;
    description?: string;
  }[];
  badge?: number;
  isDividerAfter?: boolean;
  isNew?: boolean;
  description?: string;
  group?: string;
};

export const NavItems: NavItemType[] = [
  // Main Navigation
  {
    label: "Dashboard",
    icon: <LayoutDashboard size={18} />,
    href: "/admin/dashboard",
    description: "Overview and analytics",
    group: "main",
  },

  // Catalog Management
  {
    label: "Products",
    icon: <Package size={18} />,
    description: "Manage your product catalog",
    group: "catalog",
    subItems: [
      {
        label: "All Products",
        icon: <List size={16} />,
        href: "/admin/products/list",
        description: "View and manage all products",
      },
      {
        label: "Add Product",
        icon: <PlusCircle size={16} />,
        href: "/admin/products/add",
        description: "Create a new product",
      },
    ],
  },
  {
    label: "Catalog",
    icon: <Blocks size={18} />,
    description: "Manage categories, brands, materials, and colors",
    group: "catalog",
    subItems: [
      {
        label: "Overview",
        icon: <LayoutDashboard size={16} />,
        href: "/admin/catalog",
        description: "Catalog management dashboard",
      },
      {
        label: "Categories",
        icon: <List size={16} />,
        href: "/admin/catalog/categories",
        description: "Organize product categories",
      },
      {
        label: "Brands",
        icon: <Tag size={16} />,
        href: "/admin/catalog/brands",
        description: "Manage product brands",
      },
      {
        label: "Materials",
        icon: <Hammer size={16} />,
        href: "/admin/catalog/materials",
        description: "Define product materials",
      },
      {
        label: "Colors",
        icon: <Palette size={16} />,
        href: "/admin/catalog/colors",
        description: "Manage color options",
      },
    ],
  },

  // Sales & Orders
  {
    label: "Orders",
    icon: <CreditCard size={18} />,
    href: "/admin/orders/list",
    description: "Manage customer orders",
    group: "sales",
    badge: 12,
  },
  {
    label: "Customers",
    icon: <Users size={18} />,
    description: "Manage customer relationships",
    group: "sales",
    subItems: [
      {
        label: "All Customers",
        icon: <List size={16} />,
        href: "/admin/customers/list",
        description: "View and manage all customers",
      },
      {
        label: "Create Account",
        icon: <PlusCircle size={16} />,
        href: "/admin/customers/add",
        description:
          "Create customer account for phone orders & business clients",
      },
    ],
  },

  // Analytics & Reports
  {
    label: "Analytics",
    icon: <BarChart2 size={18} />,
    href: "/admin/analytics",
    description: "Visual insights and performance analytics",
    group: "analytics",
  },
  {
    label: "Reviews",
    icon: <Star size={18} />,
    href: "/admin/reviews",
    description: "Customer feedback and ratings",
    group: "analytics",
    badge: 3,
  },

  // Financial
  {
    label: "Transactions",
    icon: <CreditCard size={18} />,
    badge: 8,
    href: "/admin/transactions",
    description: "Payment and transaction history",
    group: "financial",
  },
  {
    label: "Sellers",
    icon: <Store size={18} />,
    href: "/admin/sellers",
    description: "Manage seller accounts",
    group: "financial",
  },

  // Marketing
  {
    label: "Promotions",
    icon: <Flame size={18} />,
    isDividerAfter: true,
    href: "/admin/promotions",
    description: "Manage sales, discounts, and promotional campaigns",
    group: "marketing",
    isNew: true,
  },

  // System
  {
    label: "Appearance",
    icon: <Palette size={18} />,
    href: "/admin/appearance",
    description: "Customize your store's look",
    group: "system",
  },
  {
    label: "Settings",
    icon: <Settings size={18} />,
    href: "/admin/settings",
    description: "System configuration",
    group: "system",
  },
];
