import { Router } from "express";

import { ProductController } from "../controllers/product/product.controller";

const router = Router();
const productController = new ProductController();

// GET /api/products - Get all products with optional filtering
router.get("/", productController.getProducts);

// GET /api/products/:id - Get a product by ID
router.get("/:id", productController.getProductById);

// GET /api/products/:id/edit - Get product for editing with safe field list
router.get("/:id/edit", productController.getProductForEdit);

// POST /api/products - Create a new product
router.post("/", productController.createProduct);

// PUT /api/products/:id - Update a product (original method)
router.put("/:id", productController.updateProduct);

// PATCH /api/products/:id/edit - Update product with enhanced validation
router.patch("/:id/edit", productController.patchProduct);

// DELETE /api/products/:id - Delete a product
router.delete("/:id", productController.deleteProduct);

export const productRoutes = router;
