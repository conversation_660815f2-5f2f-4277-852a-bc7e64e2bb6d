"use client";

import {
  Activity,
  DollarSign,
  Package,
  ShoppingCart,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function AdminDashboard() {
  // Mock data for dashboard stats
  const stats = [
    {
      title: "Total Revenue",
      value: "$45,231.89",
      change: "+20.1%",
      trend: "up",
      icon: DollarSign,
    },
    {
      title: "Orders",
      value: "2,350",
      change: "+180.1%",
      trend: "up",
      icon: ShoppingCart,
    },
    {
      title: "Products",
      value: "12,234",
      change: "+19%",
      trend: "up",
      icon: Package,
    },
    {
      title: "Active Customers",
      value: "573",
      change: "+201",
      trend: "up",
      icon: Users,
    },
  ];

  const recentOrders = [
    {
      id: "ORD-001",
      customer: "<PERSON>",
      amount: "$299.99",
      status: "Completed",
    },
    {
      id: "ORD-002",
      customer: "<PERSON>",
      amount: "$159.50",
      status: "Processing",
    },
    {
      id: "ORD-003",
      customer: "<PERSON>",
      amount: "$89.99",
      status: "Shipped",
    },
    {
      id: "ORD-004",
      customer: "Sarah Wilson",
      amount: "$199.99",
      status: "Pending",
    },
  ];

  return (
    <>
      <PageHeaderWrapper
        title="Dashboard"
        description="Welcome to your admin dashboard - overview of your store performance"
      />

      <div className="container mx-auto mt-6 space-y-6">
        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="flex items-center text-xs text-muted-foreground">
                  {stat.trend === "up" ? (
                    <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                  ) : (
                    <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                  )}
                  {stat.change} from last month
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <Card className="col-span-4">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Orders
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div
                    key={order.id}
                    className="flex items-center justify-between"
                  >
                    <div>
                      <p className="text-sm font-medium">{order.id}</p>
                      <p className="text-sm text-muted-foreground">
                        {order.customer}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{order.amount}</p>
                      <p className="text-sm text-muted-foreground">
                        {order.status}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-3">
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-3">
                <div className="flex items-center gap-3 rounded-lg border p-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                    <ShoppingCart className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">New order #ORD-1234</p>
                    <p className="text-xs text-gray-500">
                      John Doe • $299.99 • 2 min ago
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-lg border p-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                    <Users className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">
                      New customer registered
                    </p>
                    <p className="text-xs text-gray-500">
                      Sarah Wilson • 15 min ago
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-lg border p-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-100">
                    <Package className="h-4 w-4 text-orange-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Low stock alert</p>
                    <p className="text-xs text-gray-500">
                      Wireless Headphones • 5 left
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Third Row - Operational Alerts */}
        <div className="grid gap-6 md:grid-cols-3">
          {/* Pending Tasks */}
          <Card>
            <CardHeader>
              <CardTitle>Pending Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 rounded-lg bg-yellow-50 p-2">
                  <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">5 orders to fulfill</p>
                    <p className="text-xs text-gray-500">Awaiting shipment</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-lg bg-red-50 p-2">
                  <div className="h-2 w-2 rounded-full bg-red-500"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">3 low stock items</p>
                    <p className="text-xs text-gray-500">Need restocking</p>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-lg bg-blue-50 p-2">
                  <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">2 customer inquiries</p>
                    <p className="text-xs text-gray-500">Awaiting response</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Inventory Alerts */}
          <Card>
            <CardHeader>
              <CardTitle>Inventory Alerts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Wireless Mouse</p>
                    <p className="text-xs text-gray-500">Only 3 left</p>
                  </div>
                  <span className="rounded bg-red-100 px-2 py-1 text-xs text-red-700">
                    Critical
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Phone Case</p>
                    <p className="text-xs text-gray-500">8 left</p>
                  </div>
                  <span className="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-700">
                    Low
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Laptop Stand</p>
                    <p className="text-xs text-gray-500">12 left</p>
                  </div>
                  <span className="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-700">
                    Low
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle>This Week</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Orders Processed
                  </span>
                  <span className="font-medium">127</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Revenue Generated
                  </span>
                  <span className="font-medium text-green-600">$15,420</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    New Products Added
                  </span>
                  <span className="font-medium">8</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Customer Reviews
                  </span>
                  <span className="font-medium">23</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Avg. Rating</span>
                  <span className="font-medium">4.7 ⭐</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Second Row - More Business Intelligence */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {/* Top Products */}
          <Card className="col-span-2">
            <CardHeader>
              <CardTitle>Top Selling Products</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded bg-gray-100">
                      <Package className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Wireless Headphones</p>
                      <p className="text-xs text-gray-500">Electronics</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">156 sold</p>
                    <p className="text-xs text-green-600">+12%</p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded bg-gray-100">
                      <Package className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Smart Watch</p>
                      <p className="text-xs text-gray-500">Electronics</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">89 sold</p>
                    <p className="text-xs text-green-600">+8%</p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded bg-gray-100">
                      <Package className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">USB-C Cable</p>
                      <p className="text-xs text-gray-500">Accessories</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">67 sold</p>
                    <p className="text-xs text-green-600">+15%</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer Insights */}
          <Card className="col-span-2">
            <CardHeader>
              <CardTitle>Customer Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    New Customers (30d)
                  </span>
                  <span className="font-medium">47</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Returning Customers
                  </span>
                  <span className="font-medium">156</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Customer Retention
                  </span>
                  <span className="font-medium text-green-600">78%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Avg. Order Value
                  </span>
                  <span className="font-medium">$127.50</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Customer Lifetime Value
                  </span>
                  <span className="font-medium">$456.80</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
