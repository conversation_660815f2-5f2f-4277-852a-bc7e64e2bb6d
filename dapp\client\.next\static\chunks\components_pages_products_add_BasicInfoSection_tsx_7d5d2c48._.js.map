{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/add/BasicInfoSection.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  ExternalLink,\r\n  Eye,\r\n  EyeOff,\r\n  FileText,\r\n  Hash,\r\n  Info,\r\n  Layers,\r\n  Lock,\r\n  Package,\r\n  Shield,\r\n  Sparkles,\r\n  Tag,\r\n  Users,\r\n} from \"lucide-react\";\r\nimport {\r\n  FieldErrors,\r\n  UseFormRegister,\r\n  UseFormSetValue,\r\n  UseFormWatch,\r\n} from \"react-hook-form\";\r\n\r\nimport { Brand } from \"@/components/pages/management/BrandManager\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { ProductFormData } from \"@/schemas/productSchema\";\r\n\r\nimport { FormField } from \"../../../common/FormField\";\r\nimport { CollapsibleSection } from \"../../../ui/collapsible-section\";\r\nimport { ImportantNotice } from \"../../../ui/important-notice\";\r\n\r\ntype BasicInfoSectionProps = {\r\n  register: UseFormRegister<ProductFormData>;\r\n  errors: FieldErrors<ProductFormData>;\r\n  setValue: UseFormSetValue<ProductFormData>;\r\n  watch: UseFormWatch<ProductFormData>;\r\n};\r\n\r\nexport const BasicInfoSection = ({\r\n  register,\r\n  errors,\r\n  setValue,\r\n  watch,\r\n}: BasicInfoSectionProps) => {\r\n  const [brands, setBrands] = useState<Brand[]>([]);\r\n\r\n  // Load brands from localStorage\r\n  useEffect(() => {\r\n    try {\r\n      const storedBrands = localStorage.getItem(\"product-brands\");\r\n      if (storedBrands) {\r\n        setBrands(JSON.parse(storedBrands));\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading brands from localStorage:\", error);\r\n    }\r\n  }, []);\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center gap-3 border-b border-gray-200 pb-4\">\r\n        <div className=\"rounded-full bg-blue-100 p-2\">\r\n          <Info className=\"h-5 w-5 text-blue-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">\r\n            Basic Information\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            Start by adding the essential details about your product\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Important Notice */}\r\n      <ImportantNotice\r\n        description=\"You must complete all required fields in this section before proceeding to the next step.\"\r\n        requiredFields={[\r\n          \"Product Name\",\r\n          \"Brand\",\r\n          \"Product Type\",\r\n          \"Product Description\",\r\n        ]}\r\n        tip=\"These basic details help customers find and understand your product, and are essential for creating a complete product listing.\"\r\n        variant=\"amber\"\r\n      />\r\n\r\n      {/* Product Name Card */}\r\n      <Card className=\"border-l-4 border-l-blue-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Package className=\"h-5 w-5 text-blue-600\" />\r\n            Product Name\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Choose a clear, descriptive name that customers will easily find\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"name\"\r\n            label=\"\"\r\n            error={errors.name?.message}\r\n            optional={false}\r\n          >\r\n            <Input\r\n              id=\"name\"\r\n              {...register(\"name\")}\r\n              placeholder=\"e.g., Wireless Bluetooth Headphones - Premium Quality\"\r\n              className=\"border-2 p-4 text-lg focus:border-blue-500\"\r\n            />\r\n          </FormField>\r\n\r\n          <div className=\"mt-3 rounded-lg bg-blue-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <Sparkles className=\"mt-0.5 h-4 w-4 text-blue-600\" />\r\n              <div className=\"text-sm text-blue-800\">\r\n                <strong>Pro tip:</strong> Include key features and benefits in\r\n                your product name for better searchability\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Brand Selection Card */}\r\n      <Card className=\"border-l-4 border-l-green-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Tag className=\"h-5 w-5 text-green-600\" />\r\n            Brand\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Select the brand or manufacturer of this product\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"brand\"\r\n            label=\"\"\r\n            error={errors.brand?.message}\r\n            optional={false}\r\n          >\r\n            <Select\r\n              onValueChange={(value) => setValue(\"brand\", value)}\r\n              value={watch(\"brand\")}\r\n            >\r\n              <SelectTrigger className=\"border-2 p-4 text-lg focus:border-green-500\">\r\n                <SelectValue placeholder=\"Choose a brand...\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {brands.length === 0 ? (\r\n                  <SelectItem value=\"no-brands\" disabled>\r\n                    No brands available - Create one first\r\n                  </SelectItem>\r\n                ) : (\r\n                  brands.map((brand) => (\r\n                    <SelectItem key={brand.id} value={brand.name}>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Tag className=\"h-4 w-4\" />\r\n                        {brand.name}\r\n                      </div>\r\n                    </SelectItem>\r\n                  ))\r\n                )}\r\n                <SelectItem value=\"other\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Sparkles className=\"h-4 w-4\" />\r\n                    Other (Custom Brand)\r\n                  </div>\r\n                </SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n\r\n            {watch(\"brand\") === \"other\" && (\r\n              <div className=\"mt-4 rounded-lg border border-amber-200 bg-amber-50 p-4\">\r\n                <Input\r\n                  id=\"custom-brand\"\r\n                  {...register(\"brand\")}\r\n                  placeholder=\"Enter custom brand name\"\r\n                  className=\"border-amber-300 focus:border-amber-500\"\r\n                />\r\n                <p className=\"mt-2 text-xs text-amber-700\">\r\n                  This will create a new brand in your catalog\r\n                </p>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"mt-3 flex items-center gap-2\">\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() =>\r\n                  window.open(\r\n                    \"/admin/products/catalog-settings?tab=brands\",\r\n                    \"_blank\"\r\n                  )\r\n                }\r\n                className=\"border-green-300 text-green-600 hover:bg-green-50\"\r\n              >\r\n                <ExternalLink className=\"mr-2 h-4 w-4\" />\r\n                Manage Brands\r\n              </Button>\r\n              <span className=\"text-xs text-gray-500\">\r\n                Add new brands or edit existing ones\r\n              </span>\r\n            </div>\r\n          </FormField>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Product Type Selection Card */}\r\n      <Card className=\"border-l-4 border-l-indigo-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <Layers className=\"h-5 w-5 text-indigo-600\" />\r\n            Product Type\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Choose the type of product you&apos;re selling - this affects\r\n            available options\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"productType\"\r\n            label=\"\"\r\n            error={errors.productType?.message}\r\n            optional={false}\r\n          >\r\n            <Select\r\n              onValueChange={(value) => {\r\n                setValue(\"productType\", value as any);\r\n\r\n                // Auto-configure fields based on product type\r\n                if (value === \"digital\") {\r\n                  setValue(\"requiresShipping\", false);\r\n                  setValue(\"virtual\", true);\r\n                  setValue(\"shippingCost\", undefined);\r\n                  setValue(\"yearMade\", undefined);\r\n                  setValue(\"dimensions\", undefined);\r\n                  setValue(\"weight\", undefined);\r\n                } else if (value === \"service\") {\r\n                  setValue(\"requiresShipping\", false);\r\n                  setValue(\"virtual\", true);\r\n                  setValue(\"stock\", 0);\r\n                  setValue(\"shippingCost\", undefined);\r\n                  setValue(\"yearMade\", undefined);\r\n                  setValue(\"dimensions\", undefined);\r\n                  setValue(\"weight\", undefined);\r\n                } else if (value === \"physical\") {\r\n                  setValue(\"requiresShipping\", true);\r\n                  setValue(\"virtual\", false);\r\n                  setValue(\"stock\", 1);\r\n                }\r\n              }}\r\n              value={watch(\"productType\")}\r\n            >\r\n              <SelectTrigger className=\"border-2 p-4 text-lg focus:border-indigo-500\">\r\n                <SelectValue placeholder=\"Select product type...\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"physical\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Package className=\"h-4 w-4\" />\r\n                    <span className=\"font-medium\">Physical Product</span>\r\n                  </div>\r\n                </SelectItem>\r\n                <SelectItem value=\"digital\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Hash className=\"h-4 w-4\" />\r\n                    <span className=\"font-medium\">Digital Product</span>\r\n                  </div>\r\n                </SelectItem>\r\n                <SelectItem value=\"service\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Sparkles className=\"h-4 w-4\" />\r\n                    <span className=\"font-medium\">Service</span>\r\n                  </div>\r\n                </SelectItem>\r\n                <SelectItem value=\"subscription\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Layers className=\"h-4 w-4\" />\r\n                    <span className=\"font-medium\">Subscription</span>\r\n                  </div>\r\n                </SelectItem>\r\n                <SelectItem value=\"bundle\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Package className=\"h-4 w-4\" />\r\n                    <span className=\"font-medium\">Bundle</span>\r\n                  </div>\r\n                </SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </FormField>\r\n\r\n          {/* Product Type Descriptions */}\r\n          {watch(\"productType\") && (\r\n            <div className=\"mt-3 rounded-lg border border-indigo-200 bg-indigo-50 p-3\">\r\n              <div className=\"flex items-start gap-2\">\r\n                <Layers className=\"mt-0.5 h-4 w-4 text-indigo-600\" />\r\n                <div className=\"text-sm text-indigo-800\">\r\n                  <strong>\r\n                    {watch(\"productType\") === \"physical\" && \"Physical Product:\"}\r\n                    {watch(\"productType\") === \"digital\" && \"Digital Product:\"}\r\n                    {watch(\"productType\") === \"service\" && \"Service:\"}\r\n                    {watch(\"productType\") === \"subscription\" && \"Subscription:\"}\r\n                    {watch(\"productType\") === \"bundle\" && \"Bundle:\"}\r\n                  </strong>\r\n                  <span className=\"ml-1\">\r\n                    {watch(\"productType\") === \"physical\" &&\r\n                      \"Tangible items that require shipping and inventory tracking\"}\r\n                    {watch(\"productType\") === \"digital\" &&\r\n                      \"Downloads, software, digital content - no shipping required\"}\r\n                    {watch(\"productType\") === \"service\" &&\r\n                      \"Consultations, appointments, virtual services - no physical delivery\"}\r\n                    {watch(\"productType\") === \"subscription\" &&\r\n                      \"Recurring billing products with ongoing access\"}\r\n                    {watch(\"productType\") === \"bundle\" &&\r\n                      \"Multiple products sold together as a package\"}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"mt-3 rounded-lg bg-blue-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <Sparkles className=\"mt-0.5 h-4 w-4 text-blue-600\" />\r\n              <div className=\"text-sm text-blue-800\">\r\n                <strong>Note:</strong> Product type affects which form sections\r\n                are required and available options\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Description Card */}\r\n      <Card className=\"border-l-4 border-l-orange-500\">\r\n        <CardHeader className=\"pb-3\">\r\n          <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n            <FileText className=\"h-5 w-5 text-orange-600\" />\r\n            Product Description\r\n            <Badge variant=\"destructive\" className=\"text-xs\">\r\n              Required\r\n            </Badge>\r\n          </CardTitle>\r\n          <p className=\"text-sm text-gray-600\">\r\n            Provide a detailed description that highlights features, benefits,\r\n            and specifications\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <FormField\r\n            id=\"description\"\r\n            label=\"\"\r\n            error={errors.description?.message}\r\n            optional={false}\r\n          >\r\n            <Textarea\r\n              id=\"description\"\r\n              {...register(\"description\")}\r\n              placeholder=\"Describe your product in detail. Include features, benefits, materials, dimensions, and any other relevant information that will help customers make a purchasing decision...\"\r\n              rows={6}\r\n              className=\"resize-none border-2 p-4 text-base focus:border-orange-500\"\r\n            />\r\n          </FormField>\r\n\r\n          <div className=\"mt-3 rounded-lg bg-orange-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <FileText className=\"mt-0.5 h-4 w-4 text-orange-600\" />\r\n              <div className=\"text-sm text-orange-800\">\r\n                <strong>Writing tips:</strong> Use bullet points for features,\r\n                mention materials and quality, include size/compatibility info,\r\n                and highlight unique selling points\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Product Visibility - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Visibility\"\r\n        description=\"Control who can see and purchase this product\"\r\n        icon={<Eye className=\"h-5 w-5 text-cyan-600\" />}\r\n        borderColor=\"border-l-cyan-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <FormField id=\"visibility\" label=\"\" optional={true}>\r\n          <Select\r\n            onValueChange={(value) => setValue(\"visibility\", value as any)}\r\n            value={watch(\"visibility\")}\r\n          >\r\n            <SelectTrigger className=\"border-2 p-4 text-lg focus:border-cyan-500\">\r\n              <SelectValue placeholder=\"Select visibility...\" />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"public\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Users className=\"h-4 w-4\" />\r\n                  <span className=\"font-medium\">Public</span>\r\n                </div>\r\n              </SelectItem>\r\n              <SelectItem value=\"private\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Shield className=\"h-4 w-4\" />\r\n                  <span className=\"font-medium\">Private</span>\r\n                </div>\r\n              </SelectItem>\r\n              <SelectItem value=\"hidden\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <EyeOff className=\"h-4 w-4\" />\r\n                  <span className=\"font-medium\">Hidden</span>\r\n                </div>\r\n              </SelectItem>\r\n              <SelectItem value=\"password-protected\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Lock className=\"h-4 w-4\" />\r\n                  <span className=\"font-medium\">Password Protected</span>\r\n                </div>\r\n              </SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n        </FormField>\r\n\r\n        {/* Visibility Descriptions */}\r\n        {watch(\"visibility\") && (\r\n          <div className=\"mt-3 rounded-lg border border-cyan-200 bg-cyan-50 p-3\">\r\n            <div className=\"flex items-start gap-2\">\r\n              {watch(\"visibility\") === \"public\" && (\r\n                <Users className=\"mt-0.5 h-4 w-4 text-cyan-600\" />\r\n              )}\r\n              {watch(\"visibility\") === \"private\" && (\r\n                <Shield className=\"mt-0.5 h-4 w-4 text-cyan-600\" />\r\n              )}\r\n              {watch(\"visibility\") === \"hidden\" && (\r\n                <EyeOff className=\"mt-0.5 h-4 w-4 text-cyan-600\" />\r\n              )}\r\n              {watch(\"visibility\") === \"password-protected\" && (\r\n                <Lock className=\"mt-0.5 h-4 w-4 text-cyan-600\" />\r\n              )}\r\n              <div className=\"text-sm text-cyan-800\">\r\n                <strong>\r\n                  {watch(\"visibility\") === \"public\" && \"Public:\"}\r\n                  {watch(\"visibility\") === \"private\" && \"Private:\"}\r\n                  {watch(\"visibility\") === \"hidden\" && \"Hidden:\"}\r\n                  {watch(\"visibility\") === \"password-protected\" &&\r\n                    \"Password Protected:\"}\r\n                </strong>\r\n                <span className=\"ml-1\">\r\n                  {watch(\"visibility\") === \"public\" &&\r\n                    \"Visible to everyone in your store and search engines\"}\r\n                  {watch(\"visibility\") === \"private\" &&\r\n                    \"Only visible to you and store administrators\"}\r\n                  {watch(\"visibility\") === \"hidden\" &&\r\n                    \"Not visible in catalog but accessible via direct link\"}\r\n                  {watch(\"visibility\") === \"password-protected\" &&\r\n                    \"Requires password to view - perfect for exclusive products\"}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CollapsibleSection>\r\n\r\n      {/* Model Number - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Model Number\"\r\n        description=\"Add the specific model number or identifier if available\"\r\n        icon={<Package className=\"h-5 w-5 text-purple-600\" />}\r\n        borderColor=\"border-l-purple-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <FormField id=\"model\" label=\"\" optional={true}>\r\n          <Input\r\n            id=\"model\"\r\n            {...register(\"model\")}\r\n            placeholder=\"e.g., WH-1000XM4, iPhone 15 Pro, Model ABC-123\"\r\n            className=\"border-2 p-4 text-lg focus:border-purple-500\"\r\n          />\r\n        </FormField>\r\n      </CollapsibleSection>\r\n\r\n      {/* Short Description - Collapsible */}\r\n      <CollapsibleSection\r\n        title=\"Short Description\"\r\n        description=\"Brief summary for product listings and search results\"\r\n        icon={<FileText className=\"h-5 w-5 text-amber-600\" />}\r\n        borderColor=\"border-l-amber-500\"\r\n        defaultOpen={false}\r\n        isOptional={true}\r\n      >\r\n        <FormField id=\"shortDescription\" label=\"\" optional={true}>\r\n          <Textarea\r\n            id=\"shortDescription\"\r\n            {...register(\"shortDescription\")}\r\n            placeholder=\"A brief, compelling summary of your product in 1-2 sentences...\"\r\n            rows={3}\r\n            className=\"resize-none border-2 p-4 text-base focus:border-amber-500\"\r\n          />\r\n        </FormField>\r\n\r\n        <div className=\"mt-3 rounded-lg bg-amber-50 p-3\">\r\n          <div className=\"flex items-start gap-2\">\r\n            <FileText className=\"mt-0.5 h-4 w-4 text-amber-600\" />\r\n            <div className=\"text-sm text-amber-800\">\r\n              <strong>Tip:</strong> Keep it under 160 characters for optimal\r\n              display in search results\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CollapsibleSection>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AACA;AACA;AACA;AACA;AAOA;AAGA;AACA;AACA;;;;;;;;;;;;;;AASO,MAAM,mBAAmB,CAAC,EAC/B,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,KAAK,EACiB;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,gCAAgC;IAChC,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI;gBACF,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,IAAI,cAAc;oBAChB,UAAU,KAAK,KAAK,CAAC;gBACvB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2CAA2C;YAC3D;QACF;qCAAG,EAAE;IACL,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,yRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,4TAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAOjC,4TAAC,2IAAA,CAAA,kBAAe;gBACd,aAAY;gBACZ,gBAAgB;oBACd;oBACA;oBACA;oBACA;iBACD;gBACD,KAAI;gBACJ,SAAQ;;;;;;0BAIV,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,+RAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA0B;kDAE7C,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;;0CACV,4TAAC,qIAAA,CAAA,YAAS;gCACR,IAAG;gCACH,OAAM;gCACN,OAAO,OAAO,IAAI,EAAE;gCACpB,UAAU;0CAEV,cAAA,4TAAC,6HAAA,CAAA,QAAK;oCACJ,IAAG;oCACF,GAAG,SAAS,OAAO;oCACpB,aAAY;oCACZ,WAAU;;;;;;;;;;;0CAId,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,iSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;8DAAO;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnC,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,uRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAA2B;kDAE1C,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,4TAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,4TAAC,qIAAA,CAAA,YAAS;4BACR,IAAG;4BACH,OAAM;4BACN,OAAO,OAAO,KAAK,EAAE;4BACrB,UAAU;;8CAEV,4TAAC,8HAAA,CAAA,SAAM;oCACL,eAAe,CAAC,QAAU,SAAS,SAAS;oCAC5C,OAAO,MAAM;;sDAEb,4TAAC,8HAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,4TAAC,8HAAA,CAAA,gBAAa;;gDACX,OAAO,MAAM,KAAK,kBACjB,4TAAC,8HAAA,CAAA,aAAU;oDAAC,OAAM;oDAAY,QAAQ;8DAAC;;;;;2DAIvC,OAAO,GAAG,CAAC,CAAC,sBACV,4TAAC,8HAAA,CAAA,aAAU;wDAAgB,OAAO,MAAM,IAAI;kEAC1C,cAAA,4TAAC;4DAAI,WAAU;;8EACb,4TAAC,uRAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEACd,MAAM,IAAI;;;;;;;uDAHE,MAAM,EAAE;;;;;8DAQ7B,4TAAC,8HAAA,CAAA,aAAU;oDAAC,OAAM;8DAChB,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,iSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;;;;;;;;;;;;gCAOvC,MAAM,aAAa,yBAClB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,6HAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,SAAS,QAAQ;4CACrB,aAAY;4CACZ,WAAU;;;;;;sDAEZ,4TAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;8CAM/C,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IACP,OAAO,IAAI,CACT,+CACA;4CAGJ,WAAU;;8DAEV,4TAAC,6SAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG3C,4TAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShD,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA4B;kDAE9C,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,4TAAC,4HAAA,CAAA,cAAW;;0CACV,4TAAC,qIAAA,CAAA,YAAS;gCACR,IAAG;gCACH,OAAM;gCACN,OAAO,OAAO,WAAW,EAAE;gCAC3B,UAAU;0CAEV,cAAA,4TAAC,8HAAA,CAAA,SAAM;oCACL,eAAe,CAAC;wCACd,SAAS,eAAe;wCAExB,8CAA8C;wCAC9C,IAAI,UAAU,WAAW;4CACvB,SAAS,oBAAoB;4CAC7B,SAAS,WAAW;4CACpB,SAAS,gBAAgB;4CACzB,SAAS,YAAY;4CACrB,SAAS,cAAc;4CACvB,SAAS,UAAU;wCACrB,OAAO,IAAI,UAAU,WAAW;4CAC9B,SAAS,oBAAoB;4CAC7B,SAAS,WAAW;4CACpB,SAAS,SAAS;4CAClB,SAAS,gBAAgB;4CACzB,SAAS,YAAY;4CACrB,SAAS,cAAc;4CACvB,SAAS,UAAU;wCACrB,OAAO,IAAI,UAAU,YAAY;4CAC/B,SAAS,oBAAoB;4CAC7B,SAAS,WAAW;4CACpB,SAAS,SAAS;wCACpB;oCACF;oCACA,OAAO,MAAM;;sDAEb,4TAAC,8HAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,4TAAC,8HAAA,CAAA,gBAAa;;8DACZ,4TAAC,8HAAA,CAAA,aAAU;oDAAC,OAAM;8DAChB,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,+RAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,4TAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;;;;;;8DAGlC,4TAAC,8HAAA,CAAA,aAAU;oDAAC,OAAM;8DAChB,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,4TAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;;;;;;8DAGlC,4TAAC,8HAAA,CAAA,aAAU;oDAAC,OAAM;8DAChB,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,iSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,4TAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;;;;;;8DAGlC,4TAAC,8HAAA,CAAA,aAAU;oDAAC,OAAM;8DAChB,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,6RAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,4TAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;;;;;;8DAGlC,4TAAC,8HAAA,CAAA,aAAU;oDAAC,OAAM;8DAChB,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,+RAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,4TAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQvC,MAAM,gCACL,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,6RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;;wDACE,MAAM,mBAAmB,cAAc;wDACvC,MAAM,mBAAmB,aAAa;wDACtC,MAAM,mBAAmB,aAAa;wDACtC,MAAM,mBAAmB,kBAAkB;wDAC3C,MAAM,mBAAmB,YAAY;;;;;;;8DAExC,4TAAC;oDAAK,WAAU;;wDACb,MAAM,mBAAmB,cACxB;wDACD,MAAM,mBAAmB,aACxB;wDACD,MAAM,mBAAmB,aACxB;wDACD,MAAM,mBAAmB,kBACxB;wDACD,MAAM,mBAAmB,YACxB;;;;;;;;;;;;;;;;;;;;;;;;0CAOZ,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,iSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;8DAAO;;;;;;gDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShC,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,4TAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4TAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,4TAAC,qSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA4B;kDAEhD,4TAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAU;;;;;;;;;;;;0CAInD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,4TAAC,4HAAA,CAAA,cAAW;;0CACV,4TAAC,qIAAA,CAAA,YAAS;gCACR,IAAG;gCACH,OAAM;gCACN,OAAO,OAAO,WAAW,EAAE;gCAC3B,UAAU;0CAEV,cAAA,4TAAC,gIAAA,CAAA,WAAQ;oCACP,IAAG;oCACF,GAAG,SAAS,cAAc;oCAC3B,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;0CAId,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,qSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;8DAAO;;;;;;gDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxC,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,uRAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;gBACrB,aAAY;gBACZ,aAAa;gBACb,YAAY;;kCAEZ,4TAAC,qIAAA,CAAA,YAAS;wBAAC,IAAG;wBAAa,OAAM;wBAAG,UAAU;kCAC5C,cAAA,4TAAC,8HAAA,CAAA,SAAM;4BACL,eAAe,CAAC,QAAU,SAAS,cAAc;4BACjD,OAAO,MAAM;;8CAEb,4TAAC,8HAAA,CAAA,gBAAa;oCAAC,WAAU;8CACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;8CAE3B,4TAAC,8HAAA,CAAA,gBAAa;;sDACZ,4TAAC,8HAAA,CAAA,aAAU;4CAAC,OAAM;sDAChB,cAAA,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,2RAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,4TAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;;;;;;;sDAGlC,4TAAC,8HAAA,CAAA,aAAU;4CAAC,OAAM;sDAChB,cAAA,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,6RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,4TAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;;;;;;;sDAGlC,4TAAC,8HAAA,CAAA,aAAU;4CAAC,OAAM;sDAChB,cAAA,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,iSAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,4TAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;;;;;;;sDAGlC,4TAAC,8HAAA,CAAA,aAAU;4CAAC,OAAM;sDAChB,cAAA,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,4TAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQvC,MAAM,+BACL,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;;gCACZ,MAAM,kBAAkB,0BACvB,4TAAC,2RAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAElB,MAAM,kBAAkB,2BACvB,4TAAC,6RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAEnB,MAAM,kBAAkB,0BACvB,4TAAC,iSAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAEnB,MAAM,kBAAkB,sCACvB,4TAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAElB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;gDACE,MAAM,kBAAkB,YAAY;gDACpC,MAAM,kBAAkB,aAAa;gDACrC,MAAM,kBAAkB,YAAY;gDACpC,MAAM,kBAAkB,wBACvB;;;;;;;sDAEJ,4TAAC;4CAAK,WAAU;;gDACb,MAAM,kBAAkB,YACvB;gDACD,MAAM,kBAAkB,aACvB;gDACD,MAAM,kBAAkB,YACvB;gDACD,MAAM,kBAAkB,wBACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,+RAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBACzB,aAAY;gBACZ,aAAa;gBACb,YAAY;0BAEZ,cAAA,4TAAC,qIAAA,CAAA,YAAS;oBAAC,IAAG;oBAAQ,OAAM;oBAAG,UAAU;8BACvC,cAAA,4TAAC,6HAAA,CAAA,QAAK;wBACJ,IAAG;wBACF,GAAG,SAAS,QAAQ;wBACrB,aAAY;wBACZ,WAAU;;;;;;;;;;;;;;;;0BAMhB,4TAAC,8IAAA,CAAA,qBAAkB;gBACjB,OAAM;gBACN,aAAY;gBACZ,oBAAM,4TAAC,qSAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,aAAY;gBACZ,aAAa;gBACb,YAAY;;kCAEZ,4TAAC,qIAAA,CAAA,YAAS;wBAAC,IAAG;wBAAmB,OAAM;wBAAG,UAAU;kCAClD,cAAA,4TAAC,gIAAA,CAAA,WAAQ;4BACP,IAAG;4BACF,GAAG,SAAS,mBAAmB;4BAChC,aAAY;4BACZ,MAAM;4BACN,WAAU;;;;;;;;;;;kCAId,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,qSAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;sDAAO;;;;;;wCAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnC;GA1ea;KAAA", "debugId": null}}]}