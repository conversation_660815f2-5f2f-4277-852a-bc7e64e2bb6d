import { Document } from "mongoose";

/**
 * RGB color value interface
 */
export interface RgbValue {
  r: number;
  g: number;
  b: number;
}

/**
 * HSL color value interface
 */
export interface HslValue {
  h: number;
  s: number;
  l: number;
}

/**
 * Color family type
 */
export type ColorFamily = "red" | "orange" | "yellow" | "green" | "blue" | "purple" | "pink" | "brown" | "gray" | "black" | "white";

/**
 * Color interface matching the frontend Color type
 */
export interface IColor {
  name: string;
  description: string;
  slug: string;
  hexValue: string;
  rgbValue: RgbValue;
  hslValue: HslValue;
  colorFamily: ColorFamily;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
}

/**
 * Color document interface for MongoDB
 */
export interface ColorDocument extends IColor, Document {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
  hexToRgb(hex: string): RgbValue;
  rgbToHsl(r: number, g: number, b: number): HslValue;
}

/**
 * DTO for creating a new color
 */
export interface CreateColorDto {
  name: string;
  description?: string;
  hexValue: string;
  rgbValue?: RgbValue;
  hslValue?: HslValue;
  colorFamily: ColorFamily;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * DTO for updating a color
 */
export interface UpdateColorDto {
  name?: string;
  description?: string;
  slug?: string;
  hexValue?: string;
  rgbValue?: RgbValue;
  hslValue?: HslValue;
  colorFamily?: ColorFamily;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * Color filters for querying
 */
export interface ColorFilters {
  isActive?: boolean;
  search?: string;
  colorFamily?: ColorFamily;
  hexValue?: string;
}
