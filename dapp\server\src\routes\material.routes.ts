import { Router } from "express";

import { MaterialController } from "../controllers/material/material.controller";

const router = Router();
const materialController = new MaterialController();

// GET /api/materials - Get all materials with optional filtering
router.get("/", materialController.getMaterials);

// GET /api/materials/slug/:slug - Get material by slug
router.get("/slug/:slug", materialController.getMaterialBySlug);

// POST /api/materials/recalculate-counts - Recalculate product counts
router.post("/recalculate-counts", materialController.recalculateProductCounts);

// GET /api/materials/:id - Get material by ID
router.get("/:id", materialController.getMaterialById);

// POST /api/materials - Create a new material
router.post("/", materialController.createMaterial);

// PUT /api/materials/:id - Update a material
router.put("/:id", materialController.updateMaterial);

// DELETE /api/materials/:id - Delete a material
router.delete("/:id", materialController.deleteMaterial);

export default router;
