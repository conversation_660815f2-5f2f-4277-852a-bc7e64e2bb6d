import { redirect } from "next/navigation";

// Redirect to the products list page with preserved query parameters
export default function AdminProducts({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  // Preserve query parameters in the redirect
  const queryString = new URLSearchParams();

  Object.entries(searchParams).forEach(([key, value]) => {
    if (value !== undefined) {
      if (Array.isArray(value)) {
        value.forEach((v) => queryString.append(key, v));
      } else {
        queryString.append(key, value);
      }
    }
  });

  const redirectUrl = queryString.toString()
    ? `/admin/products/list?${queryString.toString()}`
    : "/admin/products/list";

  redirect(redirectUrl);
}
