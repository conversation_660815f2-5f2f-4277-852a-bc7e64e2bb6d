import { FilterQuery } from "mongoose";

import { Category, CategoryDocument } from "../models/category.model";
import {
  CategoryFilters,
  CreateCategoryDto,
  UpdateCategoryDto,
} from "../types/category.types";

/**
 * Service class for handling category-related business logic
 */
export class CategoryService {
  /**
   * Create a new category
   */
  async createCategory(
    categoryData: CreateCategoryDto
  ): Promise<CategoryDocument> {
    try {
      // Generate slug if not provided
      const slug = this.generateSlug(categoryData.name);

      // Check if slug already exists
      const existingCategory = await Category.findOne({ slug });
      if (existingCategory) {
        throw new Error("A category with this name already exists");
      }

      // Get the next sort order
      const maxSortOrder = await Category.findOne(
        {},
        {},
        { sort: { sortOrder: -1 } }
      );
      const sortOrder =
        categoryData.sortOrder ?? (maxSortOrder?.sortOrder ?? 0) + 1;

      const category = new Category({
        ...categoryData,
        slug,
        sortOrder,
      });

      return await category.save();
    } catch (error) {
      throw this.handleError(error, "Error creating category");
    }
  }

  /**
   * Get all categories with optional filtering
   */
  async getCategories(
    filters: CategoryFilters = {}
  ): Promise<CategoryDocument[]> {
    try {
      const query: FilterQuery<CategoryDocument> = {};

      // Apply filters
      if (filters.isActive !== undefined) {
        query.isActive = filters.isActive;
      }

      if (filters.parentId !== undefined) {
        query.parentId = filters.parentId || null;
      }

      if (filters.search) {
        query.$text = { $search: filters.search };
      }

      return await Category.find(query)
        .populate("parentId", "name slug")
        .sort({ sortOrder: 1, createdAt: -1 });
    } catch (error) {
      throw this.handleError(error, "Error fetching categories");
    }
  }

  /**
   * Get a category by ID
   */
  async getCategoryById(id: string): Promise<CategoryDocument | null> {
    try {
      return await Category.findById(id).populate("parentId", "name slug");
    } catch (error) {
      throw this.handleError(error, "Error fetching category");
    }
  }

  /**
   * Get a category by slug
   */
  async getCategoryBySlug(slug: string): Promise<CategoryDocument | null> {
    try {
      return await Category.findOne({ slug }).populate("parentId", "name slug");
    } catch (error) {
      throw this.handleError(error, "Error fetching category");
    }
  }

  /**
   * Get a category by name
   */
  async getCategoryByName(name: string): Promise<CategoryDocument | null> {
    try {
      return await Category.findOne({ name });
    } catch (error) {
      throw this.handleError(error, "Error fetching category by name");
    }
  }

  /**
   * Update a category
   */
  async updateCategory(
    id: string,
    updateData: UpdateCategoryDto
  ): Promise<CategoryDocument | null> {
    try {
      // If name is being updated, regenerate slug
      if (updateData.name) {
        const newSlug = this.generateSlug(updateData.name);

        // Check if new slug conflicts with existing categories (excluding current one)
        const existingCategory = await Category.findOne({
          slug: newSlug,
          _id: { $ne: id },
        });

        if (existingCategory) {
          throw new Error("A category with this name already exists");
        }

        updateData = { ...updateData, slug: newSlug };
      }

      return await Category.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      }).populate("parentId", "name slug");
    } catch (error) {
      throw this.handleError(error, "Error updating category");
    }
  }

  /**
   * Delete a category
   */
  async deleteCategory(id: string): Promise<boolean> {
    try {
      // Check if category has child categories
      const childCategories = await Category.find({ parentId: id });
      if (childCategories.length > 0) {
        throw new Error("Cannot delete category with subcategories");
      }

      // Check if category has products (you might want to implement this check)
      const category = await Category.findById(id);
      if (category && category.productCount > 0) {
        throw new Error("Cannot delete category with products");
      }

      const result = await Category.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      throw this.handleError(error, "Error deleting category");
    }
  }

  /**
   * Update product count for a category
   */
  async updateProductCount(
    categoryId: string,
    increment: number = 1
  ): Promise<void> {
    try {
      await Category.findByIdAndUpdate(categoryId, {
        $inc: { productCount: increment },
      });
    } catch (error) {
      console.error("Error updating category product count:", error);
    }
  }

  /**
   * Recalculate product counts for all categories
   * This method should be called to fix any inconsistencies
   */
  async recalculateProductCounts(): Promise<void> {
    try {
      // Import Product model here to avoid circular dependency
      const { Product } = await import("../models/product.model");

      console.log("Starting product count recalculation...");

      // Get all categories
      const categories = await Category.find({});
      console.log(`Found ${categories.length} categories to process`);

      // Get all products to see what category values they have
      const allProducts = await Product.find({}, { category: 1, name: 1 });
      console.log(`Found ${allProducts.length} total products`);

      // Log sample products to see category format
      if (allProducts.length > 0) {
        console.log("Sample products with categories:");
        allProducts.slice(0, 3).forEach((product) => {
          console.log(
            `- Product: "${product.name}", Category: "${product.category}" (type: ${typeof product.category})`
          );
        });
      }

      for (const category of categories) {
        const categoryIdString = category._id.toString();
        console.log(
          `\nProcessing category: "${category.name}" (ID: ${categoryIdString})`
        );

        // Try multiple query variations to find the right format
        const countByString = await Product.countDocuments({
          category: categoryIdString,
        });

        const countByObjectId = await Product.countDocuments({
          category: category._id,
        });

        // IMPORTANT: Also try by category name (legacy format)
        const countByName = await Product.countDocuments({
          category: category.name,
        });

        // Also try to find products that match this category
        const matchingProductsById = await Product.find(
          {
            category: categoryIdString,
          },
          { name: 1, category: 1 }
        ).limit(5);

        const matchingProductsByName = await Product.find(
          {
            category: category.name,
          },
          { name: 1, category: 1 }
        ).limit(5);

        console.log(`  - Count by string ID: ${countByString}`);
        console.log(`  - Count by ObjectId: ${countByObjectId}`);
        console.log(`  - Count by name: ${countByName}`);
        console.log(
          `  - Matching products by ID: ${matchingProductsById.length}`
        );
        console.log(
          `  - Matching products by name: ${matchingProductsByName.length}`
        );

        if (matchingProductsById.length > 0) {
          console.log("  - Sample matching products by ID:");
          matchingProductsById.forEach((p) => {
            console.log(`    * "${p.name}" (category: "${p.category}")`);
          });
        }

        if (matchingProductsByName.length > 0) {
          console.log("  - Sample matching products by name:");
          matchingProductsByName.forEach((p) => {
            console.log(`    * "${p.name}" (category: "${p.category}")`);
          });
        }

        // Use the highest count (covers all possible formats)
        const actualCount = Math.max(
          countByString,
          countByObjectId,
          countByName
        );

        // Update the category with the correct count
        await Category.findByIdAndUpdate(category._id, {
          productCount: actualCount,
        });

        console.log(
          `  ✓ Updated category "${category.name}" product count to ${actualCount}`
        );
      }

      console.log("Product count recalculation completed successfully");
    } catch (error) {
      console.error("Error recalculating product counts:", error);
      throw error;
    }
  }

  /**
   * Generate slug from name
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");
  }

  /**
   * Handle and format errors
   */
  private handleError(error: unknown, defaultMessage: string): Error {
    if (error instanceof Error) {
      return error;
    }
    return new Error(defaultMessage);
  }
}
