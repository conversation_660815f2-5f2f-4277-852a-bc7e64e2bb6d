"use client";

import React from "react";

import { FileDown, Plus, RefreshCw, Upload, UserPlus } from "lucide-react";

import { Button } from "@/components/ui/button";

export const CustomersListActions = () => {
  const handleExport = () => console.log("Exporting customers...");
  const handleImport = () => console.log("Importing customers...");
  const handleRefresh = () => console.log("Refreshing customers...");
  const handleAddCustomer = () => console.log("Adding customer...");

  return (
    <div className="flex gap-2">
      <Button variant="outline" size="sm" onClick={handleRefresh}>
        <RefreshCw className="mr-2 h-4 w-4" />
        Refresh
      </Button>

      <Button variant="outline" size="sm" onClick={handleImport}>
        <Upload className="mr-2 h-4 w-4" />
        Import
      </Button>

      <Button variant="outline" size="sm" onClick={handleExport}>
        <FileDown className="mr-2 h-4 w-4" />
        Export
      </Button>

      <Button size="sm" onClick={handleAddCustomer}>
        <UserPlus className="mr-2 h-4 w-4" />
        Add Customer
      </Button>
    </div>
  );
};
