"use client";

import React, { useState } from "react";
import { ArrowLef<PERSON>, Palette } from "lucide-react";
import { useRouter } from "next/navigation";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { ColorManagerEnhanced } from "@/components/pages/management/ColorManagerEnhanced";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Color } from "@/lib/api/colors";

export default function ColorsPage() {
  const router = useRouter();
  const [colors, setColors] = useState<Color[]>([]);

  const handleColorsChange = (updatedColors: Color[]) => {
    setColors(updatedColors);
  };

  return (
    <>
      <PageHeaderWrapper
        title="Color Management"
        description="Create and manage color options for your products to enhance customer choice and visual appeal"
      >
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/admin/catalog")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Catalog
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5 text-pink-600" />
              Color Management
            </CardTitle>
            <p className="text-sm text-gray-600">
              Create and manage color options for your products to enhance customer choice and visual appeal
            </p>
          </CardHeader>
          <CardContent>
            <ColorManagerEnhanced
              initialColors={colors}
              onColorsChange={handleColorsChange}
            />
          </CardContent>
        </Card>
      </div>
    </>
  );
}
