"use client";

import React, { useState } from "react";

import { Edit, Save, Star, X } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Product } from "@/types/product";

type ProductSectionProps = {
  product: Product;
  isEditing: boolean;
  onProductUpdate: (product: Product) => void;
};

export const ProductInfoSection = ({
  product,
  isEditing,
  onProductUpdate,
}: ProductSectionProps) => {
  const [editedProduct, setEditedProduct] = useState(product);

  const handleInputChange = (field: string, value: string) => {
    setEditedProduct((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const rating = product.rating || 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Product Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Product Name - Only show in edit mode */}
        {isEditing && (
          <div>
            <Label htmlFor="product-name">Product Name</Label>
            <Input
              id="product-name"
              value={editedProduct.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className="mt-1"
            />
          </div>
        )}

        {/* Category and Brand */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <Label htmlFor="category">Category</Label>
            {isEditing ? (
              <Input
                id="category"
                value={editedProduct.category || ""}
                onChange={(e) => handleInputChange("category", e.target.value)}
                className="mt-1"
                placeholder="e.g. Clothing"
              />
            ) : (
              <p className="mt-1 text-gray-600">
                {product.category || "No category"}
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="brand">Brand</Label>
            {isEditing ? (
              <Input
                id="brand"
                value={editedProduct.brand || ""}
                onChange={(e) => handleInputChange("brand", e.target.value)}
                className="mt-1"
                placeholder="e.g. Nike"
              />
            ) : (
              <p className="mt-1 text-gray-600">
                {product.brand || "No brand"}
              </p>
            )}
          </div>
        </div>

        {/* Rating */}
        {!isEditing && rating > 0 && (
          <div>
            <Label>Customer Rating</Label>
            <div className="mt-1 flex items-center gap-2">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < rating
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600">({rating}/5)</span>
            </div>
          </div>
        )}

        {/* Product Type */}
        {!isEditing && product.productType && (
          <div>
            <Label>Product Type</Label>
            <p className="mt-1 capitalize text-gray-600">
              {product.productType}
            </p>
          </div>
        )}

        {/* Model */}
        {!isEditing && product.model && (
          <div>
            <Label>Model</Label>
            <p className="mt-1 text-gray-600">{product.model}</p>
          </div>
        )}

        {/* Year Made */}
        {!isEditing && product.yearMade && (
          <div>
            <Label>Year Made</Label>
            <p className="mt-1 text-gray-600">{product.yearMade}</p>
          </div>
        )}

        {/* Description */}
        <div>
          <Label htmlFor="description">Description</Label>
          {isEditing ? (
            <Textarea
              id="description"
              placeholder="Enter product description..."
              className="mt-1"
              rows={4}
            />
          ) : (
            <p className="mt-1 text-gray-600">
              {product.description || "No description available"}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
