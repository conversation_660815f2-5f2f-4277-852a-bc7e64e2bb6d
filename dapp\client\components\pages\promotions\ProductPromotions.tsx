"use client";

import { useState } from "react";
import { 
  Package, 
  Percent, 
  Edit, 
  Save, 
  X, 
  Tag,
  TrendingUp,
  Search,
  Filter
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { productPromotions } from "@/constants/promotions";

type ProductPromotion = typeof productPromotions[0];

export const ProductPromotions = () => {
  const [products, setProducts] = useState(productPromotions);
  const [editingProduct, setEditingProduct] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");

  // Filter products based on search and status
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = filterStatus === "all" || 
                         (filterStatus === "on-sale" && product.hasActivePromotion) ||
                         (filterStatus === "no-sale" && !product.hasActivePromotion);
    
    return matchesSearch && matchesFilter;
  });

  const updateProductSale = (productId: number, salePercentage: number, isOnSale: boolean) => {
    setProducts(prev => prev.map(product => {
      if (product.id === productId) {
        const originalPrice = parseFloat(product.price.replace('$', ''));
        const newPrice = isOnSale ? originalPrice * (1 - salePercentage / 100) : originalPrice;
        
        return {
          ...product,
          isOnSale,
          price: `$${newPrice.toFixed(2)}`,
          originalPrice: isOnSale ? `$${originalPrice.toFixed(2)}` : undefined,
          bestDiscountPercentage: isOnSale ? salePercentage : 0,
          hasActivePromotion: isOnSale,
        };
      }
      return product;
    }));
    setEditingProduct(null);
  };

  const ProductCard = ({ product }: { product: ProductPromotion }) => {
    const [tempSalePercentage, setTempSalePercentage] = useState(product.bestDiscountPercentage || 0);
    const [tempIsOnSale, setTempIsOnSale] = useState(product.hasActivePromotion);
    const isEditing = editingProduct === product.id.toString();

    const handleSave = () => {
      updateProductSale(product.id, tempSalePercentage, tempIsOnSale);
    };

    const handleCancel = () => {
      setTempSalePercentage(product.bestDiscountPercentage || 0);
      setTempIsOnSale(product.hasActivePromotion);
      setEditingProduct(null);
    };

    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg font-medium">{product.name}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="text-xs">
                  {product.category}
                </Badge>
                {product.hasActivePromotion && (
                  <Badge className="bg-red-100 text-red-800 text-xs">
                    {product.bestDiscountPercentage}% OFF
                  </Badge>
                )}
              </div>
            </div>
            <img 
              src={product.image} 
              alt={product.name}
              className="w-16 h-16 object-cover rounded-lg"
            />
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {/* Price Display */}
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold text-gray-900">{product.price}</span>
              {product.originalPrice && (
                <span className="text-sm text-gray-500 line-through">{product.originalPrice}</span>
              )}
            </div>

            {/* Sale Configuration */}
            {isEditing ? (
              <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">On Sale</span>
                  <Switch
                    checked={tempIsOnSale}
                    onCheckedChange={setTempIsOnSale}
                  />
                </div>
                
                {tempIsOnSale && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Sale Percentage</label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        min="0"
                        max="90"
                        value={tempSalePercentage}
                        onChange={(e) => setTempSalePercentage(Number(e.target.value))}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-500">%</span>
                    </div>
                    
                    {/* Quick percentage buttons */}
                    <div className="flex gap-1">
                      {[10, 20, 30, 50].map(percentage => (
                        <Button
                          key={percentage}
                          variant="outline"
                          size="sm"
                          onClick={() => setTempSalePercentage(percentage)}
                          className="text-xs"
                        >
                          {percentage}%
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleSave} className="flex-1">
                    <Save className="mr-1 h-3 w-3" />
                    Save
                  </Button>
                  <Button size="sm" variant="outline" onClick={handleCancel}>
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Switch
                    checked={product.hasActivePromotion}
                    onCheckedChange={(checked) => {
                      setTempIsOnSale(checked);
                      setEditingProduct(product.id.toString());
                    }}
                  />
                  <span className="text-sm text-gray-600">
                    {product.hasActivePromotion ? "On Sale" : "Regular Price"}
                  </span>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setEditingProduct(product.id.toString())}
                >
                  <Edit className="mr-1 h-3 w-3" />
                  Edit
                </Button>
              </div>
            )}

            {/* Stock Status */}
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Stock: {product.stock}</span>
              <span>Rating: {product.rating}/5</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Product Sales Management</h3>
          <p className="text-sm text-gray-600">Quickly manage individual product sales and discounts</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {filteredProducts.filter(p => p.hasActivePromotion).length} on sale
          </Badge>
          <Badge variant="outline">
            {filteredProducts.length} total products
          </Badge>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search products by name or category..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Products</SelectItem>
              <SelectItem value="on-sale">On Sale</SelectItem>
              <SelectItem value="no-sale">Regular Price</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredProducts.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search or filter criteria.
          </p>
        </div>
      )}
    </div>
  );
};
