"use client";

import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  CreditCard, 
  AlertCircle, 
  RefreshCw,
  CheckCircle,
  Clock
} from "lucide-react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { transactionStats, paymentMethodStats } from "@/constants/transactions";

export const TransactionsStats = () => {
  const stats = [
    {
      title: "Total Revenue",
      value: `€${transactionStats.totalAmount.toFixed(2)}`,
      change: "+12.5%",
      trend: "up",
      icon: DollarSign,
      description: "Total transaction volume",
    },
    {
      title: "Successful Transactions",
      value: transactionStats.successfulTransactions.toString(),
      change: `${((transactionStats.successfulTransactions / transactionStats.totalTransactions) * 100).toFixed(1)}%`,
      trend: "up",
      icon: CheckCircle,
      description: "Successfully processed payments",
    },
    {
      title: "Pending Transactions",
      value: transactionStats.pendingTransactions.toString(),
      change: `${((transactionStats.pendingTransactions / transactionStats.totalTransactions) * 100).toFixed(1)}%`,
      trend: "neutral",
      icon: Clock,
      description: "Awaiting payment confirmation",
    },
    {
      title: "Failed Transactions",
      value: transactionStats.failedTransactions.toString(),
      change: `${((transactionStats.failedTransactions / transactionStats.totalTransactions) * 100).toFixed(1)}%`,
      trend: "down",
      icon: AlertCircle,
      description: "Failed or declined payments",
    },
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <RefreshCw className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return "text-green-600";
      case "down":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-gray-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              <div className="flex items-center gap-1 text-xs">
                {getTrendIcon(stat.trend)}
                <span className={getTrendColor(stat.trend)}>
                  {stat.change}
                </span>
                <span className="text-gray-500">from last month</span>
              </div>
              <p className="mt-1 text-xs text-gray-500">{stat.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Payment Methods & Additional Stats */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Payment Methods Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Methods
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                  <span className="text-sm">Credit/Debit Card</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{paymentMethodStats.card}</div>
                  <div className="text-xs text-gray-500">
                    {((paymentMethodStats.card / transactionStats.totalTransactions) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                  <span className="text-sm">PayPal</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{paymentMethodStats.paypal}</div>
                  <div className="text-xs text-gray-500">
                    {((paymentMethodStats.paypal / transactionStats.totalTransactions) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm">Bank Transfer</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{paymentMethodStats.bankTransfer}</div>
                  <div className="text-xs text-gray-500">
                    {((paymentMethodStats.bankTransfer / transactionStats.totalTransactions) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                  <span className="text-sm">Cash on Delivery</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{paymentMethodStats.cashOnDelivery}</div>
                  <div className="text-xs text-gray-500">
                    {((paymentMethodStats.cashOnDelivery / transactionStats.totalTransactions) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Transaction Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Average Transaction</span>
                <span className="font-medium">€{transactionStats.averageTransactionAmount.toFixed(2)}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Success Rate</span>
                <span className="font-medium text-green-600">
                  {((transactionStats.successfulTransactions / transactionStats.totalTransactions) * 100).toFixed(1)}%
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Refund Rate</span>
                <span className="font-medium text-blue-600">
                  {((transactionStats.refundedTransactions / transactionStats.totalTransactions) * 100).toFixed(1)}%
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Refunded</span>
                <span className="font-medium text-red-600">
                  €{(transactionStats.refundedTransactions * transactionStats.averageTransactionAmount).toFixed(2)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
