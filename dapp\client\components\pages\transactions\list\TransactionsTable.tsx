"use client";

import { format } from "date-fns";
import {
  Banknote,
  CreditCard,
  ExternalLink,
  Eye,
  MoreHorizontal,
  Receipt,
  RefreshCw,
  Smartphone,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { mockCustomers } from "@/constants/products";
import { mockTransactions } from "@/constants/transactions";
import { Transaction } from "@/types/transaction";

const getStatusBadge = (status: string) => {
  const statusConfig = {
    paid: {
      variant: "default" as const,
      className: "bg-green-100 text-green-800 hover:bg-green-100",
    },
    pending: {
      variant: "secondary" as const,
      className: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100",
    },
    failed: {
      variant: "destructive" as const,
      className: "bg-red-100 text-red-800 hover:bg-red-100",
    },
    cancelled: {
      variant: "outline" as const,
      className: "bg-gray-100 text-gray-800 hover:bg-gray-100",
    },
    refunded: {
      variant: "secondary" as const,
      className: "bg-blue-100 text-blue-800 hover:bg-blue-100",
    },
  };

  const config =
    statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

  return (
    <Badge variant={config.variant} className={config.className}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

const getPaymentMethodIcon = (method: string) => {
  switch (method) {
    case "card":
      return <CreditCard className="h-4 w-4" />;
    case "paypal":
      return <Smartphone className="h-4 w-4" />;
    case "bank-transfer":
      return <Banknote className="h-4 w-4" />;
    case "cash-on-delivery":
      return <Receipt className="h-4 w-4" />;
    default:
      return <CreditCard className="h-4 w-4" />;
  }
};

const getPaymentMethodLabel = (method: string) => {
  const labels = {
    card: "Credit/Debit Card",
    paypal: "PayPal",
    "bank-transfer": "Bank Transfer",
    "cash-on-delivery": "Cash on Delivery",
  };
  return labels[method as keyof typeof labels] || method;
};

export const TransactionsTable = () => {
  const getCustomerName = (userId: string) => {
    const customer = mockCustomers.find((c) => c.id === userId);
    return customer?.name || "Unknown Customer";
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-white">
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b bg-gray-50/50 text-gray-500">
              <th className="p-4 text-left font-medium">Transaction</th>
              <th className="p-4 text-left font-medium">Customer</th>
              <th className="p-4 text-left font-medium">Order</th>
              <th className="p-4 text-center font-medium">Status</th>
              <th className="p-4 text-center font-medium">Method</th>
              <th className="p-4 text-right font-medium">Amount</th>
              <th className="p-4 text-center font-medium">Date</th>
              <th className="p-4 text-center font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {mockTransactions.map((transaction) => (
              <tr key={transaction.id} className="border-b hover:bg-gray-50/50">
                {/* Transaction ID */}
                <td className="p-4">
                  <div className="flex flex-col">
                    <a
                      href={`/admin/transactions/${transaction.id}`}
                      className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      {transaction.id}
                    </a>
                    {transaction.providerTransactionId && (
                      <span className="text-xs text-gray-500">
                        {transaction.providerTransactionId.slice(0, 20)}...
                      </span>
                    )}
                  </div>
                </td>

                {/* Customer */}
                <td className="p-4">
                  <div className="flex flex-col">
                    <span className="font-medium text-gray-900">
                      {getCustomerName(transaction.userId)}
                    </span>
                    <span className="text-xs text-gray-500">
                      {transaction.userId}
                    </span>
                  </div>
                </td>

                {/* Order */}
                <td className="p-4">
                  <Button
                    variant="link"
                    className="h-auto p-0 text-blue-600 hover:text-blue-800"
                  >
                    {transaction.orderId}
                  </Button>
                </td>

                {/* Status */}
                <td className="p-4 text-center">
                  {getStatusBadge(transaction.status)}
                </td>

                {/* Payment Method */}
                <td className="p-4 text-center">
                  <div className="flex items-center justify-center gap-2">
                    {getPaymentMethodIcon(transaction.method)}
                    <span className="hidden sm:inline">
                      {getPaymentMethodLabel(transaction.method)}
                    </span>
                  </div>
                </td>

                {/* Amount */}
                <td className="p-4 text-right">
                  <div className="flex flex-col items-end">
                    <span className="font-medium text-gray-900">
                      €{transaction.amount.toFixed(2)}
                    </span>
                    {transaction.refundedAmount && (
                      <span className="text-xs text-red-600">
                        Refunded: €{transaction.refundedAmount.toFixed(2)}
                      </span>
                    )}
                  </div>
                </td>

                {/* Date */}
                <td className="p-4 text-center">
                  <div className="flex flex-col">
                    <span className="text-gray-900">
                      {format(new Date(transaction.createdAt), "MMM dd, yyyy")}
                    </span>
                    <span className="text-xs text-gray-500">
                      {format(new Date(transaction.createdAt), "HH:mm")}
                    </span>
                  </div>
                </td>

                {/* Actions */}
                <td className="p-4 text-center">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <a href={`/admin/transactions/${transaction.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </a>
                      </DropdownMenuItem>
                      {transaction.receiptUrl && (
                        <DropdownMenuItem>
                          <Receipt className="mr-2 h-4 w-4" />
                          View Receipt
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        View Order
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {transaction.status === "paid" && (
                        <DropdownMenuItem className="text-red-600">
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Refund Transaction
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
