import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { TransactionDetailsWrapper } from "@/components/pages/transactions/details/TransactionDetailsWrapper";

type Props = {
  params: {
    id: string;
  };
};

export default function TransactionDetailsPage({ params }: Props) {
  return (
    <>
      <PageHeaderWrapper
        title={`Transaction ${params.id}`}
        description="View detailed transaction information and manage payment processing"
      />

      <div className="container mx-auto mt-6">
        <TransactionDetailsWrapper transactionId={params.id} />
      </div>
    </>
  );
}
