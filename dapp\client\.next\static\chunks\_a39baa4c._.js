(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_a39baa4c._.js", {

"[project]/lib/api/products.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * API functions for product operations
 * Handles all HTTP requests to the backend product endpoints
 */ // Types for API responses
__turbopack_context__.s({
    "checkApiHealth": (()=>checkApiHealth),
    "createProduct": (()=>createProduct),
    "deleteProduct": (()=>deleteProduct),
    "getProductById": (()=>getProductById),
    "getProductForEdit": (()=>getProductForEdit),
    "getProducts": (()=>getProducts),
    "updateProduct": (()=>updateProduct),
    "updateProductSafe": (()=>updateProductSafe)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
// Get API base URL from environment or default to localhost
const getApiBaseUrl = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        // Client-side: use current origin or environment variable
        return ("TURBOPACK compile-time value", "http://localhost:3010") || `${window.location.protocol}//${window.location.hostname}:3001`;
    }
    "TURBOPACK unreachable";
};
const API_BASE_URL = getApiBaseUrl();
/**
 * Generic fetch wrapper with error handling
 */ async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}/api${endpoint}`;
    const defaultOptions = {
        headers: {
            "Content-Type": "application/json",
            ...options.headers
        },
        ...options
    };
    console.log(`🌐 Making API request to: ${url}`);
    console.log(`🌐 Request options:`, defaultOptions);
    try {
        const response = await fetch(url, defaultOptions);
        console.log(`🌐 Response status: ${response.status}`);
        console.log(`🌐 Response ok: ${response.ok}`);
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`🌐 Response error text:`, errorText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }
        const data = await response.json();
        console.log(`🌐 Response data:`, data);
        return data;
    } catch (error) {
        console.error(`🌐 API request failed for ${endpoint}:`, error);
        throw error;
    }
}
async function getProducts(filters = {}) {
    const searchParams = new URLSearchParams();
    // Add filters to search params
    Object.entries(filters).forEach(([key, value])=>{
        if (value !== undefined && value !== null && value !== "") {
            if (Array.isArray(value)) {
                value.forEach((item)=>searchParams.append(key, item.toString()));
            } else {
                searchParams.append(key, value.toString());
            }
        }
    });
    const queryString = searchParams.toString();
    const endpoint = queryString ? `/products?${queryString}` : "/products";
    return apiRequest(endpoint);
}
async function getProductById(id) {
    return apiRequest(`/products/${id}`);
}
async function getProductForEdit(id) {
    return apiRequest(`/products/${id}/edit`);
}
async function createProduct(productData) {
    console.log("🌐 API createProduct called with data:", productData);
    console.log("🌐 API Base URL:", API_BASE_URL);
    try {
        const result = await apiRequest("/products", {
            method: "POST",
            body: JSON.stringify(productData)
        });
        console.log("🌐 API createProduct response:", result);
        return result;
    } catch (error) {
        console.error("🌐 API createProduct error:", error);
        throw error;
    }
}
async function updateProduct(id, productData) {
    return apiRequest(`/products/${id}`, {
        method: "PUT",
        body: JSON.stringify(productData)
    });
}
async function updateProductSafe(id, productData) {
    return apiRequest(`/products/${id}/edit`, {
        method: "PATCH",
        body: JSON.stringify(productData)
    });
}
async function deleteProduct(id) {
    return apiRequest(`/products/${id}`, {
        method: "DELETE"
    });
}
async function checkApiHealth() {
    try {
        const response = await fetch(`${API_BASE_URL}/api`);
        return response.ok;
    } catch (error) {
        console.error("API health check failed:", error);
        return false;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/api/categoryApi.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryApiService": (()=>CategoryApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:3010") || "http://localhost:3010/api";
class CategoryApiService {
    static baseUrl = `${API_BASE_URL}/api/categories`;
    /**
   * Transform backend category to frontend format
   */ static transformCategory(backendCategory) {
        console.log("Transforming category:", backendCategory); // Debug log
        const transformed = {
            id: backendCategory._id || backendCategory.id,
            name: backendCategory.name,
            description: backendCategory.description,
            slug: backendCategory.slug,
            icon: backendCategory.icon,
            color: backendCategory.color,
            isActive: backendCategory.isActive,
            productCount: backendCategory.productCount,
            parentId: backendCategory.parentId,
            sortOrder: backendCategory.sortOrder,
            createdAt: backendCategory.createdAt,
            updatedAt: backendCategory.updatedAt
        };
        console.log("Transformed category:", transformed); // Debug log
        return transformed;
    }
    /**
   * Get all categories with optional filtering
   */ static async getCategories(filters) {
        try {
            const params = new URLSearchParams();
            if (filters?.isActive !== undefined) {
                params.append("isActive", filters.isActive.toString());
            }
            if (filters?.parentId) {
                params.append("parentId", filters.parentId);
            }
            if (filters?.search) {
                params.append("search", filters.search);
            }
            const url = `${this.baseUrl}${params.toString() ? `?${params.toString()}` : ""}`;
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || "Failed to fetch categories");
            }
            const categories = (result.data || []).map(this.transformCategory);
            return categories;
        } catch (error) {
            console.error("Error fetching categories:", error);
            throw error;
        }
    }
    /**
   * Get a category by ID
   */ static async getCategoryById(id) {
        try {
            const response = await fetch(`${this.baseUrl}/${id}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || "Failed to fetch category");
            }
            if (!result.data) {
                throw new Error("Category not found");
            }
            return this.transformCategory(result.data);
        } catch (error) {
            console.error("Error fetching category:", error);
            throw error;
        }
    }
    /**
   * Get a category by slug
   */ static async getCategoryBySlug(slug) {
        try {
            const response = await fetch(`${this.baseUrl}/slug/${slug}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || "Failed to fetch category");
            }
            if (!result.data) {
                throw new Error("Category not found");
            }
            return this.transformCategory(result.data);
        } catch (error) {
            console.error("Error fetching category:", error);
            throw error;
        }
    }
    /**
   * Create a new category
   */ static async createCategory(categoryData) {
        try {
            const response = await fetch(this.baseUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(categoryData)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || "Failed to create category");
            }
            if (!result.data) {
                throw new Error("No category data returned");
            }
            return this.transformCategory(result.data);
        } catch (error) {
            console.error("Error creating category:", error);
            throw error;
        }
    }
    /**
   * Update a category
   */ static async updateCategory(id, updateData) {
        try {
            const response = await fetch(`${this.baseUrl}/${id}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(updateData)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || "Failed to update category");
            }
            if (!result.data) {
                throw new Error("No category data returned");
            }
            return this.transformCategory(result.data);
        } catch (error) {
            console.error("Error updating category:", error);
            throw error;
        }
    }
    /**
   * Delete a category
   */ static async deleteCategory(id) {
        try {
            console.log("Deleting category with ID:", id); // Debug log
            if (!id || id === "undefined") {
                throw new Error("Category ID is required for deletion");
            }
            const response = await fetch(`${this.baseUrl}/${id}`, {
                method: "DELETE"
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || "Failed to delete category");
            }
        } catch (error) {
            console.error("Error deleting category:", error);
            throw error;
        }
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/uploadthing/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteImageFromServer": (()=>deleteImageFromServer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
const deleteImageFromServer = async (fileKey)=>{
    try {
        const response = await fetch(`/api/uploadthing/delete`, {
            method: "DELETE",
            body: JSON.stringify({
                fileKey
            }),
            headers: {
                "Content-Type": "application/json"
            }
        });
        if (!response.ok) {
            throw new Error("Failed to delete image");
        }
        return true;
    } catch (error) {
        console.error("Error deleting image:", error);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to delete image from server");
        return false;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/hooks/useProducts.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * React hooks for product data management
 * Provides easy-to-use hooks for CRUD operations on products
 */ __turbopack_context__.s({
    "useProduct": (()=>useProduct),
    "useProductForEdit": (()=>useProductForEdit),
    "useProductMutations": (()=>useProductMutations),
    "useProducts": (()=>useProducts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/products.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
;
function useProducts(initialFilters = {}) {
    _s();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        products: [],
        loading: true,
        error: null
    });
    const [filters, setFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialFilters);
    const fetchProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProducts.useCallback[fetchProducts]": async (newFilters)=>{
            setState({
                "useProducts.useCallback[fetchProducts]": (prev)=>({
                        ...prev,
                        loading: true,
                        error: null
                    })
            }["useProducts.useCallback[fetchProducts]"]);
            try {
                const filtersToUse = newFilters || filters;
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProducts"])(filtersToUse);
                setState({
                    products: response.data,
                    loading: false,
                    error: null,
                    meta: response.meta
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch products';
                setState({
                    "useProducts.useCallback[fetchProducts]": (prev)=>({
                            ...prev,
                            loading: false,
                            error: errorMessage
                        })
                }["useProducts.useCallback[fetchProducts]"]);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to load products');
            }
        }
    }["useProducts.useCallback[fetchProducts]"], [
        filters
    ]);
    const updateFilters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProducts.useCallback[updateFilters]": (newFilters)=>{
            setFilters(newFilters);
            fetchProducts(newFilters);
        }
    }["useProducts.useCallback[updateFilters]"], [
        fetchProducts
    ]);
    const refreshProducts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProducts.useCallback[refreshProducts]": ()=>{
            fetchProducts();
        }
    }["useProducts.useCallback[refreshProducts]"], [
        fetchProducts
    ]);
    // Initial fetch
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useProducts.useEffect": ()=>{
            fetchProducts();
        }
    }["useProducts.useEffect"], [
        fetchProducts
    ]);
    return {
        ...state,
        filters,
        updateFilters,
        refreshProducts,
        refetch: fetchProducts
    };
}
_s(useProducts, "nU+3kHSdxylUKn0ktt99NUr4ZpM=");
function useProduct(id) {
    _s1();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        product: null,
        loading: true,
        error: null
    });
    const fetchProduct = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProduct.useCallback[fetchProduct]": async ()=>{
            if (!id) {
                setState({
                    product: null,
                    loading: false,
                    error: null
                });
                return;
            }
            setState({
                "useProduct.useCallback[fetchProduct]": (prev)=>({
                        ...prev,
                        loading: true,
                        error: null
                    })
            }["useProduct.useCallback[fetchProduct]"]);
            try {
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProductById"])(id);
                setState({
                    product: response.data,
                    loading: false,
                    error: null
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product';
                setState({
                    "useProduct.useCallback[fetchProduct]": (prev)=>({
                            ...prev,
                            loading: false,
                            error: errorMessage
                        })
                }["useProduct.useCallback[fetchProduct]"]);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to load product');
            }
        }
    }["useProduct.useCallback[fetchProduct]"], [
        id
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useProduct.useEffect": ()=>{
            fetchProduct();
        }
    }["useProduct.useEffect"], [
        fetchProduct
    ]);
    return {
        ...state,
        refetch: fetchProduct
    };
}
_s1(useProduct, "S2OtAc4zVf7leQl+bgDyTf0MZzQ=");
function useProductMutations() {
    _s2();
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const createProductMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProductMutations.useCallback[createProductMutation]": async (productData)=>{
            setLoading(true);
            try {
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createProduct"])(productData);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Product created successfully');
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to create product';
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
                throw error;
            } finally{
                setLoading(false);
            }
        }
    }["useProductMutations.useCallback[createProductMutation]"], []);
    const updateProductMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProductMutations.useCallback[updateProductMutation]": async (id, productData)=>{
            setLoading(true);
            try {
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateProduct"])(id, productData);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Product updated successfully');
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to update product';
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
                throw error;
            } finally{
                setLoading(false);
            }
        }
    }["useProductMutations.useCallback[updateProductMutation]"], []);
    const updateProductSafeMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProductMutations.useCallback[updateProductSafeMutation]": async (id, productData)=>{
            setLoading(true);
            try {
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateProductSafe"])(id, productData);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Product updated successfully');
                return response.data;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to update product';
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
                throw error;
            } finally{
                setLoading(false);
            }
        }
    }["useProductMutations.useCallback[updateProductSafeMutation]"], []);
    const deleteProductMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProductMutations.useCallback[deleteProductMutation]": async (id)=>{
            setLoading(true);
            try {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteProduct"])(id);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Product deleted successfully');
                return true;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete product';
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
                throw error;
            } finally{
                setLoading(false);
            }
        }
    }["useProductMutations.useCallback[deleteProductMutation]"], []);
    return {
        loading,
        createProduct: createProductMutation,
        updateProduct: updateProductMutation,
        updateProductSafe: updateProductSafeMutation,
        deleteProduct: deleteProductMutation
    };
}
_s2(useProductMutations, "xFTPUanuAbfQ3pKezpBQlMDprLM=");
function useProductForEdit(id) {
    _s3();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        product: null,
        loading: true,
        error: null,
        editableFields: []
    });
    const fetchProductForEdit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProductForEdit.useCallback[fetchProductForEdit]": async ()=>{
            if (!id) {
                setState({
                    product: null,
                    loading: false,
                    error: null,
                    editableFields: []
                });
                return;
            }
            setState({
                "useProductForEdit.useCallback[fetchProductForEdit]": (prev)=>({
                        ...prev,
                        loading: true,
                        error: null
                    })
            }["useProductForEdit.useCallback[fetchProductForEdit]"]);
            try {
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$products$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProductForEdit"])(id);
                setState({
                    product: response.data,
                    loading: false,
                    error: null,
                    editableFields: response.editableFields || []
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product for editing';
                setState({
                    "useProductForEdit.useCallback[fetchProductForEdit]": (prev)=>({
                            ...prev,
                            loading: false,
                            error: errorMessage
                        })
                }["useProductForEdit.useCallback[fetchProductForEdit]"]);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to load product for editing');
            }
        }
    }["useProductForEdit.useCallback[fetchProductForEdit]"], [
        id
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useProductForEdit.useEffect": ()=>{
            fetchProductForEdit();
        }
    }["useProductForEdit.useEffect"], [
        fetchProductForEdit
    ]);
    return {
        ...state,
        refetch: fetchProductForEdit
    };
}
_s3(useProductForEdit, "vqZ19lmhd5MuRGKOQXvdiigSamU=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/hooks/useProductFormWithSections.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useProductFormWithSections": (()=>useProductFormWithSections)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$hookform$2b$resolvers$40$5$2e$0$2e$1_r_0b0cec1155f943d447f0b7c275a9fd8d$2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@hookform+resolvers@5.0.1_r_0b0cec1155f943d447f0b7c275a9fd8d/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hook$2d$form$40$7$2e$56$2e$1_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-hook-form@7.56.1_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$useProducts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/hooks/useProducts.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$schemas$2f$productSchema$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/schemas/productSchema.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
function useProductFormWithSections() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { createProduct, loading: creating } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$useProducts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductMutations"])();
    // Section navigation state
    const [currentSection, setCurrentSection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("basic-info");
    const [completedSections, setCompletedSections] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Form data state
    const [tags, setTags] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [images, setImages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [imageAltTexts, setImageAltTexts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Initialize react-hook-form
    const { register, handleSubmit, setValue, watch, getValues, formState: { errors, isValid }, trigger } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hook$2d$form$40$7$2e$56$2e$1_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$hookform$2b$resolvers$40$5$2e$0$2e$1_r_0b0cec1155f943d447f0b7c275a9fd8d$2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$schemas$2f$productSchema$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productSchema"]),
        defaultValues: {
            currency: "EUR",
            condition: "new",
            stock: 1,
            isPublished: true,
            status: "draft",
            ageRestriction: "none",
            freeShipping: false,
            productType: "physical",
            visibility: "public",
            taxStatus: "taxable",
            stockManagement: "track",
            trackQuantity: true,
            backorderAllowed: false,
            soldIndividually: false,
            requiresShipping: true,
            separateShipping: false,
            shippingClass: "standard",
            shippingTime: "3-5-business-days",
            featured: false,
            sticky: false,
            downloadable: false,
            virtual: false,
            costPrice: 0
        },
        mode: "onChange"
    });
    // Define the order of form sections
    const SECTIONS_ORDER = [
        "basic-info",
        "pricing",
        "inventory",
        "details",
        "media",
        "shipping",
        "availability",
        "seo",
        "warranty",
        "advanced"
    ];
    // Calculate progress percentage
    const progress = Math.round(completedSections.length / SECTIONS_ORDER.length * 100);
    /**
   * Validate the current section and navigate to the next if valid
   */ const validateAndNavigate = async (nextSection)=>{
        console.log("🔍 validateAndNavigate called");
        console.log("🔍 Current section:", currentSection);
        console.log("🔍 Next section:", nextSection);
        let fieldsToValidate = [];
        // Determine which fields to validate based on current section
        switch(currentSection){
            case "basic-info":
                fieldsToValidate = [
                    "name",
                    "brand",
                    "description",
                    "productType"
                ];
                break;
            case "pricing":
                fieldsToValidate = [
                    "price",
                    "currency",
                    "taxStatus",
                    "costPrice"
                ];
                break;
            case "inventory":
                fieldsToValidate = [
                    "stock",
                    "condition",
                    "stockManagement"
                ];
                break;
            case "details":
                fieldsToValidate = [
                    "category",
                    "material",
                    "tags",
                    "color"
                ];
                break;
            case "media":
                fieldsToValidate = [
                    "mainImage"
                ];
                break;
            case "shipping":
                // Only validate shipping fields if shipping is required
                const requiresShipping = getValues("requiresShipping");
                if (requiresShipping) {
                    const freeShipping = getValues("freeShipping");
                    // Always require shipping class and shipping time
                    // Only require shipping cost if not free shipping
                    fieldsToValidate = freeShipping ? [
                        "shippingClass",
                        "shippingTime"
                    ] : [
                        "shippingClass",
                        "shippingCost",
                        "shippingTime"
                    ];
                } else {
                    fieldsToValidate = [];
                }
                break;
            case "availability":
                fieldsToValidate = [
                    "status"
                ];
                break;
            // SEO, warranty, and advanced are optional sections
            case "seo":
            case "warranty":
            case "advanced":
                fieldsToValidate = []; // No required fields for optional sections
                break;
        }
        console.log("🔍 Fields to validate:", fieldsToValidate);
        // Trigger validation for the specified fields
        let isValid = await trigger(fieldsToValidate);
        // Additional validation for media section (alt texts)
        if (currentSection === "media" && isValid) {
            if (images.length > 0) {
                const missingAltTexts = images.some((_, index)=>!imageAltTexts[index] || imageAltTexts[index].trim() === "");
                if (missingAltTexts) {
                    isValid = false;
                }
            }
        }
        console.log("🔍 Validation result:", isValid);
        if (isValid) {
            console.log("🔍 Validation passed, navigating to next section");
            // Add current section to completed sections if not already there
            if (!completedSections.includes(currentSection)) {
                console.log("🔍 Adding current section to completed:", currentSection);
                setCompletedSections([
                    ...completedSections,
                    currentSection
                ]);
            }
            // Navigate to next section
            console.log("🔍 Setting current section to:", nextSection);
            setCurrentSection(nextSection);
        } else {
            console.log("🔍 Validation failed, showing error message");
            // Show specific error messages for each section
            switch(currentSection){
                case "basic-info":
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please complete all required basic information: Product Name, Brand, Description, and Product Type");
                    break;
                case "pricing":
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please complete all required pricing fields: Regular Price, Currency, Tax Status, and Cost Price for business intelligence");
                    break;
                case "inventory":
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please complete all required inventory fields: Stock Quantity, Condition, and Stock Management");
                    break;
                case "details":
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please complete all required details: Category, Material, Product Tags, and Color are required");
                    break;
                case "media":
                    if (images.length === 0) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please upload at least one product image");
                    } else {
                        const missingAltTexts = images.some((_, index)=>!imageAltTexts[index] || imageAltTexts[index].trim() === "");
                        if (missingAltTexts) {
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please add alt text for all images. Alt text is required for accessibility and SEO.");
                        } else {
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please complete all required media fields");
                        }
                    }
                    break;
                case "shipping":
                    const requiresShippingForError = getValues("requiresShipping");
                    if (requiresShippingForError) {
                        const freeShippingForError = getValues("freeShipping");
                        if (freeShippingForError) {
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please complete all required shipping information: Shipping Class and Estimated Shipping Time are required.");
                        } else {
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please complete all required shipping information: Shipping Class, Shipping Cost, and Estimated Shipping Time are required.");
                        }
                    }
                    break;
                case "availability":
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please complete all required availability: Product Status is required");
                    break;
                default:
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please fill in all required fields correctly");
                    break;
            }
        }
    };
    /**
   * Handle next button click
   */ const handleNext = async ()=>{
        console.log("🔍 handleNext called");
        console.log("🔍 Current section:", currentSection);
        const currentIndex = SECTIONS_ORDER.indexOf(currentSection);
        console.log("🔍 Current index:", currentIndex);
        console.log("🔍 Total sections:", SECTIONS_ORDER.length);
        console.log("🔍 Is last section check:", currentIndex >= SECTIONS_ORDER.length - 1);
        if (currentIndex < SECTIONS_ORDER.length - 1) {
            const nextSection = SECTIONS_ORDER[currentIndex + 1];
            console.log("🔍 Next section:", nextSection);
            console.log("🔍 Calling validateAndNavigate...");
            validateAndNavigate(nextSection);
        } else {
            console.log("🔍 Already on last section, cannot navigate further");
        }
    };
    /**
   * Handle previous button click
   */ const handlePrevious = ()=>{
        const currentIndex = SECTIONS_ORDER.indexOf(currentSection);
        if (currentIndex > 0) {
            setCurrentSection(SECTIONS_ORDER[currentIndex - 1]);
        }
    };
    /**
   * Handle section selection from navigator
   */ const handleSectionChange = (sectionId)=>{
        // Only allow navigation to completed sections or the next section
        if (completedSections.includes(sectionId) || sectionId === currentSection || SECTIONS_ORDER.indexOf(sectionId) === completedSections.length) {
            setCurrentSection(sectionId);
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info("Please complete the current section first");
        }
    };
    /**
   * Validate all sections before submission
   */ const validateAllSections = async ()=>{
        console.log("🔍 Validating all sections...");
        // Trigger validation for all fields
        const isFormValid = await trigger();
        if (!isFormValid) {
            console.error("❌ Form validation failed");
            const errorFields = Object.keys(errors);
            console.error("Fields with errors:", errorFields);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(`Please fix errors in: ${errorFields.join(", ")}`);
            return false;
        }
        return true;
    };
    /**
   * Handle form submission
   */ const onSubmit = async (data)=>{
        console.log("🚀 Form submission started...");
        console.log("📍 Current section:", currentSection);
        console.log("✅ Completed sections:", completedSections);
        console.log("📊 Progress:", progress + "%");
        // Debug: Check what watch() returns for price
        const watchedPrice = watch("price");
        console.log("👀 Watched price value:", watchedPrice, "Type:", typeof watchedPrice);
        console.log("Form data received:", data);
        console.log("Tags:", tags);
        console.log("Images:", images);
        console.log("Creating state:", creating);
        console.log("Form errors:", errors);
        console.log("Form is valid:", isValid);
        try {
            // FIRST: Fix missing price field issue before validation
            if (!data.price) {
                const watchedPrice = watch("price");
                console.log("🔧 Fixing missing price field. Watched value:", watchedPrice);
                if (watchedPrice !== undefined && !isNaN(watchedPrice)) {
                    console.log("✅ Applied price fallback:", watchedPrice);
                    data.price = watchedPrice;
                } else {
                    console.error("❌ Cannot fix price field - watched value is invalid:", watchedPrice);
                }
            }
            // Validate all sections before submission
            const isAllValid = await validateAllSections();
            if (!isAllValid) {
                return null;
            }
            // Validate that we have minimum required data
            if (!data.name || !data.price) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Product name and price are required");
                console.error("❌ Missing required fields:", {
                    name: data.name,
                    price: data.price
                });
                return null;
            }
            // Validate required fields based on current form state
            if (!data.brand) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Brand is required");
                return null;
            }
            if (!data.description || data.description.length < 10) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Description must be at least 10 characters long");
                return null;
            }
            if (!data.category) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Category is required");
                return null;
            }
            if (!data.material) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Material is required");
                return null;
            }
            if (!data.tags || data.tags.length === 0) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("At least one product tag is required");
                return null;
            }
            if (!data.color) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Color is required");
                return null;
            }
            if (!data.mainImage && images.length === 0) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("At least one product image is required");
                return null;
            }
            // Validate that all images have alt text
            if (images.length > 0) {
                const missingAltTexts = images.some((_, index)=>!imageAltTexts[index] || imageAltTexts[index].trim() === "");
                if (missingAltTexts) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("All images must have alt text for accessibility and SEO");
                    return null;
                }
            }
            // Clean up form data - convert empty strings to undefined for optional fields
            const cleanedData = {
                ...data
            };
            // Debug: Log the original data to see what we're working with
            console.log("🔍 Original form data:", data);
            console.log("🔍 All form data keys:", Object.keys(data));
            console.log("🔍 Price field exists in data:", "price" in data);
            console.log("🔍 Price field type:", typeof data.price, "Value:", data.price);
            console.log("🔍 Name field:", data.name);
            // Check if price is undefined, null, or NaN
            if (data.price === undefined) {
                console.error("❌ Price is undefined in form data!");
                // Try to get price from watch() as fallback
                const watchedPrice = watch("price");
                console.log("🔄 Trying fallback - watched price:", watchedPrice);
                if (watchedPrice !== undefined && !isNaN(watchedPrice)) {
                    console.log("✅ Using watched price as fallback");
                    cleanedData.price = watchedPrice;
                } else {
                    console.error("❌ Watched price is also invalid:", watchedPrice);
                }
            } else if (data.price === null) {
                console.error("❌ Price is null!");
            } else if (isNaN(data.price)) {
                console.error("❌ Price is NaN!");
            } else {
                console.log("✅ Price is valid:", data.price);
            }
            // Remove empty SKU to avoid duplicate key issues
            if (cleanedData.sku === "" || cleanedData.sku === null || cleanedData.sku === undefined) {
                delete cleanedData.sku;
            }
            // Handle required number fields (convert strings to numbers, but don't delete if invalid)
            const requiredNumberFields = [
                "price",
                "stock"
            ];
            requiredNumberFields.forEach((field)=>{
                if (cleanedData[field] !== undefined && typeof cleanedData[field] === "string") {
                    const numValue = parseFloat(cleanedData[field]);
                    if (!isNaN(numValue)) {
                        cleanedData[field] = numValue;
                    }
                // Don't delete required fields even if conversion fails
                }
            });
            // Convert empty strings to undefined for optional number fields
            const optionalNumberFields = [
                "lowStockThreshold",
                "yearMade",
                "shippingCost",
                "costPrice",
                "originalPrice",
                "minimumOrderQuantity",
                "maximumOrderQuantity",
                "salePrice"
            ];
            optionalNumberFields.forEach((field)=>{
                if (cleanedData[field] === "" || cleanedData[field] === null) {
                    delete cleanedData[field];
                } else if (cleanedData[field] !== undefined && typeof cleanedData[field] === "string") {
                    // Convert string numbers to actual numbers
                    const numValue = parseFloat(cleanedData[field]);
                    if (!isNaN(numValue)) {
                        cleanedData[field] = numValue;
                    } else {
                        delete cleanedData[field];
                    }
                }
            });
            // Convert empty strings to undefined for optional URL fields
            const urlFields = [
                "videoUrl",
                "threeDModelUrl",
                "canonicalUrl"
            ];
            urlFields.forEach((field)=>{
                if (cleanedData[field] === "" || cleanedData[field] === null) {
                    delete cleanedData[field];
                }
            });
            // Convert empty strings to undefined for optional string fields
            const optionalStringFields = [
                "model",
                "shortDescription",
                "barcode",
                "subcategory",
                "color",
                "size",
                "location",
                "shippingTime",
                "taxClass",
                "metaTitle",
                "metaDescription",
                "focusKeyword",
                "slug"
            ];
            optionalStringFields.forEach((field)=>{
                if (cleanedData[field] === "" || cleanedData[field] === null) {
                    delete cleanedData[field];
                }
            });
            // Convert empty strings to undefined for optional date fields
            const dateFields = [
                "saleEndsAt",
                "availableFrom",
                "availableUntil"
            ];
            dateFields.forEach((field)=>{
                if (cleanedData[field] === "" || cleanedData[field] === null) {
                    delete cleanedData[field];
                }
            });
            // Debug: Log cleaned data before final preparation
            console.log("🧹 Cleaned data:", cleanedData);
            console.log("🧹 Cleaned price field type:", typeof cleanedData.price, "Value:", cleanedData.price);
            // Prepare the final data with images and tags
            const mainImageUrl = images.length > 0 ? images[0].url : data.mainImage || "";
            const additionalImages = images.length > 1 ? images.slice(1).map((img)=>img.url) : [];
            const finalProductData = {
                ...cleanedData,
                tags: tags || [],
                images: additionalImages,
                mainImage: mainImageUrl,
                imageAltTexts: imageAltTexts || []
            };
            console.log("📦 Final product data to be sent:", finalProductData);
            console.log("📦 Final price field type:", typeof finalProductData.price, "Value:", finalProductData.price);
            // Show loading toast
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].loading("Creating product...", {
                id: "create-product"
            });
            // Create product via API
            console.log("📡 Calling API...");
            const createdProduct = await createProduct(finalProductData);
            // Dismiss loading toast
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].dismiss("create-product");
            // Display the created product in the console
            console.log("✅ PRODUCT CREATED SUCCESSFULLY!");
            console.log("Product Details:", createdProduct);
            // Show success message
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Product created successfully!");
            // Navigate to the created product's detail page
            if (createdProduct && (createdProduct._id || createdProduct.id)) {
                const productId = createdProduct._id || createdProduct.id;
                console.log(`🔗 Navigating to product: ${productId}`);
                router.push(`/admin/products/${productId}`);
            } else {
                // Fallback to products list
                console.log("🔗 Navigating to products list");
                router.push("/admin/products/list");
            }
            return createdProduct;
        } catch (err) {
            // Dismiss loading toast
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].dismiss("create-product");
            console.error("❌ Failed to create product:", err);
            // Show user-friendly error messages
            if (err instanceof Error) {
                let errorMessage = err.message;
                // Handle specific error types
                if (errorMessage.includes("Duplicate key error")) {
                    errorMessage = "A product with this SKU already exists. Please use a different SKU or leave it empty.";
                } else if (errorMessage.includes("ValidationError")) {
                    errorMessage = "Please check all required fields and try again.";
                } else if (errorMessage.includes("HTTP error! status: 400")) {
                    // Extract the actual error message from the API response
                    const match = errorMessage.match(/message: (.+)/);
                    if (match) {
                        const apiError = match[1];
                        if (apiError.includes("Duplicate key error")) {
                            errorMessage = "A product with this SKU already exists. Please use a different SKU or leave it empty.";
                        } else {
                            errorMessage = `Error: ${apiError}`;
                        }
                    }
                }
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
                console.error("Error details:", err);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to create product: Unknown error");
                console.error("Unknown error:", err);
            }
            return null;
        }
    };
    return {
        // Form state
        register,
        handleSubmit,
        setValue,
        watch,
        errors,
        isValid,
        // Section navigation
        currentSection,
        completedSections,
        SECTIONS_ORDER,
        progress,
        handleNext,
        handlePrevious,
        handleSectionChange,
        // Additional state
        tags,
        setTags,
        images,
        setImages,
        imageAltTexts,
        setImageAltTexts,
        // Form submission
        onSubmit,
        creating,
        validateAllSections
    };
}
_s(useProductFormWithSections, "p78DA/ZfwY/YqAQ7Mhlv1igssK8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$useProducts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductMutations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hook$2d$form$40$7$2e$56$2e$1_react$40$19$2e$1$2e$0$2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/hooks/useCategories.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * React hooks for category data management
 * Provides easy-to-use hooks for CRUD operations on categories
 */ __turbopack_context__.s({
    "useCategories": (()=>useCategories),
    "useCategory": (()=>useCategory),
    "useCategoryMutations": (()=>useCategoryMutations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@2.0.3_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$categoryApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/categoryApi.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
;
;
;
function useCategories(initialFilters = {}) {
    _s();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        categories: [],
        loading: true,
        error: null
    });
    const [filters, setFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialFilters);
    const fetchCategories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCategories.useCallback[fetchCategories]": async (newFilters)=>{
            setState({
                "useCategories.useCallback[fetchCategories]": (prev)=>({
                        ...prev,
                        loading: true,
                        error: null
                    })
            }["useCategories.useCallback[fetchCategories]"]);
            try {
                const filtersToUse = newFilters || filters;
                const categories = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$categoryApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CategoryApiService"].getCategories(filtersToUse);
                setState({
                    categories,
                    loading: false,
                    error: null
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch categories';
                setState({
                    "useCategories.useCallback[fetchCategories]": (prev)=>({
                            ...prev,
                            loading: false,
                            error: errorMessage
                        })
                }["useCategories.useCallback[fetchCategories]"]);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to load categories');
            }
        }
    }["useCategories.useCallback[fetchCategories]"], [
        filters
    ]);
    // Initial fetch
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCategories.useEffect": ()=>{
            fetchCategories();
        }
    }["useCategories.useEffect"], [
        fetchCategories
    ]);
    const updateFilters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCategories.useCallback[updateFilters]": (newFilters)=>{
            setFilters(newFilters);
            fetchCategories(newFilters);
        }
    }["useCategories.useCallback[updateFilters]"], [
        fetchCategories
    ]);
    return {
        ...state,
        filters,
        updateFilters,
        refetch: fetchCategories
    };
}
_s(useCategories, "qg6vvDBSyZLEcz9c0stcUID6nAU=");
function useCategory(id) {
    _s1();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        category: null,
        loading: true,
        error: null
    });
    const fetchCategory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCategory.useCallback[fetchCategory]": async ()=>{
            if (!id) {
                setState({
                    category: null,
                    loading: false,
                    error: null
                });
                return;
            }
            setState({
                "useCategory.useCallback[fetchCategory]": (prev)=>({
                        ...prev,
                        loading: true,
                        error: null
                    })
            }["useCategory.useCallback[fetchCategory]"]);
            try {
                const category = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$categoryApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CategoryApiService"].getCategoryById(id);
                setState({
                    category,
                    loading: false,
                    error: null
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch category';
                setState({
                    "useCategory.useCallback[fetchCategory]": (prev)=>({
                            ...prev,
                            loading: false,
                            error: errorMessage
                        })
                }["useCategory.useCallback[fetchCategory]"]);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to load category');
            }
        }
    }["useCategory.useCallback[fetchCategory]"], [
        id
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useCategory.useEffect": ()=>{
            fetchCategory();
        }
    }["useCategory.useEffect"], [
        fetchCategory
    ]);
    return {
        ...state,
        refetch: fetchCategory
    };
}
_s1(useCategory, "recjNixmNjERx0R/tK+BqPWW6Ws=");
function useCategoryMutations() {
    _s2();
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const createCategory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCategoryMutations.useCallback[createCategory]": async (categoryData)=>{
            setLoading(true);
            try {
                const category = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$categoryApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CategoryApiService"].createCategory(categoryData);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Category created successfully');
                return category;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to create category';
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
                throw error;
            } finally{
                setLoading(false);
            }
        }
    }["useCategoryMutations.useCallback[createCategory]"], []);
    const updateCategory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCategoryMutations.useCallback[updateCategory]": async (id, updateData)=>{
            setLoading(true);
            try {
                const category = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$categoryApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CategoryApiService"].updateCategory(id, updateData);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Category updated successfully');
                return category;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to update category';
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
                throw error;
            } finally{
                setLoading(false);
            }
        }
    }["useCategoryMutations.useCallback[updateCategory]"], []);
    const deleteCategory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCategoryMutations.useCallback[deleteCategory]": async (id)=>{
            setLoading(true);
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$categoryApi$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CategoryApiService"].deleteCategory(id);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Category deleted successfully');
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete category';
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$2$2e$0$2e$3_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
                throw error;
            } finally{
                setLoading(false);
            }
        }
    }["useCategoryMutations.useCallback[deleteCategory]"], []);
    return {
        createCategory,
        updateCategory,
        deleteCategory,
        loading
    };
}
_s2(useCategoryMutations, "PbFygb7GYDwlhPINxhXFwpNJhbI=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/schemas/productSchema.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// schemas/productSchema.ts
__turbopack_context__.s({
    "ageRestrictionValues": (()=>ageRestrictionValues),
    "availabilitySchema": (()=>availabilitySchema),
    "basePricingSchema": (()=>basePricingSchema),
    "basicInfoSchema": (()=>basicInfoSchema),
    "conditionValues": (()=>conditionValues),
    "currencyValues": (()=>currencyValues),
    "detailsSchema": (()=>detailsSchema),
    "dimensionUnitValues": (()=>dimensionUnitValues),
    "imagesSchema": (()=>imagesSchema),
    "inventorySchema": (()=>inventorySchema),
    "pricingSchema": (()=>pricingSchema),
    "productSchema": (()=>productSchema),
    "productStatusValues": (()=>productStatusValues),
    "productTypeValues": (()=>productTypeValues),
    "returnPolicySchema": (()=>returnPolicySchema),
    "seoSchema": (()=>seoSchema),
    "shippingClassValues": (()=>shippingClassValues),
    "shippingSchema": (()=>shippingSchema),
    "shippingTimeValues": (()=>shippingTimeValues),
    "stockManagementValues": (()=>stockManagementValues),
    "taxStatusValues": (()=>taxStatusValues),
    "visibilityValues": (()=>visibilityValues),
    "warrantySchema": (()=>warrantySchema),
    "weightUnitValues": (()=>weightUnitValues)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zod@3.24.3/node_modules/zod/lib/index.mjs [app-client] (ecmascript)");
;
const conditionValues = [
    "new",
    "like-new",
    "excellent",
    "good",
    "fair",
    "used",
    "refurbished",
    "vintage",
    "antique",
    "damaged"
];
const productStatusValues = [
    "in-stock",
    "out-of-stock",
    "coming-soon",
    "archived",
    "draft",
    "suspended"
];
const productTypeValues = [
    "physical",
    "digital",
    "service",
    "subscription",
    "bundle"
];
const visibilityValues = [
    "public",
    "private",
    "hidden",
    "password-protected"
];
const taxStatusValues = [
    "taxable",
    "tax-exempt",
    "shipping-only"
];
const stockManagementValues = [
    "track",
    "no-track",
    "backorder"
];
const shippingClassValues = [
    "standard",
    "express",
    "overnight",
    "international",
    "heavy",
    "fragile",
    "digital-only"
];
const shippingTimeValues = [
    "same-day",
    "1-2-business-days",
    "2-3-business-days",
    "3-5-business-days",
    "5-7-business-days",
    "7-10-business-days",
    "10-14-business-days",
    "2-3-weeks",
    "3-4-weeks",
    "4-6-weeks"
];
const currencyValues = [
    "EUR",
    "USD"
];
const weightUnitValues = [
    "g",
    "kg"
];
const dimensionUnitValues = [
    "mm",
    "cm"
];
const ageRestrictionValues = [
    "none",
    "18+",
    "21+"
];
// Schema definitions
const dimensionsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    width: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().positive("Width must be positive"),
    height: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().positive("Height must be positive"),
    depth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().positive("Depth must be positive"),
    unit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(dimensionUnitValues)
});
const weightSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    value: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().positive("Weight must be positive"),
    unit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(weightUnitValues)
});
// Enhanced address schema
const addressSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    street: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    city: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    state: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    postalCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    country: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    coordinates: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
        latitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number(),
        longitude: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number()
    }).optional()
}).optional();
const basicInfoSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().min(1, "Name is required"),
    brand: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().min(1, "Brand is required"),
    model: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().min(10, "Description should be at least 10 characters"),
    shortDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    productType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(productTypeValues).default("physical"),
    visibility: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(visibilityValues).default("public")
});
const basePricingSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    price: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().positive("Price must be a positive number"),
    originalPrice: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>{
        if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
            return undefined;
        }
        const numVal = Number(val);
        return Number.isNaN(numVal) ? undefined : numVal;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().positive("Original price must be a positive number").optional()),
    salePrice: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>{
        if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
            return undefined;
        }
        const numVal = Number(val);
        return Number.isNaN(numVal) ? undefined : numVal;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().positive("Sale price must be a positive number").optional()),
    currency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(currencyValues),
    isOnSale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(false),
    saleEndsAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    costPrice: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().positive("Cost price is required for business intelligence"),
    taxStatus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(taxStatusValues).default("taxable"),
    taxClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    minimumOrderQuantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>{
        if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
            return undefined;
        }
        const numVal = Number(val);
        return Number.isNaN(numVal) ? undefined : numVal;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().int().positive().optional()),
    maximumOrderQuantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>{
        if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
            return undefined;
        }
        const numVal = Number(val);
        return Number.isNaN(numVal) ? undefined : numVal;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().int().positive().optional())
});
const pricingSchema = basePricingSchema.refine((data)=>{
    // If on sale, sale price must be lower than regular price
    if (data.isOnSale && data.salePrice) {
        return data.salePrice < data.price;
    }
    return true;
}, {
    message: "Sale price must be lower than regular price",
    path: [
        "salePrice"
    ]
}).refine((data)=>{
    // If on sale, original price should equal regular price (for backwards compatibility)
    if (data.isOnSale && data.originalPrice) {
        return data.originalPrice >= data.price;
    }
    return true;
}, {
    message: "Original price should be at least the regular price",
    path: [
        "originalPrice"
    ]
});
const inventorySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    stock: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().int().nonnegative("Stock must be zero or a positive number"),
    condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(conditionValues),
    isPublished: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(true),
    stockManagement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(stockManagementValues).default("track"),
    lowStockThreshold: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>{
        if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
            return undefined;
        }
        const numVal = Number(val);
        return Number.isNaN(numVal) ? undefined : numVal;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().int().nonnegative().optional()),
    backorderAllowed: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(false),
    trackQuantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(true),
    soldIndividually: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(false)
});
const detailsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().min(1, "Category is required"),
    subcategory: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    material: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().min(1, "Material is required"),
    dimensions: dimensionsSchema.optional(),
    weight: weightSchema.optional(),
    yearMade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>{
        if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
            return undefined;
        }
        const numVal = Number(val);
        return Number.isNaN(numVal) ? undefined : numVal;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().int("Year must be a whole number").min(1800).max(new Date().getFullYear()).optional()),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string()).min(1, "At least one tag is required"),
    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().min(1, "Color is required"),
    size: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional()
});
const imagesSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    mainImage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().url("Main image must be a valid URL"),
    images: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().url("Image must be a valid URL")).optional(),
    imageAltTexts: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string()).optional(),
    videoUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>val === "" || val === null || val === undefined ? undefined : val, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().url().optional()),
    threeDModelUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>val === "" || val === null || val === undefined ? undefined : val, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().url().optional())
});
const shippingSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    address: addressSchema,
    shippingCost: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>{
        if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
            return undefined;
        }
        const numVal = Number(val);
        return Number.isNaN(numVal) ? undefined : numVal;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().nonnegative("Shipping cost must be a non-negative number")),
    shippingTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(shippingTimeValues, {
        required_error: "Estimated shipping time is required"
    }),
    freeShipping: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(false),
    shippingClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(shippingClassValues, {
        required_error: "Shipping class is required"
    }),
    requiresShipping: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(true),
    separateShipping: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(false),
    shippingDimensions: dimensionsSchema.optional(),
    shippingWeight: weightSchema.optional()
});
const availabilitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    status: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(productStatusValues).default("draft"),
    ageRestriction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum(ageRestrictionValues).default("none"),
    availableFrom: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    availableUntil: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    featured: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(false),
    sticky: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(false),
    downloadable: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(false),
    virtual: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(false)
});
const seoSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    metaTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    metaDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    focusKeyword: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    slug: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    canonicalUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>val === "" || val === null || val === undefined ? undefined : val, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().url().optional())
});
const warrantySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    duration: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].preprocess((val)=>{
        if (val === "" || val === null || val === undefined || Number.isNaN(val)) {
            return undefined;
        }
        const numVal = Number(val);
        return Number.isNaN(numVal) ? undefined : numVal;
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().int().positive().optional()),
    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].enum([
        "manufacturer",
        "seller",
        "extended"
    ]).optional(),
    terms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string().optional(),
    coverage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string()).optional()
});
const returnPolicySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    allowed: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].boolean().default(true),
    period: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().int().positive().optional(),
    conditions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].string()).optional(),
    restockingFee: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].number().nonnegative().optional()
});
const productSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zod$40$3$2e$24$2e$3$2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["z"].object({
    ...basicInfoSchema.shape,
    ...basePricingSchema.shape,
    ...inventorySchema.shape,
    ...detailsSchema.shape,
    ...imagesSchema.shape,
    ...shippingSchema.shape,
    ...availabilitySchema.shape,
    ...seoSchema.shape,
    ...warrantySchema.shape,
    ...returnPolicySchema.shape
}).refine((data)=>{
    // If on sale, sale price must be lower than regular price
    if (data.isOnSale && data.salePrice) {
        return data.salePrice < data.price;
    }
    return true;
}, {
    message: "Sale price must be lower than regular price",
    path: [
        "salePrice"
    ]
}).refine((data)=>{
    // If on sale, original price should equal regular price (for backwards compatibility)
    if (data.isOnSale && data.originalPrice) {
        return data.originalPrice >= data.price;
    }
    return true;
}, {
    message: "Original price should be at least the regular price",
    path: [
        "originalPrice"
    ]
}).refine((data)=>{
    // Conditional validation based on product type
    if (data.productType === "digital") {
        return data.requiresShipping === false;
    }
    if (data.productType === "service") {
        return data.virtual === true;
    }
    return true;
}, {
    message: "Product configuration doesn't match the selected product type"
}).transform((data)=>{
    // Clean up fields that don't apply to certain product types
    if (data.productType === "digital") {
        // For digital products, remove physical-only fields
        return {
            ...data,
            yearMade: undefined,
            shippingCost: undefined,
            dimensions: undefined,
            weight: undefined,
            requiresShipping: false,
            virtual: true
        };
    }
    if (data.productType === "service") {
        // For services, remove physical-only fields
        return {
            ...data,
            yearMade: undefined,
            shippingCost: undefined,
            dimensions: undefined,
            weight: undefined,
            requiresShipping: false,
            virtual: true,
            stock: 0
        };
    }
    return data;
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_a39baa4c._.js.map