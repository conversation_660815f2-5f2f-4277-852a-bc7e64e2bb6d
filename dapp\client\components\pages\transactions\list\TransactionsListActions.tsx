"use client";

import { Download, Filter, Plus, RefreshCw } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";

export const TransactionsListActions = () => {
  return (
    <div className="flex gap-2">
      <Button variant="outline" size="sm">
        <Filter className="mr-2 h-4 w-4" />
        Advanced Filters
      </Button>

      <Button variant="outline" size="sm">
        <RefreshCw className="mr-2 h-4 w-4" />
        Refresh
      </Button>

      <Button variant="outline" size="sm">
        <Download className="mr-2 h-4 w-4" />
        Export
      </Button>

      <Button size="sm">
        <Plus className="mr-2 h-4 w-4" />
        Manual Transaction
      </Button>
    </div>
  );
};
