/**
 * React hooks for category data management
 * Provides easy-to-use hooks for CRUD operations on categories
 */

import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';

import { Category } from '@/components/pages/management/CategoryManager';
import {
  CategoryApiService,
  type CategoryFilters,
  type CreateCategoryDto,
  type UpdateCategoryDto,
} from '@/lib/api/categoryApi';

// Hook state types
interface UseCategoriesState {
  categories: Category[];
  loading: boolean;
  error: string | null;
}

interface UseCategoryState {
  category: Category | null;
  loading: boolean;
  error: string | null;
}

/**
 * Hook for fetching and managing categories list
 */
export function useCategories(initialFilters: CategoryFilters = {}) {
  const [state, setState] = useState<UseCategoriesState>({
    categories: [],
    loading: true,
    error: null,
  });

  const [filters, setFilters] = useState<CategoryFilters>(initialFilters);

  const fetchCategories = useCallback(async (newFilters?: CategoryFilters) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const filtersToUse = newFilters || filters;
      const categories = await CategoryApiService.getCategories(filtersToUse);
      
      setState({
        categories,
        loading: false,
        error: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch categories';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      toast.error('Failed to load categories');
    }
  }, [filters]);

  // Initial fetch
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const updateFilters = useCallback((newFilters: CategoryFilters) => {
    setFilters(newFilters);
    fetchCategories(newFilters);
  }, [fetchCategories]);

  return {
    ...state,
    filters,
    updateFilters,
    refetch: fetchCategories,
  };
}

/**
 * Hook for fetching and managing a single category
 */
export function useCategory(id: string | null) {
  const [state, setState] = useState<UseCategoryState>({
    category: null,
    loading: true,
    error: null,
  });

  const fetchCategory = useCallback(async () => {
    if (!id) {
      setState({ category: null, loading: false, error: null });
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const category = await CategoryApiService.getCategoryById(id);
      setState({
        category,
        loading: false,
        error: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch category';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      toast.error('Failed to load category');
    }
  }, [id]);

  useEffect(() => {
    fetchCategory();
  }, [fetchCategory]);

  return {
    ...state,
    refetch: fetchCategory,
  };
}

/**
 * Hook for category CRUD operations
 */
export function useCategoryMutations() {
  const [loading, setLoading] = useState(false);

  const createCategory = useCallback(async (categoryData: CreateCategoryDto) => {
    setLoading(true);
    try {
      const category = await CategoryApiService.createCategory(categoryData);
      toast.success('Category created successfully');
      return category;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create category';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateCategory = useCallback(async (id: string, updateData: UpdateCategoryDto) => {
    setLoading(true);
    try {
      const category = await CategoryApiService.updateCategory(id, updateData);
      toast.success('Category updated successfully');
      return category;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update category';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteCategory = useCallback(async (id: string) => {
    setLoading(true);
    try {
      await CategoryApiService.deleteCategory(id);
      toast.success('Category deleted successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete category';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createCategory,
    updateCategory,
    deleteCategory,
    loading,
  };
}
