"use client";

import React, { useEffect, useState } from "react";

import {
  Archive,
  Check,
  Copy,
  Edit,
  Eye,
  Filter,
  Grid3X3,
  List,
  MoreHorizontal,
  Package,
  Palette,
  Pencil,
  Plus,
  Save,
  Search,
  SortAsc,
  SortDesc,
  Star,
  Trash2,
  TrendingUp,
  X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useColors } from "@/hooks/useColors";
import { Color, CreateColorDto } from "@/lib/api/colors";

interface ColorManagerEnhancedProps {
  initialColors?: Color[];
  onColorsChange?: (colors: Color[]) => void;
}

/**
 * Enhanced component for managing product colors with real API integration
 */
export const ColorManagerEnhanced = ({
  initialColors = [],
  onColorsChange,
}: ColorManagerEnhancedProps) => {
  const router = useRouter();

  // API hooks
  const {
    colors: apiColors,
    loading,
    error,
    createColor,
    updateColor,
    deleteColor,
    refreshColors,
  } = useColors();

  // Local state
  const [colors, setColors] = useState<Color[]>(initialColors);
  const [newColor, setNewColor] = useState<Partial<CreateColorDto>>({
    name: "",
    description: "",
    hexCode: "#000000",
    family: "neutral",
    isActive: true,
  });
  const [editingColorId, setEditingColorId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<Color>>({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showInactive, setShowInactive] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<"name" | "created" | "products">("name");
  const [filterFamily, setFilterFamily] = useState<string>("all");

  // Update local colors when API data changes
  useEffect(() => {
    setColors(apiColors);
    if (onColorsChange) {
      onColorsChange(apiColors);
    }
  }, [apiColors, onColorsChange]);

  // Filter and sort colors
  const filteredColors = colors
    .filter((color) => {
      const matchesSearch = color.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesStatus = showInactive || color.isActive;
      const matchesFamily =
        filterFamily === "all" || color.family === filterFamily;
      return matchesSearch && matchesStatus && matchesFamily;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "created":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "products":
          return (b.productCount || 0) - (a.productCount || 0);
        default:
          return 0;
      }
    });

  // Handle create color
  const handleCreateColor = async () => {
    if (!newColor.name?.trim()) {
      toast.error("Color name is required");
      return;
    }

    if (!newColor.hexCode || !/^#[0-9A-F]{6}$/i.test(newColor.hexCode)) {
      toast.error("Valid hex color code is required (e.g., #FF0000)");
      return;
    }

    try {
      await createColor(newColor as CreateColorDto);
      setNewColor({
        name: "",
        description: "",
        hexCode: "#000000",
        family: "neutral",
        isActive: true,
      });
      setShowAddForm(false);
      refreshColors();
    } catch (error) {
      console.error("Error creating color:", error);
    }
  };

  // Handle update color
  const handleUpdateColor = async () => {
    if (!editingColorId || !editForm.name?.trim()) {
      toast.error("Color name is required");
      return;
    }

    try {
      await updateColor(editingColorId, editForm);
      setEditingColorId(null);
      setEditForm({});
      refreshColors();
    } catch (error) {
      console.error("Error updating color:", error);
    }
  };

  // Handle delete color
  const handleDeleteColor = async (colorId: string) => {
    if (!confirm("Are you sure you want to delete this color?")) {
      return;
    }

    try {
      await deleteColor(colorId);
      refreshColors();
    } catch (error) {
      console.error("Error deleting color:", error);
    }
  };

  // Statistics
  const stats = {
    total: colors.length,
    active: colors.filter((c) => c.isActive).length,
    primary: colors.filter((c) => c.family === "primary").length,
    neutral: colors.filter((c) => c.family === "neutral").length,
  };

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4">
        <p className="text-red-600">Error loading colors: {error}</p>
        <Button onClick={refreshColors} className="mt-2">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-pink-100 p-2">
                <Palette className="h-5 w-5 text-pink-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Colors</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-green-100 p-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Colors</p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-100 p-2">
                <Star className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Primary</p>
                <p className="text-2xl font-bold">{stats.primary}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-gray-100 p-2">
                <Package className="h-5 w-5 text-gray-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Neutral</p>
                <p className="text-2xl font-bold">{stats.neutral}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            {/* Search and Filters */}
            <div className="flex flex-1 gap-4">
              <div className="relative max-w-md flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search colors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={filterFamily} onValueChange={setFilterFamily}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Families</SelectItem>
                  <SelectItem value="primary">Primary</SelectItem>
                  <SelectItem value="secondary">Secondary</SelectItem>
                  <SelectItem value="neutral">Neutral</SelectItem>
                  <SelectItem value="warm">Warm</SelectItem>
                  <SelectItem value="cool">Cool</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={sortBy}
                onValueChange={(value: any) => setSortBy(value)}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="created">Created</SelectItem>
                  <SelectItem value="products">Products</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <div className="flex items-center gap-2">
                <Switch
                  checked={showInactive}
                  onCheckedChange={setShowInactive}
                />
                <Label className="text-sm">Show inactive</Label>
              </div>

              <div className="flex gap-1">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <Button onClick={() => setShowAddForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Color
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Color Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Color</CardTitle>
            <CardDescription>
              Create a new color for your product catalog
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="color-name">Name *</Label>
                <Input
                  id="color-name"
                  value={newColor.name || ""}
                  onChange={(e) =>
                    setNewColor({ ...newColor, name: e.target.value })
                  }
                  placeholder="e.g., Ocean Blue"
                />
              </div>
              <div>
                <Label htmlFor="color-hex">Color Selection *</Label>
                <div className="space-y-3">
                  {/* Color Picker */}
                  <div className="flex gap-2">
                    <input
                      type="color"
                      id="color-picker"
                      value={newColor.hexCode || "#000000"}
                      onChange={(e) =>
                        setNewColor({ ...newColor, hexCode: e.target.value })
                      }
                      className="h-10 w-16 cursor-pointer rounded border"
                    />
                    <Input
                      id="color-hex"
                      value={newColor.hexCode || ""}
                      onChange={(e) =>
                        setNewColor({ ...newColor, hexCode: e.target.value })
                      }
                      placeholder="#FF0000"
                      className="flex-1"
                    />
                  </div>

                  {/* Preset Colors */}
                  <div>
                    <Label className="text-xs text-gray-500">
                      Quick Colors
                    </Label>
                    <div className="mt-1 flex gap-2">
                      {[
                        "#FF0000",
                        "#00FF00",
                        "#0000FF",
                        "#FFFF00",
                        "#FF00FF",
                        "#00FFFF",
                        "#FFA500",
                        "#800080",
                        "#FFC0CB",
                        "#A52A2A",
                        "#808080",
                        "#000000",
                      ].map((color) => (
                        <button
                          key={color}
                          type="button"
                          className="h-8 w-8 rounded border-2 border-gray-300 transition-colors hover:border-gray-500"
                          style={{ backgroundColor: color }}
                          onClick={() =>
                            setNewColor({ ...newColor, hexCode: color })
                          }
                          title={color}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="color-family">Family</Label>
                <Select
                  value={newColor.family}
                  onValueChange={(value: any) =>
                    setNewColor({ ...newColor, family: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="primary">Primary</SelectItem>
                    <SelectItem value="secondary">Secondary</SelectItem>
                    <SelectItem value="neutral">Neutral</SelectItem>
                    <SelectItem value="warm">Warm</SelectItem>
                    <SelectItem value="cool">Cool</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="color-description">Description</Label>
              <Textarea
                id="color-description"
                value={newColor.description || ""}
                onChange={(e) =>
                  setNewColor({ ...newColor, description: e.target.value })
                }
                placeholder="Describe the color and its uses..."
                rows={3}
              />
            </div>

            <div className="flex items-center gap-2">
              <Switch
                checked={newColor.isActive}
                onCheckedChange={(checked) =>
                  setNewColor({ ...newColor, isActive: checked })
                }
              />
              <Label>Active</Label>
            </div>

            <div className="flex gap-2 pt-2">
              <Button onClick={handleCreateColor}>
                <Check className="mr-2 h-4 w-4" />
                Add Color
              </Button>
              <Button variant="outline" onClick={() => setShowAddForm(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Colors Display */}
      {loading ? (
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-20 w-full" />
          ))}
        </div>
      ) : filteredColors.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No colors found</p>
            {searchTerm && (
              <Button
                variant="link"
                onClick={() => setSearchTerm("")}
                className="mt-2"
              >
                Clear search
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div
          className={
            viewMode === "grid"
              ? "grid gap-4 md:grid-cols-2 lg:grid-cols-3"
              : "space-y-3"
          }
        >
          {filteredColors.map((color) => (
            <Card
              key={color._id}
              className={`transition-shadow hover:shadow-md ${
                !color.isActive ? "opacity-60" : ""
              }`}
            >
              <CardContent className="p-4">
                {editingColorId === color._id ? (
                  // Edit form
                  <div className="space-y-3">
                    <Input
                      value={editForm.name || ""}
                      onChange={(e) =>
                        setEditForm({ ...editForm, name: e.target.value })
                      }
                      placeholder="Color name"
                    />
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <input
                          type="color"
                          value={editForm.hexCode || "#000000"}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              hexCode: e.target.value,
                            })
                          }
                          className="h-10 w-16 cursor-pointer rounded border"
                        />
                        <Input
                          value={editForm.hexCode || ""}
                          onChange={(e) =>
                            setEditForm({
                              ...editForm,
                              hexCode: e.target.value,
                            })
                          }
                          placeholder="#FF0000"
                          className="flex-1"
                        />
                      </div>
                      <div className="flex gap-1">
                        {[
                          "#FF0000",
                          "#00FF00",
                          "#0000FF",
                          "#FFFF00",
                          "#FF00FF",
                          "#00FFFF",
                        ].map((color) => (
                          <button
                            key={color}
                            type="button"
                            className="h-6 w-6 rounded border border-gray-300 transition-colors hover:border-gray-500"
                            style={{ backgroundColor: color }}
                            onClick={() =>
                              setEditForm({ ...editForm, hexCode: color })
                            }
                            title={color}
                          />
                        ))}
                      </div>
                    </div>
                    <Textarea
                      value={editForm.description || ""}
                      onChange={(e) =>
                        setEditForm({
                          ...editForm,
                          description: e.target.value,
                        })
                      }
                      placeholder="Description"
                      rows={2}
                    />
                    <div className="flex gap-2">
                      <Button size="sm" onClick={handleUpdateColor}>
                        <Save className="mr-2 h-4 w-4" />
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingColorId(null);
                          setEditForm({});
                        }}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  // Display mode
                  <div>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div
                          className="h-12 w-12 rounded-lg border-2 border-gray-200 shadow-sm"
                          style={{ backgroundColor: color.hexCode }}
                        />
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            {color.name}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {color.hexCode}
                          </p>
                          <div className="mt-1 flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {color.family}
                            </Badge>
                            {!color.isActive && (
                              <Badge variant="secondary" className="text-xs">
                                Inactive
                              </Badge>
                            )}
                            <span className="text-xs text-gray-500">
                              {color.productCount || 0} products
                            </span>
                          </div>
                        </div>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setEditingColorId(color._id);
                              setEditForm(color);
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Products
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteColor(color._id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
