import { FilterQuery } from "mongoose";

import { Brand } from "../models/brand.model";
import {
  BrandDocument,
  CreateBrandDto,
  UpdateBrandDto,
  BrandFilters,
} from "../types/brand.types";

/**
 * Service class for handling brand-related business logic
 */
export class BrandService {
  /**
   * Create a new brand
   */
  async createBrand(brandData: CreateBrandDto): Promise<BrandDocument> {
    try {
      // Generate slug if not provided
      const slug = this.generateSlug(brandData.name);

      // Check if slug already exists
      const existingBrand = await Brand.findOne({ slug });
      if (existingBrand) {
        throw new Error("A brand with this name already exists");
      }

      // Get the next sort order
      const maxSortOrder = await Brand.findOne(
        {},
        {},
        { sort: { sortOrder: -1 } }
      );
      const sortOrder =
        brandData.sortOrder ?? (maxSortOrder?.sortOrder ?? 0) + 1;

      const brand = new Brand({
        ...brandData,
        slug,
        sortOrder,
      });

      return await brand.save();
    } catch (error) {
      throw this.handleError(error, "Error creating brand");
    }
  }

  /**
   * Get all brands with optional filtering
   */
  async getBrands(filters: BrandFilters = {}): Promise<BrandDocument[]> {
    try {
      const query: FilterQuery<BrandDocument> = {};

      // Apply filters
      if (filters.isActive !== undefined) {
        query.isActive = filters.isActive;
      }

      if (filters.search) {
        query.$text = { $search: filters.search };
      }

      return await Brand.find(query).sort({ sortOrder: 1, createdAt: -1 });
    } catch (error) {
      throw this.handleError(error, "Error fetching brands");
    }
  }

  /**
   * Get a brand by ID
   */
  async getBrandById(id: string): Promise<BrandDocument | null> {
    try {
      return await Brand.findById(id);
    } catch (error) {
      throw this.handleError(error, "Error fetching brand");
    }
  }

  /**
   * Get a brand by slug
   */
  async getBrandBySlug(slug: string): Promise<BrandDocument | null> {
    try {
      return await Brand.findOne({ slug });
    } catch (error) {
      throw this.handleError(error, "Error fetching brand");
    }
  }

  /**
   * Get a brand by name
   */
  async getBrandByName(name: string): Promise<BrandDocument | null> {
    try {
      return await Brand.findOne({ name });
    } catch (error) {
      throw this.handleError(error, "Error fetching brand by name");
    }
  }

  /**
   * Update a brand
   */
  async updateBrand(
    id: string,
    updateData: UpdateBrandDto
  ): Promise<BrandDocument | null> {
    try {
      // If name is being updated, regenerate slug
      if (updateData.name) {
        const newSlug = this.generateSlug(updateData.name);

        // Check if new slug conflicts with existing brands (excluding current one)
        const existingBrand = await Brand.findOne({
          slug: newSlug,
          _id: { $ne: id },
        });

        if (existingBrand) {
          throw new Error("A brand with this name already exists");
        }

        updateData = { ...updateData, slug: newSlug };
      }

      return await Brand.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      });
    } catch (error) {
      throw this.handleError(error, "Error updating brand");
    }
  }

  /**
   * Delete a brand
   */
  async deleteBrand(id: string): Promise<boolean> {
    try {
      // Check if brand has products
      const brand = await Brand.findById(id);
      if (brand && brand.productCount > 0) {
        throw new Error("Cannot delete brand with products");
      }

      const result = await Brand.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      throw this.handleError(error, "Error deleting brand");
    }
  }

  /**
   * Recalculate product counts for all brands
   */
  async recalculateProductCounts(): Promise<void> {
    try {
      console.info("Starting brand product count recalculation...");

      const brands = await Brand.find({});
      console.info(`Found ${brands.length} brands to process`);

      // Import Product model here to avoid circular dependency
      const { Product } = await import("../models/product.model");

      const totalProducts = await Product.countDocuments();
      console.info(`Found ${totalProducts} total products`);

      // Sample some products to see their brand structure
      const sampleProducts = await Product.find({}).limit(5);
      console.info("Sample products with brands:");
      sampleProducts.forEach((product) => {
        console.info(
          `- Product: "${product.name}", Brand: "${product.brand}" (type: ${typeof product.brand})`
        );
      });

      for (const brand of brands) {
        console.info(`Processing brand: "${brand.name}" (ID: ${brand._id})`);

        // Count products by brand name (since products store brand as string)
        const countByName = await Product.countDocuments({
          brand: brand.name,
        });

        console.info(
          `  - Count by name: ${countByName}`
        );

        // Update the brand with the correct count
        await Brand.findByIdAndUpdate(brand._id, {
          productCount: countByName,
        });

        console.info(`  ✓ Updated brand "${brand.name}" product count to ${countByName}`);
      }

      console.info("Brand product count recalculation completed successfully");
    } catch (error) {
      console.error("Error recalculating brand product counts:", error);
      throw this.handleError(error, "Error recalculating brand product counts");
    }
  }

  /**
   * Generate a URL-friendly slug from a string
   */
  private generateSlug(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
  }

  /**
   * Handle and format errors consistently
   */
  private handleError(error: unknown, context: string): Error {
    if (error instanceof Error) {
      return new Error(`${context}: ${error.message}`);
    }
    return new Error(`${context}: Unknown error occurred`);
  }
}
