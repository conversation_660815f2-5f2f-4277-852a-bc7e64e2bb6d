"use client";

import React, { useState } from "react";

import {
  Activity,
  ArrowDownRight,
  ArrowUpRight,
  BarChart3,
  Calendar,
  DollarSign,
  Download,
  Eye,
  Filter,
  LineChart,
  Package,
  PieChart,
  ShoppingCart,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";

import { PageHeaderWrapper } from "@/components/common/PageHeaderWrapper";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function Analytics() {
  const [timeRange, setTimeRange] = useState("30d");

  // Mock analytics data
  const salesData = {
    totalRevenue: 125420.5,
    revenueChange: 12.5,
    totalOrders: 1847,
    ordersChange: 8.3,
    avgOrderValue: 67.89,
    avgOrderChange: 4.2,
    conversionRate: 3.4,
    conversionChange: -0.8,
  };

  const topProducts = [
    { name: "Wireless Headphones", sales: 1240, revenue: 89280, growth: 15.2 },
    { name: "Smart Watch", sales: 856, revenue: 171200, growth: 22.1 },
    { name: "USB-C Cable", sales: 2340, revenue: 46800, growth: 8.7 },
    { name: "Phone Case", sales: 1680, revenue: 50400, growth: -2.3 },
    { name: "Laptop Stand", sales: 420, revenue: 29400, growth: 18.9 },
  ];

  const customerMetrics = {
    totalCustomers: 8420,
    newCustomers: 247,
    returningCustomers: 1580,
    customerRetention: 78.5,
    avgLifetimeValue: 456.8,
  };

  const trafficData = [
    {
      source: "Organic Search",
      visitors: 12450,
      percentage: 42.1,
      change: 8.2,
    },
    { source: "Direct", visitors: 8920, percentage: 30.2, change: 5.1 },
    { source: "Social Media", visitors: 4680, percentage: 15.8, change: 12.7 },
    { source: "Email", visitors: 2340, percentage: 7.9, change: -3.2 },
    { source: "Paid Ads", visitors: 1180, percentage: 4.0, change: 22.5 },
  ];

  // Mock chart data for visual representation
  const revenueChartData = [
    { month: "Jul", revenue: 85000 },
    { month: "Aug", revenue: 92000 },
    { month: "Sep", revenue: 78000 },
    { month: "Oct", revenue: 105000 },
    { month: "Nov", revenue: 111540 },
    { month: "Dec", revenue: 125420 },
  ];

  const orderStatusData = [
    {
      status: "Completed",
      count: 1240,
      percentage: 67.2,
      color: "bg-green-500",
    },
    {
      status: "Processing",
      count: 380,
      percentage: 20.6,
      color: "bg-blue-500",
    },
    { status: "Pending", count: 156, percentage: 8.4, color: "bg-yellow-500" },
    { status: "Cancelled", count: 71, percentage: 3.8, color: "bg-red-500" },
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? "+" : "";
    return `${sign}${value.toFixed(1)}%`;
  };

  return (
    <>
      <PageHeaderWrapper
        title="Analytics"
        description="Comprehensive business intelligence with visual insights and performance metrics"
      >
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 3 months</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </Button>

          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </PageHeaderWrapper>

      <div className="container mx-auto mt-6 space-y-6">
        {/* Key Metrics Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Revenue
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(salesData.totalRevenue)}
              </div>
              <p className="flex items-center text-xs text-muted-foreground">
                {salesData.revenueChange >= 0 ? (
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                )}
                {formatPercentage(salesData.revenueChange)} from last period
              </p>
              {/* Mini chart background */}
              <div className="absolute bottom-0 right-0 opacity-10">
                <LineChart className="h-16 w-16 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Orders
              </CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {salesData.totalOrders.toLocaleString()}
              </div>
              <p className="flex items-center text-xs text-muted-foreground">
                {salesData.ordersChange >= 0 ? (
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                )}
                {formatPercentage(salesData.ordersChange)} from last period
              </p>
              <div className="absolute bottom-0 right-0 opacity-10">
                <BarChart3 className="h-16 w-16 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Avg. Order Value
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(salesData.avgOrderValue)}
              </div>
              <p className="flex items-center text-xs text-muted-foreground">
                {salesData.avgOrderChange >= 0 ? (
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                )}
                {formatPercentage(salesData.avgOrderChange)} from last period
              </p>
              <div className="absolute bottom-0 right-0 opacity-10">
                <Activity className="h-16 w-16 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Conversion Rate
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {salesData.conversionRate}%
              </div>
              <p className="flex items-center text-xs text-muted-foreground">
                {salesData.conversionChange >= 0 ? (
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                )}
                {formatPercentage(salesData.conversionChange)} from last period
              </p>
              <div className="absolute bottom-0 right-0 opacity-10">
                <PieChart className="h-16 w-16 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Visual Charts Section */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Revenue Trend Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5 text-blue-600" />
                Revenue Trend (6 Months)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Simple bar chart representation */}
                <div className="flex h-40 items-end justify-between gap-2">
                  {revenueChartData.map((data, index) => {
                    const height = (data.revenue / 125420) * 100; // Percentage of max
                    return (
                      <div
                        key={data.month}
                        className="flex flex-1 flex-col items-center"
                      >
                        <div className="flex w-full flex-col items-center">
                          <div className="mb-1 text-xs text-gray-500">
                            {formatCurrency(data.revenue / 1000)}k
                          </div>
                          <div
                            className="w-full rounded-t bg-gradient-to-t from-blue-500 to-blue-300 transition-all duration-500 hover:from-blue-600 hover:to-blue-400"
                            style={{ height: `${height}%`, minHeight: "20px" }}
                          />
                        </div>
                        <div className="mt-2 text-xs font-medium text-gray-700">
                          {data.month}
                        </div>
                      </div>
                    );
                  })}
                </div>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>6 months growth trend</span>
                  <span className="flex items-center gap-1 text-green-600">
                    <TrendingUp className="h-3 w-3" />
                    +47.2% overall
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5 text-green-600" />
                Order Status Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Donut chart representation */}
                <div className="relative">
                  <div className="relative mx-auto h-32 w-32">
                    {/* Outer ring segments */}
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background: `conic-gradient(
                        #10b981 0deg ${orderStatusData[0].percentage * 3.6}deg,
                        #3b82f6 ${orderStatusData[0].percentage * 3.6}deg ${(orderStatusData[0].percentage + orderStatusData[1].percentage) * 3.6}deg,
                        #eab308 ${(orderStatusData[0].percentage + orderStatusData[1].percentage) * 3.6}deg ${(orderStatusData[0].percentage + orderStatusData[1].percentage + orderStatusData[2].percentage) * 3.6}deg,
                        #ef4444 ${(orderStatusData[0].percentage + orderStatusData[1].percentage + orderStatusData[2].percentage) * 3.6}deg 360deg
                      )`,
                      }}
                    >
                      {/* Inner circle to create donut effect */}
                      <div className="absolute inset-4 flex items-center justify-center rounded-full bg-white">
                        <div className="text-center">
                          <div className="text-lg font-bold text-gray-900">
                            {salesData.totalOrders.toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-500">Total</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Legend */}
                <div className="grid grid-cols-2 gap-2">
                  {orderStatusData.map((status) => (
                    <div
                      key={status.status}
                      className="flex items-center gap-2"
                    >
                      <div className={`h-3 w-3 rounded-full ${status.color}`} />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">
                          {status.status}
                        </div>
                        <div className="text-xs text-gray-500">
                          {status.count} ({status.percentage}%)
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
