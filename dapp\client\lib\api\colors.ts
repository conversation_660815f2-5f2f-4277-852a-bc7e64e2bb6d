import { ApiResponse } from "./types";

// Get API base URL from environment or default to localhost
const getApiBaseUrl = (): string => {
  if (typeof window !== "undefined") {
    // Client-side: use current origin or environment variable
    return (
      process.env.NEXT_PUBLIC_API_URL ||
      `${window.location.protocol}//${window.location.hostname}:3011`
    );
  }
  // Server-side: use environment variable or default
  return process.env.NEXT_PUBLIC_API_URL || "http://localhost:3011";
};

const API_BASE_URL = getApiBaseUrl();

/**
 * RGB color value interface
 */
export interface RgbValue {
  r: number;
  g: number;
  b: number;
}

/**
 * HSL color value interface
 */
export interface HslValue {
  h: number;
  s: number;
  l: number;
}

/**
 * Color family type
 */
export type ColorFamily =
  | "red"
  | "orange"
  | "yellow"
  | "green"
  | "blue"
  | "purple"
  | "pink"
  | "brown"
  | "gray"
  | "black"
  | "white";

/**
 * Color interface matching the backend Color type
 */
export interface Color {
  _id: string;
  name: string;
  description: string;
  slug: string;
  hexValue: string;
  rgbValue: RgbValue;
  hslValue: HslValue;
  colorFamily: ColorFamily;
  isActive: boolean;
  productCount: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO for creating a new color
 */
export interface CreateColorDto {
  name: string;
  description?: string;
  hexValue: string;
  rgbValue?: RgbValue;
  hslValue?: HslValue;
  colorFamily: ColorFamily;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * DTO for updating a color
 */
export interface UpdateColorDto {
  name?: string;
  description?: string;
  hexValue?: string;
  rgbValue?: RgbValue;
  hslValue?: HslValue;
  colorFamily?: ColorFamily;
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * Color filters for querying
 */
export interface ColorFilters {
  isActive?: boolean;
  search?: string;
  colorFamily?: ColorFamily;
  hexValue?: string;
}

/**
 * API service for color management
 */
export class ColorApi {
  private static readonly BASE_URL = `${API_BASE_URL}/api/colors`;

  /**
   * Get all colors with optional filtering
   */
  static async getColors(
    filters?: ColorFilters
  ): Promise<ApiResponse<Color[]>> {
    try {
      const params = new URLSearchParams();

      if (filters?.isActive !== undefined) {
        params.append("isActive", filters.isActive.toString());
      }

      if (filters?.search) {
        params.append("search", filters.search);
      }

      if (filters?.colorFamily) {
        params.append("colorFamily", filters.colorFamily);
      }

      if (filters?.hexValue) {
        params.append("hexValue", filters.hexValue);
      }

      const queryString = params.toString();
      const url = queryString
        ? `${this.BASE_URL}?${queryString}`
        : this.BASE_URL;

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching colors:", error);
      throw error;
    }
  }

  /**
   * Get a color by ID
   */
  static async getColorById(id: string): Promise<ApiResponse<Color>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching color:", error);
      throw error;
    }
  }

  /**
   * Get a color by slug
   */
  static async getColorBySlug(slug: string): Promise<ApiResponse<Color>> {
    try {
      const response = await fetch(`${this.BASE_URL}/slug/${slug}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching color by slug:", error);
      throw error;
    }
  }

  /**
   * Get a color by hex value
   */
  static async getColorByHex(hex: string): Promise<ApiResponse<Color>> {
    try {
      // Remove # if present for URL
      const hexValue = hex.replace("#", "");
      const response = await fetch(`${this.BASE_URL}/hex/${hexValue}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching color by hex:", error);
      throw error;
    }
  }

  /**
   * Get colors grouped by color family
   */
  static async getColorsByFamily(): Promise<
    ApiResponse<Record<ColorFamily, Color[]>>
  > {
    try {
      const response = await fetch(`${this.BASE_URL}/families`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error fetching colors by family:", error);
      throw error;
    }
  }

  /**
   * Create a new color
   */
  static async createColor(
    colorData: CreateColorDto
  ): Promise<ApiResponse<Color>> {
    try {
      const response = await fetch(this.BASE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(colorData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error creating color:", error);
      throw error;
    }
  }

  /**
   * Update a color
   */
  static async updateColor(
    id: string,
    updateData: UpdateColorDto
  ): Promise<ApiResponse<Color>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error updating color:", error);
      throw error;
    }
  }

  /**
   * Delete a color
   */
  static async deleteColor(id: string): Promise<ApiResponse<null>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error deleting color:", error);
      throw error;
    }
  }

  /**
   * Recalculate product counts for all colors
   */
  static async recalculateProductCounts(): Promise<ApiResponse<null>> {
    try {
      const response = await fetch(`${this.BASE_URL}/recalculate-counts`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error recalculating color product counts:", error);
      throw error;
    }
  }
}
