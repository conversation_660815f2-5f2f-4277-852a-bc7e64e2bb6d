"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BrandApi } from "@/lib/api/brands";
import { useBrands } from "@/hooks/useBrands";

export const BrandsDebug = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const { brands, loading, error } = useBrands();

  const addLog = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testDirectAPI = async () => {
    addLog("🧪 Testing direct API call...");
    try {
      const response = await fetch("http://localhost:3011/api/brands");
      addLog(`Response status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        addLog(`✅ Direct API success: ${JSON.stringify(data)}`);
      } else {
        addLog(`❌ Direct API failed: ${response.statusText}`);
      }
    } catch (error) {
      addLog(`❌ Direct API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const testBrandApi = async () => {
    addLog("🧪 Testing BrandApi class...");
    try {
      const response = await BrandApi.getBrands();
      addLog(`✅ BrandApi success: ${JSON.stringify(response)}`);
    } catch (error) {
      addLog(`❌ BrandApi error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const clearLogs = () => {
    setTestResults([]);
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Brands API Debug</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={testDirectAPI}>Test Direct API</Button>
          <Button onClick={testBrandApi}>Test BrandApi Class</Button>
          <Button onClick={clearLogs} variant="outline">Clear Logs</Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold mb-2">useBrands Hook Status:</h3>
            <div className="space-y-1 text-sm">
              <p>Loading: {loading ? "Yes" : "No"}</p>
              <p>Error: {error || "None"}</p>
              <p>Brands Count: {brands.length}</p>
              <p>Brands: {JSON.stringify(brands, null, 2)}</p>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Test Results:</h3>
            <div className="bg-gray-100 p-3 rounded max-h-60 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500">No tests run yet</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="text-xs mb-1 font-mono">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
