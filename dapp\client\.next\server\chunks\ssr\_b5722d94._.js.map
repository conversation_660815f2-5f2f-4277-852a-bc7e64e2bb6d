{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/common/PageHeaderWrapper.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ntype PageHeaderWrapperProps = {\r\n  title: string;\r\n  description: string;\r\n  children?: React.ReactNode;\r\n};\r\n\r\n/**\r\n * A standardized page header component with title and description\r\n * Used across admin pages for consistent UI\r\n */\r\nexport const PageHeaderWrapper = ({\r\n  title,\r\n  description,\r\n  children,\r\n}: PageHeaderWrapperProps) => {\r\n  return (\r\n    <div className=\"container mx-auto py-6\">\r\n      <div className=\"flex flex-col items-start justify-between gap-4 border-b pb-5 sm:flex-row sm:items-center sm:gap-0\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold tracking-tight\">{title}</h1>\r\n          <p className=\"text-muted-foreground\">{description}</p>\r\n        </div>\r\n        {children && <div>{children}</div>}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAYO,MAAM,oBAAoB,CAAC,EAChC,KAAK,EACL,WAAW,EACX,QAAQ,EACe;IACvB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;;sCACC,6WAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6WAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;gBAEvC,0BAAY,6WAAC;8BAAK;;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductListActions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { FileDown, Plus, RefreshCw, Upload } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface ProductListActionsProps {\r\n  onRefresh?: () => void;\r\n  loading?: boolean;\r\n}\r\n\r\nexport const ProductListActions = ({\r\n  onRefresh,\r\n  loading = false,\r\n}: ProductListActionsProps) => {\r\n  const router = useRouter();\r\n\r\n  const handleExport = () => console.log(\"Exporting...\");\r\n  const handleImport = () => console.log(\"Importing...\");\r\n  const handleRefresh = () => onRefresh?.();\r\n  const handleCreate = () => router.push(\"/admin/products/add\");\r\n\r\n  return (\r\n    <div className=\"flex gap-2\">\r\n      <Button\r\n        variant=\"outline\"\r\n        size=\"sm\"\r\n        onClick={handleRefresh}\r\n        disabled={loading}\r\n      >\r\n        <RefreshCw\r\n          className={`mr-2 h-4 w-4 ${loading ? \"animate-spin\" : \"\"}`}\r\n        />\r\n        Refresh\r\n      </Button>\r\n\r\n      <Button variant=\"outline\" size=\"sm\" onClick={handleImport}>\r\n        <Upload className=\"mr-2 h-4 w-4\" />\r\n        Import\r\n      </Button>\r\n\r\n      <Button variant=\"outline\" size=\"sm\" onClick={handleExport}>\r\n        <FileDown className=\"mr-2 h-4 w-4\" />\r\n        Export\r\n      </Button>\r\n\r\n      <Button size=\"sm\" onClick={handleCreate}>\r\n        <Plus className=\"mr-2 h-4 w-4\" />\r\n        Add Product\r\n      </Button>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AACA;AAEA;AAPA;;;;;AAcO,MAAM,qBAAqB,CAAC,EACjC,SAAS,EACT,UAAU,KAAK,EACS;IACxB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,IAAM,QAAQ,GAAG,CAAC;IACvC,MAAM,eAAe,IAAM,QAAQ,GAAG,CAAC;IACvC,MAAM,gBAAgB,IAAM;IAC5B,MAAM,eAAe,IAAM,OAAO,IAAI,CAAC;IAEvC,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,2HAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,UAAU;;kCAEV,6WAAC,oSAAA,CAAA,YAAS;wBACR,WAAW,CAAC,aAAa,EAAE,UAAU,iBAAiB,IAAI;;;;;;oBAC1D;;;;;;;0BAIJ,6WAAC,2HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;gBAAK,SAAS;;kCAC3C,6WAAC,0RAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIrC,6WAAC,2HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;gBAAK,SAAS;;kCAC3C,6WAAC,kSAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIvC,6WAAC,2HAAA,CAAA,SAAM;gBAAC,MAAK;gBAAK,SAAS;;kCACzB,6WAAC,sRAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6WAAC,4QAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,4QAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/products.ts"], "sourcesContent": ["/**\r\n * API functions for product operations\r\n * Handles all HTTP requests to the backend product endpoints\r\n */\r\n\r\n// Types for API responses\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  meta?: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    pages: number;\r\n  };\r\n}\r\n\r\nexport interface ProductFilters {\r\n  search?: string;\r\n  category?: string;\r\n  brand?: string;\r\n  minPrice?: number;\r\n  maxPrice?: number;\r\n  condition?: string;\r\n  inStock?: boolean;\r\n  tags?: string[];\r\n  status?: string;\r\n  sortBy?: \"price\" | \"createdAt\" | \"name\";\r\n  sortOrder?: \"asc\" | \"desc\";\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\n// Get API base URL from environment or default to localhost\r\nconst getApiBaseUrl = (): string => {\r\n  if (typeof window !== \"undefined\") {\r\n    // Client-side: use current origin or environment variable\r\n    return (\r\n      process.env.NEXT_PUBLIC_API_URL ||\r\n      `${window.location.protocol}//${window.location.hostname}:3001`\r\n    );\r\n  }\r\n  // Server-side: use environment variable or default\r\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\";\r\n};\r\n\r\nconst API_BASE_URL = getApiBaseUrl();\r\n\r\n/**\r\n * Generic fetch wrapper with error handling\r\n */\r\nasync function apiRequest<T>(\r\n  endpoint: string,\r\n  options: RequestInit = {}\r\n): Promise<ApiResponse<T>> {\r\n  const url = `${API_BASE_URL}/api${endpoint}`;\r\n\r\n  const defaultOptions: RequestInit = {\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      ...options.headers,\r\n    },\r\n    ...options,\r\n  };\r\n\r\n  console.log(`🌐 Making API request to: ${url}`);\r\n  console.log(`🌐 Request options:`, defaultOptions);\r\n\r\n  try {\r\n    const response = await fetch(url, defaultOptions);\r\n\r\n    console.log(`🌐 Response status: ${response.status}`);\r\n    console.log(`🌐 Response ok: ${response.ok}`);\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      console.error(`🌐 Response error text:`, errorText);\r\n      throw new Error(\r\n        `HTTP error! status: ${response.status}, message: ${errorText}`\r\n      );\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(`🌐 Response data:`, data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(`🌐 API request failed for ${endpoint}:`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Get all products with optional filtering\r\n */\r\nexport async function getProducts(\r\n  filters: ProductFilters = {}\r\n): Promise<ApiResponse<any[]>> {\r\n  const searchParams = new URLSearchParams();\r\n\r\n  // Add filters to search params\r\n  Object.entries(filters).forEach(([key, value]) => {\r\n    if (value !== undefined && value !== null && value !== \"\") {\r\n      if (Array.isArray(value)) {\r\n        value.forEach((item) => searchParams.append(key, item.toString()));\r\n      } else {\r\n        searchParams.append(key, value.toString());\r\n      }\r\n    }\r\n  });\r\n\r\n  const queryString = searchParams.toString();\r\n  const endpoint = queryString ? `/products?${queryString}` : \"/products\";\r\n\r\n  return apiRequest<any[]>(endpoint);\r\n}\r\n\r\n/**\r\n * Get a single product by ID\r\n */\r\nexport async function getProductById(id: string): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}`);\r\n}\r\n\r\n/**\r\n * Get a product for editing (with safe field list)\r\n */\r\nexport async function getProductForEdit(id: string): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}/edit`);\r\n}\r\n\r\n/**\r\n * Create a new product\r\n */\r\nexport async function createProduct(\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  console.log(\"🌐 API createProduct called with data:\", productData);\r\n  console.log(\"🌐 API Base URL:\", API_BASE_URL);\r\n\r\n  try {\r\n    const result = await apiRequest<any>(\"/products\", {\r\n      method: \"POST\",\r\n      body: JSON.stringify(productData),\r\n    });\r\n    console.log(\"🌐 API createProduct response:\", result);\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"🌐 API createProduct error:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n/**\r\n * Update an existing product\r\n */\r\nexport async function updateProduct(\r\n  id: string,\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}`, {\r\n    method: \"PUT\",\r\n    body: JSON.stringify(productData),\r\n  });\r\n}\r\n\r\n/**\r\n * Update product with safe fields (PATCH)\r\n */\r\nexport async function updateProductSafe(\r\n  id: string,\r\n  productData: any\r\n): Promise<ApiResponse<any>> {\r\n  return apiRequest<any>(`/products/${id}/edit`, {\r\n    method: \"PATCH\",\r\n    body: JSON.stringify(productData),\r\n  });\r\n}\r\n\r\n/**\r\n * Delete a product\r\n */\r\nexport async function deleteProduct(id: string): Promise<ApiResponse<boolean>> {\r\n  return apiRequest<boolean>(`/products/${id}`, {\r\n    method: \"DELETE\",\r\n  });\r\n}\r\n\r\n/**\r\n * Health check for API connection\r\n */\r\nexport async function checkApiHealth(): Promise<boolean> {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/api`);\r\n    return response.ok;\r\n  } catch (error) {\r\n    console.error(\"API health check failed:\", error);\r\n    return false;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0BAA0B;;;;;;;;;;;AA6B1B,4DAA4D;AAC5D,MAAM,gBAAgB;IACpB,uCAAmC;;IAMnC;IACA,mDAAmD;IACnD,OAAO,6DAAmC;AAC5C;AAEA,MAAM,eAAe;AAErB;;CAEC,GACD,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,GAAG,aAAa,IAAI,EAAE,UAAU;IAE5C,MAAM,iBAA8B;QAClC,SAAS;YACP,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QACA,GAAG,OAAO;IACZ;IAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,KAAK;IAC9C,QAAQ,GAAG,CAAC,CAAC,mBAAmB,CAAC,EAAE;IAEnC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QACpD,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;QAE5C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,uBAAuB,CAAC,EAAE;YACzC,MAAM,IAAI,MACR,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW;QAEnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,CAAC,EAAE;QACjC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC,EAAE;QACxD,MAAM;IACR;AACF;AAKO,eAAe,YACpB,UAA0B,CAAC,CAAC;IAE5B,MAAM,eAAe,IAAI;IAEzB,+BAA+B;IAC/B,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAC,OAAS,aAAa,MAAM,CAAC,KAAK,KAAK,QAAQ;YAChE,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;YACzC;QACF;IACF;IAEA,MAAM,cAAc,aAAa,QAAQ;IACzC,MAAM,WAAW,cAAc,CAAC,UAAU,EAAE,aAAa,GAAG;IAE5D,OAAO,WAAkB;AAC3B;AAKO,eAAe,eAAe,EAAU;IAC7C,OAAO,WAAgB,CAAC,UAAU,EAAE,IAAI;AAC1C;AAKO,eAAe,kBAAkB,EAAU;IAChD,OAAO,WAAgB,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC;AAC/C;AAKO,eAAe,cACpB,WAAgB;IAEhB,QAAQ,GAAG,CAAC,0CAA0C;IACtD,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,IAAI;QACF,MAAM,SAAS,MAAM,WAAgB,aAAa;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAKO,eAAe,cACpB,EAAU,EACV,WAAgB;IAEhB,OAAO,WAAgB,CAAC,UAAU,EAAE,IAAI,EAAE;QACxC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe,kBACpB,EAAU,EACV,WAAgB;IAEhB,OAAO,WAAgB,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC,EAAE;QAC7C,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAKO,eAAe,cAAc,EAAU;IAC5C,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI,EAAE;QAC5C,QAAQ;IACV;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,IAAI,CAAC;QAClD,OAAO,SAAS,EAAE;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useProducts.ts"], "sourcesContent": ["/**\r\n * React hooks for product data management\r\n * Provides easy-to-use hooks for CRUD operations on products\r\n */\r\n\r\nimport { useCallback, useEffect, useState } from 'react';\r\nimport { toast } from 'sonner';\r\n\r\nimport {\r\n  createProduct,\r\n  deleteProduct,\r\n  getProductById,\r\n  getProductForEdit,\r\n  getProducts,\r\n  updateProduct,\r\n  updateProductSafe,\r\n  type ApiResponse,\r\n  type ProductFilters,\r\n} from '@/lib/api/products';\r\n\r\n// Hook state types\r\ninterface UseProductsState {\r\n  products: any[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  meta?: {\r\n    total: number;\r\n    page: number;\r\n    limit: number;\r\n    pages: number;\r\n  };\r\n}\r\n\r\ninterface UseProductState {\r\n  product: any | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\n/**\r\n * Hook for fetching and managing multiple products\r\n */\r\nexport function useProducts(initialFilters: ProductFilters = {}) {\r\n  const [state, setState] = useState<UseProductsState>({\r\n    products: [],\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  const [filters, setFilters] = useState<ProductFilters>(initialFilters);\r\n\r\n  const fetchProducts = useCallback(async (newFilters?: ProductFilters) => {\r\n    setState(prev => ({ ...prev, loading: true, error: null }));\r\n    \r\n    try {\r\n      const filtersToUse = newFilters || filters;\r\n      const response = await getProducts(filtersToUse);\r\n      \r\n      setState({\r\n        products: response.data,\r\n        loading: false,\r\n        error: null,\r\n        meta: response.meta,\r\n      });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch products';\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error('Failed to load products');\r\n    }\r\n  }, [filters]);\r\n\r\n  const updateFilters = useCallback((newFilters: ProductFilters) => {\r\n    setFilters(newFilters);\r\n    fetchProducts(newFilters);\r\n  }, [fetchProducts]);\r\n\r\n  const refreshProducts = useCallback(() => {\r\n    fetchProducts();\r\n  }, [fetchProducts]);\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchProducts();\r\n  }, [fetchProducts]);\r\n\r\n  return {\r\n    ...state,\r\n    filters,\r\n    updateFilters,\r\n    refreshProducts,\r\n    refetch: fetchProducts,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for fetching and managing a single product\r\n */\r\nexport function useProduct(id: string | null) {\r\n  const [state, setState] = useState<UseProductState>({\r\n    product: null,\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  const fetchProduct = useCallback(async () => {\r\n    if (!id) {\r\n      setState({ product: null, loading: false, error: null });\r\n      return;\r\n    }\r\n\r\n    setState(prev => ({ ...prev, loading: true, error: null }));\r\n    \r\n    try {\r\n      const response = await getProductById(id);\r\n      setState({\r\n        product: response.data,\r\n        loading: false,\r\n        error: null,\r\n      });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product';\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error('Failed to load product');\r\n    }\r\n  }, [id]);\r\n\r\n  useEffect(() => {\r\n    fetchProduct();\r\n  }, [fetchProduct]);\r\n\r\n  return {\r\n    ...state,\r\n    refetch: fetchProduct,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for product CRUD operations\r\n */\r\nexport function useProductMutations() {\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const createProductMutation = useCallback(async (productData: any) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await createProduct(productData);\r\n      toast.success('Product created successfully');\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateProductMutation = useCallback(async (id: string, productData: any) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await updateProduct(id, productData);\r\n      toast.success('Product updated successfully');\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateProductSafeMutation = useCallback(async (id: string, productData: any) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await updateProductSafe(id, productData);\r\n      toast.success('Product updated successfully');\r\n      return response.data;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const deleteProductMutation = useCallback(async (id: string) => {\r\n    setLoading(true);\r\n    try {\r\n      await deleteProduct(id);\r\n      toast.success('Product deleted successfully');\r\n      return true;\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete product';\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  return {\r\n    loading,\r\n    createProduct: createProductMutation,\r\n    updateProduct: updateProductMutation,\r\n    updateProductSafe: updateProductSafeMutation,\r\n    deleteProduct: deleteProductMutation,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for fetching product for editing\r\n */\r\nexport function useProductForEdit(id: string | null) {\r\n  const [state, setState] = useState<UseProductState & { editableFields?: string[] }>({\r\n    product: null,\r\n    loading: true,\r\n    error: null,\r\n    editableFields: [],\r\n  });\r\n\r\n  const fetchProductForEdit = useCallback(async () => {\r\n    if (!id) {\r\n      setState({ product: null, loading: false, error: null, editableFields: [] });\r\n      return;\r\n    }\r\n\r\n    setState(prev => ({ ...prev, loading: true, error: null }));\r\n    \r\n    try {\r\n      const response = await getProductForEdit(id);\r\n      setState({\r\n        product: response.data,\r\n        loading: false,\r\n        error: null,\r\n        editableFields: response.editableFields || [],\r\n      });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product for editing';\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: errorMessage,\r\n      }));\r\n      toast.error('Failed to load product for editing');\r\n    }\r\n  }, [id]);\r\n\r\n  useEffect(() => {\r\n    fetchProductForEdit();\r\n  }, [fetchProductForEdit]);\r\n\r\n  return {\r\n    ...state,\r\n    refetch: fetchProductForEdit,\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AACA;AAEA;;;;AAkCO,SAAS,YAAY,iBAAiC,CAAC,CAAC;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,UAAU,EAAE;QACZ,SAAS;QACT,OAAO;IACT;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,IAAI;YACF,MAAM,eAAe,cAAc;YACnC,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;YAEnC,SAAS;gBACP,UAAU,SAAS,IAAI;gBACvB,SAAS;gBACT,OAAO;gBACP,MAAM,SAAS,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,WAAW;QACX,cAAc;IAChB,GAAG;QAAC;KAAc;IAElB,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAClC;IACF,GAAG;QAAC;KAAc;IAElB,gBAAgB;IAChB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA,SAAS;IACX;AACF;AAKO,SAAS,WAAW,EAAiB;IAC1C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,SAAS;QACT,SAAS;QACT,OAAO;IACT;IAEA,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,IAAI;YACP,SAAS;gBAAE,SAAS;gBAAM,SAAS;gBAAO,OAAO;YAAK;YACtD;QACF;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;YACtC,SAAS;gBACP,SAAS,SAAS,IAAI;gBACtB,SAAS;gBACT,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAG;IAEP,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;AAKO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,wBAAwB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/C,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;YACrC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QAC3D,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;YACzC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,4BAA4B,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QAC/D,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI;YAC7C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/C,WAAW;QACX,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE;YACpB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,eAAe;IACjB;AACF;AAKO,SAAS,kBAAkB,EAAiB;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmD;QAClF,SAAS;QACT,SAAS;QACT,OAAO;QACP,gBAAgB,EAAE;IACpB;IAEA,MAAM,sBAAsB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,CAAC,IAAI;YACP,SAAS;gBAAE,SAAS;gBAAM,SAAS;gBAAO,OAAO;gBAAM,gBAAgB,EAAE;YAAC;YAC1E;QACF;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE;YACzC,SAAS;gBACP,SAAS,SAAS,IAAI;gBACtB,SAAS;gBACT,OAAO;gBACP,gBAAgB,SAAS,cAAc,IAAI,EAAE;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAG;IAEP,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAoB;IAExB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/categoryApi.ts"], "sourcesContent": ["import { Category } from \"@/components/pages/management/CategoryManager\";\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3011\";\n\nexport type CreateCategoryDto = {\n  name: string;\n  description?: string;\n  icon?: string;\n  color?: string;\n  isActive?: boolean;\n  parentId?: string;\n  sortOrder?: number;\n};\n\nexport type UpdateCategoryDto = Partial<CreateCategoryDto>;\n\nexport type CategoryFilters = {\n  isActive?: boolean;\n  parentId?: string;\n  search?: string;\n};\n\nexport type ApiResponse<T> = {\n  success: boolean;\n  data?: T;\n  message?: string;\n  count?: number;\n};\n\n/**\n * Category API service for frontend-backend communication\n */\nexport class CategoryApiService {\n  private static baseUrl = `${API_BASE_URL}/api/categories`;\n\n  /**\n   * Transform backend category to frontend format\n   */\n  private static transformCategory(backendCategory: any): Category {\n    console.log(\"Transforming category:\", backendCategory); // Debug log\n    const transformed = {\n      id: backendCategory._id || backendCategory.id,\n      name: backendCategory.name,\n      description: backendCategory.description,\n      slug: backendCategory.slug,\n      icon: backendCategory.icon,\n      color: backendCategory.color,\n      isActive: backendCategory.isActive,\n      productCount: backendCategory.productCount,\n      parentId: backendCategory.parentId,\n      sortOrder: backendCategory.sortOrder,\n      createdAt: backendCategory.createdAt,\n      updatedAt: backendCategory.updatedAt,\n    };\n    console.log(\"Transformed category:\", transformed); // Debug log\n    return transformed;\n  }\n\n  /**\n   * Get all categories with optional filtering\n   */\n  static async getCategories(filters?: CategoryFilters): Promise<Category[]> {\n    try {\n      const params = new URLSearchParams();\n\n      if (filters?.isActive !== undefined) {\n        params.append(\"isActive\", filters.isActive.toString());\n      }\n      if (filters?.parentId) {\n        params.append(\"parentId\", filters.parentId);\n      }\n      if (filters?.search) {\n        params.append(\"search\", filters.search);\n      }\n\n      const url = `${this.baseUrl}${params.toString() ? `?${params.toString()}` : \"\"}`;\n      const response = await fetch(url);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any[]> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch categories\");\n      }\n\n      const categories = (result.data || []).map(this.transformCategory);\n      return categories;\n    } catch (error) {\n      console.error(\"Error fetching categories:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a category by ID\n   */\n  static async getCategoryById(id: string): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/${id}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"Category not found\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error fetching category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a category by slug\n   */\n  static async getCategoryBySlug(slug: string): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/slug/${slug}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"Category not found\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error fetching category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a new category\n   */\n  static async createCategory(\n    categoryData: CreateCategoryDto\n  ): Promise<Category> {\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(categoryData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to create category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"No category data returned\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error creating category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update a category\n   */\n  static async updateCategory(\n    id: string,\n    updateData: UpdateCategoryDto\n  ): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/${id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to update category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"No category data returned\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error updating category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a category\n   */\n  static async deleteCategory(id: string): Promise<void> {\n    try {\n      console.log(\"Deleting category with ID:\", id); // Debug log\n      if (!id || id === \"undefined\") {\n        throw new Error(\"Category ID is required for deletion\");\n      }\n\n      const response = await fetch(`${this.baseUrl}/${id}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<null> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to delete category\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Recalculate product counts for all categories\n   */\n  static async recalculateProductCounts(): Promise<void> {\n    try {\n      const response = await fetch(`${this.baseUrl}/recalculate-counts`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(\n          result.message || \"Failed to recalculate product counts\"\n        );\n      }\n    } catch (error) {\n      console.error(\"Error recalculating product counts:\", error);\n      throw error;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEA,MAAM,eAAe,6DAAmC;AA8BjD,MAAM;IACX,OAAe,UAAU,GAAG,aAAa,eAAe,CAAC,CAAC;IAE1D;;GAEC,GACD,OAAe,kBAAkB,eAAoB,EAAY;QAC/D,QAAQ,GAAG,CAAC,0BAA0B,kBAAkB,YAAY;QACpE,MAAM,cAAc;YAClB,IAAI,gBAAgB,GAAG,IAAI,gBAAgB,EAAE;YAC7C,MAAM,gBAAgB,IAAI;YAC1B,aAAa,gBAAgB,WAAW;YACxC,MAAM,gBAAgB,IAAI;YAC1B,MAAM,gBAAgB,IAAI;YAC1B,OAAO,gBAAgB,KAAK;YAC5B,UAAU,gBAAgB,QAAQ;YAClC,cAAc,gBAAgB,YAAY;YAC1C,UAAU,gBAAgB,QAAQ;YAClC,WAAW,gBAAgB,SAAS;YACpC,WAAW,gBAAgB,SAAS;YACpC,WAAW,gBAAgB,SAAS;QACtC;QACA,QAAQ,GAAG,CAAC,yBAAyB,cAAc,YAAY;QAC/D,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,cAAc,OAAyB,EAAuB;QACzE,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,SAAS,aAAa,WAAW;gBACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;YACrD;YACA,IAAI,SAAS,UAAU;gBACrB,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YAC5C;YACA,IAAI,SAAS,QAAQ;gBACnB,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACxC;YAEA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YAChF,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,MAAM,aAAa,CAAC,OAAO,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,iBAAiB;YACjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,gBAAgB,EAAU,EAAqB;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI;YAEpD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,kBAAkB,IAAY,EAAqB;QAC9D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM;YAE3D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,YAA+B,EACZ;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,EAAU,EACV,UAA6B,EACV;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,EAAU,EAAiB;QACrD,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B,KAAK,YAAY;YAC3D,IAAI,CAAC,MAAM,OAAO,aAAa;gBAC7B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;gBACpD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA4B,MAAM,SAAS,IAAI;YAErD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,2BAA0C;QACrD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MACR,OAAO,OAAO,IAAI;YAEtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useCategories.ts"], "sourcesContent": ["/**\n * React hooks for category data management\n * Provides easy-to-use hooks for CRUD operations on categories\n */\n\nimport { useCallback, useEffect, useState } from 'react';\nimport { toast } from 'sonner';\n\nimport { Category } from '@/components/pages/management/CategoryManager';\nimport {\n  CategoryApiService,\n  type CategoryFilters,\n  type CreateCategoryDto,\n  type UpdateCategoryDto,\n} from '@/lib/api/categoryApi';\n\n// Hook state types\ninterface UseCategoriesState {\n  categories: Category[];\n  loading: boolean;\n  error: string | null;\n}\n\ninterface UseCategoryState {\n  category: Category | null;\n  loading: boolean;\n  error: string | null;\n}\n\n/**\n * Hook for fetching and managing categories list\n */\nexport function useCategories(initialFilters: CategoryFilters = {}) {\n  const [state, setState] = useState<UseCategoriesState>({\n    categories: [],\n    loading: true,\n    error: null,\n  });\n\n  const [filters, setFilters] = useState<CategoryFilters>(initialFilters);\n\n  const fetchCategories = useCallback(async (newFilters?: CategoryFilters) => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n    \n    try {\n      const filtersToUse = newFilters || filters;\n      const categories = await CategoryApiService.getCategories(filtersToUse);\n      \n      setState({\n        categories,\n        loading: false,\n        error: null,\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch categories';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      toast.error('Failed to load categories');\n    }\n  }, [filters]);\n\n  // Initial fetch\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  const updateFilters = useCallback((newFilters: CategoryFilters) => {\n    setFilters(newFilters);\n    fetchCategories(newFilters);\n  }, [fetchCategories]);\n\n  return {\n    ...state,\n    filters,\n    updateFilters,\n    refetch: fetchCategories,\n  };\n}\n\n/**\n * Hook for fetching and managing a single category\n */\nexport function useCategory(id: string | null) {\n  const [state, setState] = useState<UseCategoryState>({\n    category: null,\n    loading: true,\n    error: null,\n  });\n\n  const fetchCategory = useCallback(async () => {\n    if (!id) {\n      setState({ category: null, loading: false, error: null });\n      return;\n    }\n\n    setState(prev => ({ ...prev, loading: true, error: null }));\n    \n    try {\n      const category = await CategoryApiService.getCategoryById(id);\n      setState({\n        category,\n        loading: false,\n        error: null,\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch category';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      toast.error('Failed to load category');\n    }\n  }, [id]);\n\n  useEffect(() => {\n    fetchCategory();\n  }, [fetchCategory]);\n\n  return {\n    ...state,\n    refetch: fetchCategory,\n  };\n}\n\n/**\n * Hook for category CRUD operations\n */\nexport function useCategoryMutations() {\n  const [loading, setLoading] = useState(false);\n\n  const createCategory = useCallback(async (categoryData: CreateCategoryDto) => {\n    setLoading(true);\n    try {\n      const category = await CategoryApiService.createCategory(categoryData);\n      toast.success('Category created successfully');\n      return category;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const updateCategory = useCallback(async (id: string, updateData: UpdateCategoryDto) => {\n    setLoading(true);\n    try {\n      const category = await CategoryApiService.updateCategory(id, updateData);\n      toast.success('Category updated successfully');\n      return category;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const deleteCategory = useCallback(async (id: string) => {\n    setLoading(true);\n    try {\n      await CategoryApiService.deleteCategory(id);\n      toast.success('Category deleted successfully');\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    createCategory,\n    updateCategory,\n    deleteCategory,\n    loading,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AAGA;;;;AAuBO,SAAS,cAAc,iBAAkC,CAAC,CAAC;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,YAAY,EAAE;QACd,SAAS;QACT,OAAO;IACT;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAExD,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,IAAI;YACF,MAAM,eAAe,cAAc;YACnC,MAAM,aAAa,MAAM,yHAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;YAE1D,SAAS;gBACP;gBACA,SAAS;gBACT,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAQ;IAEZ,gBAAgB;IAChB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,WAAW;QACX,gBAAgB;IAClB,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA,SAAS;IACX;AACF;AAKO,SAAS,YAAY,EAAiB;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,UAAU;QACV,SAAS;QACT,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,IAAI;YACP,SAAS;gBAAE,UAAU;gBAAM,SAAS;gBAAO,OAAO;YAAK;YACvD;QACF;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YAC1D,SAAS;gBACP;gBACA,SAAS;gBACT,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAG;IAEP,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;AAKO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACzD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QACpD,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,IAAI;YAC7D,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,WAAW;QACX,IAAI;YACF,MAAM,yHAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACxC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductFilter.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  Download,\r\n  Filter,\r\n  Grid3X3,\r\n  List,\r\n  RefreshCw,\r\n  Search,\r\n} from \"lucide-react\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { useBrands } from \"@/hooks/useBrands\";\r\nimport { useCategories } from \"@/hooks/useCategories\";\r\nimport { ProductFilters } from \"@/lib/api/products\";\r\n\r\ninterface ProductFilterProps {\r\n  onFiltersChange?: (filters: ProductFilters) => void;\r\n  onRefresh?: () => void;\r\n  loading?: boolean;\r\n  initialFilters?: ProductFilters;\r\n}\r\n\r\nexport const ProductFilter = ({\r\n  onFiltersChange,\r\n  onRefresh,\r\n  loading = false,\r\n  initialFilters = {},\r\n}: ProductFilterProps) => {\r\n  // Fetch real categories from the database\r\n  const { categories: dbCategories, loading: categoriesLoading } =\r\n    useCategories();\r\n\r\n  const [filters, setFilters] = useState<ProductFilters>({\r\n    search: \"\",\r\n    category: \"\",\r\n    brand: \"\",\r\n    status: \"\",\r\n    sortBy: \"createdAt\",\r\n    sortOrder: \"desc\",\r\n    ...initialFilters,\r\n  });\r\n\r\n  const updateFilters = useCallback(\r\n    (newFilters: Partial<ProductFilters>) => {\r\n      const updatedFilters = { ...filters, ...newFilters };\r\n      setFilters(updatedFilters);\r\n      onFiltersChange?.(updatedFilters);\r\n    },\r\n    [filters, onFiltersChange]\r\n  );\r\n\r\n  const handleSearchChange = useCallback(\r\n    (value: string) => {\r\n      updateFilters({ search: value });\r\n    },\r\n    [updateFilters]\r\n  );\r\n\r\n  const handleCategoryChange = useCallback(\r\n    (value: string) => {\r\n      updateFilters({ category: value === \"all\" ? \"\" : value });\r\n    },\r\n    [updateFilters]\r\n  );\r\n\r\n  const handleBrandChange = useCallback(\r\n    (value: string) => {\r\n      updateFilters({ brand: value === \"all\" ? \"\" : value });\r\n    },\r\n    [updateFilters]\r\n  );\r\n\r\n  const handleStatusChange = useCallback(\r\n    (value: string) => {\r\n      updateFilters({ status: value === \"all\" ? \"\" : value });\r\n    },\r\n    [updateFilters]\r\n  );\r\n\r\n  const handleSortChange = useCallback(\r\n    (value: string) => {\r\n      const [sortBy, sortOrder] = value.split(\"-\");\r\n      updateFilters({\r\n        sortBy: sortBy as \"price\" | \"createdAt\" | \"name\",\r\n        sortOrder: sortOrder as \"asc\" | \"desc\",\r\n      });\r\n    },\r\n    [updateFilters]\r\n  );\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Search and Quick Actions */}\r\n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n        <div className=\"relative max-w-md flex-1\">\r\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n          <Input\r\n            placeholder=\"Search products by name, brand, or category...\"\r\n            className=\"pl-10\"\r\n            value={filters.search}\r\n            onChange={(e) => handleSearchChange(e.target.value)}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex gap-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={onRefresh}\r\n            disabled={loading}\r\n          >\r\n            <RefreshCw\r\n              className={`mr-2 h-4 w-4 ${loading ? \"animate-spin\" : \"\"}`}\r\n            />\r\n            Refresh\r\n          </Button>\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <Download className=\"mr-2 h-4 w-4\" />\r\n            Export\r\n          </Button>\r\n          <div className=\"flex rounded-md border\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"rounded-r-none border-r\"\r\n            >\r\n              <Grid3X3 className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"rounded-l-none\">\r\n              <List className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"flex flex-wrap gap-4\">\r\n        <Select\r\n          value={filters.category || \"all\"}\r\n          onValueChange={handleCategoryChange}\r\n        >\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <Filter className=\"mr-2 h-4 w-4\" />\r\n            <SelectValue placeholder=\"Category\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Categories</SelectItem>\r\n            {categoriesLoading ? (\r\n              <SelectItem value=\"loading\" disabled>\r\n                Loading categories...\r\n              </SelectItem>\r\n            ) : (\r\n              dbCategories.map((category) => (\r\n                <SelectItem key={category.id} value={category.name}>\r\n                  {category.icon} {category.name}\r\n                </SelectItem>\r\n              ))\r\n            )}\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select\r\n          value={filters.brand || \"all\"}\r\n          onValueChange={handleBrandChange}\r\n        >\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <SelectValue placeholder=\"Brand\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Brands</SelectItem>\r\n            {brandOptions.map((option) => (\r\n              <SelectItem key={option.value} value={option.value}>\r\n                {option.label}\r\n              </SelectItem>\r\n            ))}\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select\r\n          value={filters.status || \"all\"}\r\n          onValueChange={handleStatusChange}\r\n        >\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <SelectValue placeholder=\"Status\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Status</SelectItem>\r\n            <SelectItem value=\"in-stock\">In Stock</SelectItem>\r\n            <SelectItem value=\"out-of-stock\">Out of Stock</SelectItem>\r\n            <SelectItem value=\"draft\">Draft</SelectItem>\r\n            <SelectItem value=\"archived\">Archived</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select\r\n          value={`${filters.sortBy}-${filters.sortOrder}`}\r\n          onValueChange={handleSortChange}\r\n        >\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <SelectValue placeholder=\"Sort by\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"createdAt-desc\">Newest First</SelectItem>\r\n            <SelectItem value=\"createdAt-asc\">Oldest First</SelectItem>\r\n            <SelectItem value=\"name-asc\">Name A-Z</SelectItem>\r\n            <SelectItem value=\"name-desc\">Name Z-A</SelectItem>\r\n            <SelectItem value=\"price-asc\">Price Low-High</SelectItem>\r\n            <SelectItem value=\"price-desc\">Price High-Low</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AAQA;AAvBA;;;;;;;;AAiCO,MAAM,gBAAgB,CAAC,EAC5B,eAAe,EACf,SAAS,EACT,UAAU,KAAK,EACf,iBAAiB,CAAC,CAAC,EACA;IACnB,0CAA0C;IAC1C,MAAM,EAAE,YAAY,YAAY,EAAE,SAAS,iBAAiB,EAAE,GAC5D,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD;IAEd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;QACrD,QAAQ;QACR,UAAU;QACV,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,GAAG,cAAc;IACnB;IAEA,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,MAAM,iBAAiB;YAAE,GAAG,OAAO;YAAE,GAAG,UAAU;QAAC;QACnD,WAAW;QACX,kBAAkB;IACpB,GACA;QAAC;QAAS;KAAgB;IAG5B,MAAM,qBAAqB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EACnC,CAAC;QACC,cAAc;YAAE,QAAQ;QAAM;IAChC,GACA;QAAC;KAAc;IAGjB,MAAM,uBAAuB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EACrC,CAAC;QACC,cAAc;YAAE,UAAU,UAAU,QAAQ,KAAK;QAAM;IACzD,GACA;QAAC;KAAc;IAGjB,MAAM,oBAAoB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,cAAc;YAAE,OAAO,UAAU,QAAQ,KAAK;QAAM;IACtD,GACA;QAAC;KAAc;IAGjB,MAAM,qBAAqB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EACnC,CAAC;QACC,cAAc;YAAE,QAAQ,UAAU,QAAQ,KAAK;QAAM;IACvD,GACA;QAAC;KAAc;IAGjB,MAAM,mBAAmB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EACjC,CAAC;QACC,MAAM,CAAC,QAAQ,UAAU,GAAG,MAAM,KAAK,CAAC;QACxC,cAAc;YACZ,QAAQ;YACR,WAAW;QACb;IACF,GACA;QAAC;KAAc;IAEjB,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6WAAC,0HAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,WAAU;gCACV,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kCAItD,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,6WAAC,oSAAA,CAAA,YAAS;wCACR,WAAW,CAAC,aAAa,EAAE,UAAU,iBAAiB,IAAI;;;;;;oCAC1D;;;;;;;0CAGJ,6WAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,6WAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,6WAAC,gSAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAC5C,cAAA,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,2HAAA,CAAA,SAAM;wBACL,OAAO,QAAQ,QAAQ,IAAI;wBAC3B,eAAe;;0CAEf,6WAAC,2HAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC,2HAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;;0CAE3B,6WAAC,2HAAA,CAAA,gBAAa;;kDACZ,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;oCACvB,kCACC,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;wCAAU,QAAQ;kDAAC;;;;;+CAIrC,aAAa,GAAG,CAAC,CAAC,yBAChB,6WAAC,2HAAA,CAAA,aAAU;4CAAmB,OAAO,SAAS,IAAI;;gDAC/C,SAAS,IAAI;gDAAC;gDAAE,SAAS,IAAI;;2CADf,SAAS,EAAE;;;;;;;;;;;;;;;;;kCAQpC,6WAAC,2HAAA,CAAA,SAAM;wBACL,OAAO,QAAQ,KAAK,IAAI;wBACxB,eAAe;;0CAEf,6WAAC,2HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6WAAC,2HAAA,CAAA,gBAAa;;kDACZ,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;oCACvB,aAAa,GAAG,CAAC,CAAC,uBACjB,6WAAC,2HAAA,CAAA,aAAU;4CAAoB,OAAO,OAAO,KAAK;sDAC/C,OAAO,KAAK;2CADE,OAAO,KAAK;;;;;;;;;;;;;;;;;kCAOnC,6WAAC,2HAAA,CAAA,SAAM;wBACL,OAAO,QAAQ,MAAM,IAAI;wBACzB,eAAe;;0CAEf,6WAAC,2HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6WAAC,2HAAA,CAAA,gBAAa;;kDACZ,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAe;;;;;;kDACjC,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAQ;;;;;;kDAC1B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;;;;;;;;;;;;;kCAIjC,6WAAC,2HAAA,CAAA,SAAM;wBACL,OAAO,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE;wBAC/C,eAAe;;0CAEf,6WAAC,2HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6WAAC,2HAAA,CAAA,gBAAa;;kDACZ,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAiB;;;;;;kDACnC,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAgB;;;;;;kDAClC,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,6WAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C", "debugId": null}}, {"offset": {"line": 1758, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,2KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6WAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\n\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAEA;AACA;AAEA;AAPA;;;;;;AASA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,+QAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,+QAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC;;0BACC,6WAAC;;;;;0BACD,6WAAC,+QAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6WAAC,+QAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6WAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6WAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,+QAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1978, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/confirmation-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { AlertTriangle, Loader2 } from \"lucide-react\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\n\r\ninterface ConfirmationDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  title: string;\r\n  description: string;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  onConfirm: () => void | Promise<void>;\r\n  loading?: boolean;\r\n  variant?: \"default\" | \"destructive\";\r\n}\r\n\r\nexport function ConfirmationDialog({\r\n  open,\r\n  onOpenChange,\r\n  title,\r\n  description,\r\n  confirmText = \"Confirm\",\r\n  cancelText = \"Cancel\",\r\n  onConfirm,\r\n  loading = false,\r\n  variant = \"default\",\r\n}: ConfirmationDialogProps) {\r\n  const handleConfirm = async () => {\r\n    await onConfirm();\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <div className=\"flex items-center gap-3\">\r\n            {variant === \"destructive\" && (\r\n              <div className=\"flex h-10 w-10 items-center justify-center rounded-full bg-red-100\">\r\n                <AlertTriangle className=\"h-5 w-5 text-red-600\" />\r\n              </div>\r\n            )}\r\n            <div>\r\n              <DialogTitle className=\"text-left\">{title}</DialogTitle>\r\n              <DialogDescription className=\"text-left\">\r\n                {description}\r\n              </DialogDescription>\r\n            </div>\r\n          </div>\r\n        </DialogHeader>\r\n        <DialogFooter className=\"flex gap-2 sm:gap-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => onOpenChange(false)}\r\n            disabled={loading}\r\n          >\r\n            {cancelText}\r\n          </Button>\r\n          <Button\r\n            variant={variant === \"destructive\" ? \"destructive\" : \"default\"}\r\n            onClick={handleConfirm}\r\n            disabled={loading}\r\n          >\r\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\r\n            {confirmText}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAEA;AACA;AAPA;;;;;AA4BO,SAAS,mBAAmB,EACjC,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,SAAS,EACT,UAAU,KAAK,EACf,UAAU,SAAS,EACK;IACxB,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,6WAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6WAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6WAAC,2HAAA,CAAA,eAAY;8BACX,cAAA,6WAAC;wBAAI,WAAU;;4BACZ,YAAY,+BACX,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,4SAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAG7B,6WAAC;;kDACC,6WAAC,2HAAA,CAAA,cAAW;wCAAC,WAAU;kDAAa;;;;;;kDACpC,6WAAC,2HAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC1B;;;;;;;;;;;;;;;;;;;;;;;8BAKT,6WAAC,2HAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6WAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;sCAET;;;;;;sCAEH,6WAAC,2HAAA,CAAA,SAAM;4BACL,SAAS,YAAY,gBAAgB,gBAAgB;4BACrD,SAAS;4BACT,UAAU;;gCAET,yBAAW,6WAAC,qSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 2110, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\n\r\nimport clsx from \"clsx\";\r\nimport { Edit, Eye, MoreHorizontal, Package, Star, Trash2 } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { ConfirmationDialog } from \"@/components/ui/confirmation-dialog\";\r\nimport { useProductMutations } from \"@/hooks/useProducts\";\r\n\r\ntype Product = {\r\n  _id?: string;\r\n  id?: number | string;\r\n  name: string;\r\n  price: number | string;\r\n  originalPrice?: number | string;\r\n  mainImage?: string;\r\n  image?: string;\r\n  category?: string;\r\n  brand?: string;\r\n  stock?: number;\r\n  status?:\r\n    | \"in-stock\"\r\n    | \"out-of-stock\"\r\n    | \"coming-soon\"\r\n    | \"archived\"\r\n    | \"draft\"\r\n    | \"suspended\"\r\n    | \"active\";\r\n  averageRating?: number;\r\n  rating?: number;\r\n  isOnSale?: boolean;\r\n  currency?: string;\r\n  saleEndsAt?: string | Date;\r\n};\r\n\r\nexport const ProductCard = ({\r\n  product,\r\n  index,\r\n  onDelete,\r\n}: {\r\n  product: Product;\r\n  index: number;\r\n  onDelete?: () => void;\r\n}) => {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const router = useRouter();\r\n  const { deleteProduct } = useProductMutations();\r\n\r\n  // Handle both backend (_id) and frontend (id) identifiers\r\n  const productId = product._id || product.id;\r\n\r\n  // Default values for optional properties\r\n  const stock = product.stock ?? 0;\r\n  const status = product.status ?? \"active\";\r\n  const rating = product.averageRating || product.rating ?? 0;\r\n  const isOnSale = product.isOnSale ?? (product.saleEndsAt ? new Date(product.saleEndsAt) > new Date() : false);\r\n\r\n  // Handle image - backend uses mainImage, frontend uses image\r\n  const productImage = product.mainImage || product.image || '/images/placeholder.jpg';\r\n\r\n  // Format price with currency\r\n  const formatPrice = (price: number | string, currency?: string) => {\r\n    const numPrice = typeof price === 'string' ? parseFloat(price) : price;\r\n    const currencySymbol = currency === 'EUR' ? '€' : '$';\r\n    return `${currencySymbol}${numPrice.toFixed(2)}`;\r\n  };\r\n\r\n  const handleViewProduct = () => {\r\n    router.push(`/admin/products/${productId}`);\r\n  };\r\n\r\n  const handleEditProduct = () => {\r\n    // Navigate to product details page - edit mode will be handled there\r\n    router.push(`/admin/products/${productId}?edit=true`);\r\n  };\r\n\r\n  const handleDeleteClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmDelete = async () => {\r\n    if (!productId) {\r\n      toast.error(\"Product ID not found\");\r\n      setShowDeleteDialog(false);\r\n      return;\r\n    }\r\n\r\n    setIsDeleting(true);\r\n    try {\r\n      await deleteProduct(productId.toString());\r\n      // Call the onDelete callback to refresh the product list\r\n      onDelete?.();\r\n      setShowDeleteDialog(false);\r\n    } catch (error) {\r\n      console.error(\"Failed to delete product:\", error);\r\n      // Error toast is already handled in the mutation hook\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case \"active\":\r\n      case \"in-stock\":\r\n        return \"bg-green-100 text-green-700\";\r\n      case \"draft\":\r\n      case \"coming-soon\":\r\n        return \"bg-yellow-100 text-yellow-700\";\r\n      case \"archived\":\r\n      case \"suspended\":\r\n        return \"bg-gray-100 text-gray-700\";\r\n      case \"out-of-stock\":\r\n        return \"bg-red-100 text-red-700\";\r\n      default:\r\n        return \"bg-gray-100 text-gray-700\";\r\n    }\r\n  };\r\n\r\n  const getStockStatus = (stock: number) => {\r\n    if (stock === 0) return { text: \"Out of Stock\", color: \"text-red-600\" };\r\n    if (stock < 10) return { text: \"Low Stock\", color: \"text-orange-600\" };\r\n    return { text: \"In Stock\", color: \"text-green-600\" };\r\n  };\r\n\r\n  const stockStatus = getStockStatus(stock);\r\n\r\n  return (\r\n    <Card className=\"group relative w-full overflow-hidden p-3 shadow-sm transition-all hover:shadow-lg\">\r\n      {/* Status Badge */}\r\n      {status !== \"active\" && (\r\n        <Badge\r\n          className={`absolute left-2 top-2 z-10 text-xs ${getStatusColor(status)}`}\r\n        >\r\n          {status.charAt(0).toUpperCase() + status.slice(1)}\r\n        </Badge>\r\n      )}\r\n\r\n      {/* Sale Badge */}\r\n      {isOnSale && (\r\n        <Badge className=\"absolute right-2 top-2 z-10 bg-red-500 text-white\">\r\n          Sale\r\n        </Badge>\r\n      )}\r\n\r\n      {/* Product Image */}\r\n      <div\r\n        className=\"relative mb-3 h-52 w-full cursor-pointer overflow-hidden rounded-md\"\r\n        onClick={handleViewProduct}\r\n      >\r\n        {isLoading && (\r\n          <Skeleton className=\"absolute inset-0 size-full rounded-md\" />\r\n        )}\r\n\r\n        <Image\r\n          src={productImage}\r\n          alt={product.name}\r\n          fill\r\n          priority={index === 0}\r\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 20vw\"\r\n          onLoad={() => setIsLoading(false)}\r\n          onError={() => setIsLoading(false)}\r\n          className={clsx(\r\n            \"rounded object-cover object-center transition-all duration-300 group-hover:scale-105\",\r\n            isLoading ? \"opacity-0\" : \"opacity-100\"\r\n          )}\r\n        />\r\n\r\n        {/* Quick Actions Overlay */}\r\n        <div className=\"absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity group-hover:opacity-100\">\r\n          <Button\r\n            size=\"sm\"\r\n            variant=\"secondary\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              handleViewProduct();\r\n            }}\r\n          >\r\n            <Eye className=\"mr-2 h-4 w-4\" />\r\n            View\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      <CardContent className=\"p-2\">\r\n        {/* Product Info */}\r\n        <div className=\"mb-2\">\r\n          <Link\r\n            href={`/admin/products/${productId}`}\r\n            className=\"text-sm font-medium hover:text-blue-600 hover:underline\"\r\n          >\r\n            {product.name}\r\n          </Link>\r\n          {product.category && (\r\n            <div className=\"text-xs text-gray-500\">{product.category}</div>\r\n          )}\r\n          {product.brand && (\r\n            <div className=\"text-xs text-gray-400\">{product.brand}</div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Rating */}\r\n        {rating > 0 && (\r\n          <div className=\"mb-2 flex items-center gap-1\">\r\n            <div className=\"flex\">\r\n              {[...Array(5)].map((_, i) => (\r\n                <Star\r\n                  key={i}\r\n                  className={clsx(\r\n                    \"h-3 w-3\",\r\n                    i < rating\r\n                      ? \"fill-yellow-400 text-yellow-400\"\r\n                      : \"text-gray-300\"\r\n                  )}\r\n                />\r\n              ))}\r\n            </div>\r\n            <span className=\"text-xs text-gray-500\">({rating})</span>\r\n          </div>\r\n        )}\r\n\r\n        {/* Price */}\r\n        <div className=\"mb-2 flex items-center gap-2\">\r\n          <span className=\"font-semibold text-gray-900\">\r\n            {formatPrice(product.price, product.currency)}\r\n          </span>\r\n          {product.originalPrice && isOnSale && (\r\n            <span className=\"text-sm text-gray-500 line-through\">\r\n              {formatPrice(product.originalPrice, product.currency)}\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {/* Stock Status */}\r\n        <div className=\"mb-3 flex items-center gap-1\">\r\n          <Package className=\"h-3 w-3 text-gray-400\" />\r\n          <span className={`text-xs ${stockStatus.color}`}>\r\n            {stockStatus.text} {stock > 0 && `(${stock})`}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Actions */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex gap-1\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"h-8 px-2 text-xs\"\r\n              onClick={handleEditProduct}\r\n            >\r\n              <Edit className=\"mr-1 h-3 w-3\" />\r\n              Edit\r\n            </Button>\r\n          </div>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n                <MoreHorizontal className=\"h-4 w-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\">\r\n              <DropdownMenuItem onClick={handleViewProduct}>\r\n                <Eye className=\"mr-2 h-4 w-4\" />\r\n                View Details\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleEditProduct}>\r\n                <Edit className=\"mr-2 h-4 w-4\" />\r\n                Edit Product\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                className=\"text-red-600\"\r\n                onClick={handleDeleteClick}\r\n                disabled={isDeleting}\r\n              >\r\n                <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                Delete Product\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </CardContent>\r\n\r\n      {/* Delete Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        open={showDeleteDialog}\r\n        onOpenChange={setShowDeleteDialog}\r\n        title=\"Delete Product\"\r\n        description={`Are you sure you want to delete \"${product.name}\"? This action cannot be undone and will permanently remove the product from your inventory.`}\r\n        confirmText={isDeleting ? \"Deleting...\" : \"Delete Product\"}\r\n        cancelText=\"Cancel\"\r\n        onConfirm={handleConfirmDelete}\r\n        loading={isDeleting}\r\n        variant=\"destructive\"\r\n      />\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AAvBA;;;;;;;;;;;;;;;;AAmDO,MAAM,cAAc,CAAC,EAC1B,OAAO,EACP,KAAK,EACL,QAAQ,EAKT;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD;IAE5C,0DAA0D;IAC1D,MAAM,YAAY,QAAQ,GAAG,IAAI,QAAQ,EAAE;IAE3C,yCAAyC;IACzC,MAAM,QAAQ,QAAQ,KAAK,IAAI;IAC/B,MAAM,SAAS,QAAQ,MAAM,IAAI;IACjC,MAAM,SAAS,CAAA,QAAQ,aAAa,IAAI,QAAQ,MAAM,AAAD,KAAK;IAC1D,MAAM,WAAW,QAAQ,QAAQ,IAAI,CAAC,QAAQ,UAAU,GAAG,IAAI,KAAK,QAAQ,UAAU,IAAI,IAAI,SAAS,KAAK;IAE5G,6DAA6D;IAC7D,MAAM,eAAe,QAAQ,SAAS,IAAI,QAAQ,KAAK,IAAI;IAE3D,6BAA6B;IAC7B,MAAM,cAAc,CAAC,OAAwB;QAC3C,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;QACjE,MAAM,iBAAiB,aAAa,QAAQ,MAAM;QAClD,OAAO,GAAG,iBAAiB,SAAS,OAAO,CAAC,IAAI;IAClD;IAEA,MAAM,oBAAoB;QACxB,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,WAAW;IAC5C;IAEA,MAAM,oBAAoB;QACxB,qEAAqE;QACrE,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,UAAU,UAAU,CAAC;IACtD;IAEA,MAAM,oBAAoB;QACxB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,WAAW;YACd,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,oBAAoB;YACpB;QACF;QAEA,cAAc;QACd,IAAI;YACF,MAAM,cAAc,UAAU,QAAQ;YACtC,yDAAyD;YACzD;YACA,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,sDAAsD;QACxD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;YAAE,MAAM;YAAgB,OAAO;QAAe;QACtE,IAAI,QAAQ,IAAI,OAAO;YAAE,MAAM;YAAa,OAAO;QAAkB;QACrE,OAAO;YAAE,MAAM;YAAY,OAAO;QAAiB;IACrD;IAEA,MAAM,cAAc,eAAe;IAEnC,qBACE,6WAAC,yHAAA,CAAA,OAAI;QAAC,WAAU;;YAEb,WAAW,0BACV,6WAAC,0HAAA,CAAA,QAAK;gBACJ,WAAW,CAAC,mCAAmC,EAAE,eAAe,SAAS;0BAExE,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;YAKlD,0BACC,6WAAC,0HAAA,CAAA,QAAK;gBAAC,WAAU;0BAAoD;;;;;;0BAMvE,6WAAC;gBACC,WAAU;gBACV,SAAS;;oBAER,2BACC,6WAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCAGtB,6WAAC,4PAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,UAAU,UAAU;wBACpB,OAAM;wBACN,QAAQ,IAAM,aAAa;wBAC3B,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAA,GAAA,sLAAA,CAAA,UAAI,AAAD,EACZ,wFACA,YAAY,cAAc;;;;;;kCAK9B,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,2HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;;8CAEA,6WAAC,oRAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAMtC,6WAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,2RAAA,CAAA,UAAI;gCACH,MAAM,CAAC,gBAAgB,EAAE,WAAW;gCACpC,WAAU;0CAET,QAAQ,IAAI;;;;;;4BAEd,QAAQ,QAAQ,kBACf,6WAAC;gCAAI,WAAU;0CAAyB,QAAQ,QAAQ;;;;;;4BAEzD,QAAQ,KAAK,kBACZ,6WAAC;gCAAI,WAAU;0CAAyB,QAAQ,KAAK;;;;;;;;;;;;oBAKxD,SAAS,mBACR,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6WAAC,sRAAA,CAAA,OAAI;wCAEH,WAAW,CAAA,GAAA,sLAAA,CAAA,UAAI,AAAD,EACZ,WACA,IAAI,SACA,oCACA;uCALD;;;;;;;;;;0CAUX,6WAAC;gCAAK,WAAU;;oCAAwB;oCAAE;oCAAO;;;;;;;;;;;;;kCAKrD,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAK,WAAU;0CACb,YAAY,QAAQ,KAAK,EAAE,QAAQ,QAAQ;;;;;;4BAE7C,QAAQ,aAAa,IAAI,0BACxB,6WAAC;gCAAK,WAAU;0CACb,YAAY,QAAQ,aAAa,EAAE,QAAQ,QAAQ;;;;;;;;;;;;kCAM1D,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,4RAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6WAAC;gCAAK,WAAW,CAAC,QAAQ,EAAE,YAAY,KAAK,EAAE;;oCAC5C,YAAY,IAAI;oCAAC;oCAAE,QAAQ,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;;;;;;;;;;;;;kCAKjD,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,2HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;;sDAET,6WAAC,+RAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAKrC,6WAAC,qIAAA,CAAA,eAAY;;kDACX,6WAAC,qIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,6WAAC,oSAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG9B,6WAAC,qIAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,6WAAC,qIAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,6WAAC,qIAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,6WAAC,+RAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;0DACtB,6WAAC,qIAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS;gDACT,UAAU;;kEAEV,6WAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,6WAAC,2IAAA,CAAA,qBAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,OAAM;gBACN,aAAa,CAAC,iCAAiC,EAAE,QAAQ,IAAI,CAAC,4FAA4F,CAAC;gBAC3J,aAAa,aAAa,gBAAgB;gBAC1C,YAAW;gBACX,WAAW;gBACX,SAAS;gBACT,SAAQ;;;;;;;;;;;;AAIhB", "debugId": null}}, {"offset": {"line": 2605, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductGrid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AlertCircle, RefreshCw } from \"lucide-react\";\r\n\r\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nimport { ProductCard } from \"./ProductCard\";\r\n\r\ninterface ProductGridProps {\r\n  products?: unknown[];\r\n  loading?: boolean;\r\n  error?: string | null;\r\n  onRefresh?: () => void;\r\n}\r\n\r\nexport const ProductGrid = ({\r\n  products = [],\r\n  loading = false,\r\n  error = null,\r\n  onRefresh,\r\n}: ProductGridProps) => {\r\n  if (loading) {\r\n    return (\r\n      <section className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5\">\r\n        {Array.from({ length: 8 }).map((_, index) => (\r\n          <div key={index} className=\"w-full\">\r\n            <Skeleton className=\"h-64 w-full rounded-lg\" />\r\n            <div className=\"mt-4 space-y-2\">\r\n              <Skeleton className=\"h-4 w-3/4\" />\r\n              <Skeleton className=\"h-4 w-1/2\" />\r\n              <Skeleton className=\"h-6 w-1/4\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </section>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Alert className=\"mx-auto max-w-md\">\r\n        <AlertCircle className=\"h-4 w-4\" />\r\n        <AlertDescription className=\"flex items-center justify-between\">\r\n          <span>Failed to load products: {error}</span>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={onRefresh}\r\n            className=\"ml-2\"\r\n          >\r\n            <RefreshCw className=\"mr-1 h-4 w-4\" />\r\n            Retry\r\n          </Button>\r\n        </AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n\r\n  if (!products || products.length === 0) {\r\n    return (\r\n      <div className=\"py-12 text-center\">\r\n        <p className=\"text-lg text-gray-500\">No products found</p>\r\n        <Button variant=\"outline\" onClick={onRefresh} className=\"mt-4\">\r\n          <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n          Refresh\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <section className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5\">\r\n      {products.map((product, index) => (\r\n        <ProductCard\r\n          key={(product as any)?._id || (product as any)?.id || index}\r\n          product={product as any}\r\n          index={index}\r\n          onDelete={onRefresh}\r\n        />\r\n      ))}\r\n    </section>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAEA;AACA;AACA;AAEA;AARA;;;;;;;AAiBO,MAAM,cAAc,CAAC,EAC1B,WAAW,EAAE,EACb,UAAU,KAAK,EACf,QAAQ,IAAI,EACZ,SAAS,EACQ;IACjB,IAAI,SAAS;QACX,qBACE,6WAAC;YAAQ,WAAU;sBAChB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6WAAC;oBAAgB,WAAU;;sCACzB,6WAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6WAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6WAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;mBALd;;;;;;;;;;IAWlB;IAEA,IAAI,OAAO;QACT,qBACE,6WAAC,0HAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6WAAC,0HAAA,CAAA,mBAAgB;oBAAC,WAAU;;sCAC1B,6WAAC;;gCAAK;gCAA0B;;;;;;;sCAChC,6WAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6WAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;IAMhD;IAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,qBACE,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAE,WAAU;8BAAwB;;;;;;8BACrC,6WAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,SAAS;oBAAW,WAAU;;sCACtD,6WAAC,oSAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAK9C;IAEA,qBACE,6WAAC;QAAQ,WAAU;kBAChB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6WAAC,+IAAA,CAAA,cAAW;gBAEV,SAAS;gBACT,OAAO;gBACP,UAAU;eAHL,AAAC,SAAiB,OAAQ,SAAiB,MAAM;;;;;;;;;;AAQhE", "debugId": null}}, {"offset": {"line": 2798, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductPagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { Chevron<PERSON>eft, ChevronRight } from \"lucide-react\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\n\r\nexport const ProductPagination = () => {\r\n  return (\r\n    <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n      {/* Results info */}\r\n      <div className=\"text-sm text-gray-500\">\r\n        Showing <span className=\"font-medium text-gray-900\">1-12</span> of{\" \"}\r\n        <span className=\"font-medium text-gray-900\">48</span> products\r\n      </div>\r\n\r\n      {/* Pagination controls */}\r\n      <div className=\"flex items-center gap-4\">\r\n        {/* Items per page */}\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-gray-500\">Show:</span>\r\n          <Select defaultValue=\"12\">\r\n            <SelectTrigger className=\"w-[70px]\">\r\n              <SelectValue />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"12\">12</SelectItem>\r\n              <SelectItem value=\"24\">24</SelectItem>\r\n              <SelectItem value=\"48\">48</SelectItem>\r\n              <SelectItem value=\"96\">96</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n\r\n        {/* Page navigation */}\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button variant=\"outline\" size=\"sm\" disabled>\r\n            <ChevronLeft className=\"h-4 w-4\" />\r\n            Previous\r\n          </Button>\r\n\r\n          {/* Page numbers */}\r\n          <div className=\"flex items-center gap-1\">\r\n            <Button variant=\"default\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              1\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              2\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              3\r\n            </Button>\r\n            <span className=\"px-2 text-sm text-gray-500\">...</span>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              4\r\n            </Button>\r\n          </div>\r\n\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            Next\r\n            <ChevronRight className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAEA;AACA;AAPA;;;;;AAeO,MAAM,oBAAoB;IAC/B,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;oBAAwB;kCAC7B,6WAAC;wBAAK,WAAU;kCAA4B;;;;;;oBAAW;oBAAI;kCACnE,6WAAC;wBAAK,WAAU;kCAA4B;;;;;;oBAAS;;;;;;;0BAIvD,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6WAAC,2HAAA,CAAA,SAAM;gCAAC,cAAa;;kDACnB,6WAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6WAAC,2HAAA,CAAA,gBAAa;;0DACZ,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAM7B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,QAAQ;;kDAC1C,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAKrC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,6WAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAK9D,6WAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;oCAAK;kDAElC,6WAAC,0SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC", "debugId": null}}, {"offset": {"line": 3047, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/products/ProductListWrapper.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, {\r\n  forwardRef,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useState,\r\n} from \"react\";\r\n\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { useProducts } from \"@/hooks/useProducts\";\r\nimport { ProductFilters } from \"@/lib/api/products\";\r\n\r\nimport { ProductFilter } from \"./ProductFilter\";\r\nimport { ProductGrid } from \"./ProductGrid\";\r\nimport { ProductPagination } from \"./ProductPagination\";\r\n\r\nexport interface ProductListWrapperRef {\r\n  handleRefresh: () => void;\r\n  loading: boolean;\r\n}\r\n\r\nexport interface ProductListWrapperProps {\r\n  initialFilters?: ProductFilters;\r\n}\r\n\r\nexport const ProductListWrapper = forwardRef<\r\n  ProductListWrapperRef,\r\n  ProductListWrapperProps\r\n>(({ initialFilters = {} }, ref) => {\r\n  const [filters, setFilters] = useState<ProductFilters>(initialFilters);\r\n  const { products, loading, error, updateFilters, refreshProducts, meta } =\r\n    useProducts(filters);\r\n\r\n  const handleFiltersChange = (newFilters: ProductFilters) => {\r\n    setFilters(newFilters);\r\n    updateFilters(newFilters);\r\n  };\r\n\r\n  const handleRefresh = () => {\r\n    refreshProducts();\r\n  };\r\n\r\n  // Expose methods to parent component\r\n  useImperativeHandle(ref, () => ({\r\n    handleRefresh,\r\n    loading,\r\n  }));\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardContent className=\"p-6\">\r\n          <ProductFilter\r\n            onFiltersChange={handleFiltersChange}\r\n            onRefresh={handleRefresh}\r\n            loading={loading}\r\n            initialFilters={initialFilters}\r\n          />\r\n\r\n          <Separator className=\"my-3 mb-6\" />\r\n\r\n          <ProductGrid\r\n            products={products}\r\n            loading={loading}\r\n            error={error}\r\n            onRefresh={handleRefresh}\r\n          />\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {meta && meta.pages > 1 && (\r\n        <div className=\"mt-6\">\r\n          <ProductPagination\r\n            currentPage={meta.page}\r\n            totalPages={meta.pages}\r\n            onPageChange={(page) => handleFiltersChange({ ...filters, page })}\r\n          />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nProductListWrapper.displayName = \"ProductListWrapper\";\r\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AACA;AACA;AAGA;AACA;AACA;AAhBA;;;;;;;;;AA2BO,MAAM,mCAAqB,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAGzC,CAAC,EAAE,iBAAiB,CAAC,CAAC,EAAE,EAAE;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,IAAI,EAAE,GACtE,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;IAEd,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,cAAc;IAChB;IAEA,MAAM,gBAAgB;QACpB;IACF;IAEA,qCAAqC;IACrC,CAAA,GAAA,oUAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAM,CAAC;YAC9B;YACA;QACF,CAAC;IAED,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,iJAAA,CAAA,gBAAa;4BACZ,iBAAiB;4BACjB,WAAW;4BACX,SAAS;4BACT,gBAAgB;;;;;;sCAGlB,6WAAC,8HAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCAErB,6WAAC,+IAAA,CAAA,cAAW;4BACV,UAAU;4BACV,SAAS;4BACT,OAAO;4BACP,WAAW;;;;;;;;;;;;;;;;;YAKhB,QAAQ,KAAK,KAAK,GAAG,mBACpB,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,qJAAA,CAAA,oBAAiB;oBAChB,aAAa,KAAK,IAAI;oBACtB,YAAY,KAAK,KAAK;oBACtB,cAAc,CAAC,OAAS,oBAAoB;4BAAE,GAAG,OAAO;4BAAE;wBAAK;;;;;;;;;;;;;;;;;AAM3E;AAEA,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3160, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/app/admin/products/list/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useRef, useState } from \"react\";\r\n\r\nimport { useSearchParams } from \"next/navigation\";\r\n\r\nimport { PageHeaderWrapper } from \"@/components/common/PageHeaderWrapper\";\r\nimport { ProductListActions } from \"@/components/pages/products/ProductListActions\";\r\nimport { ProductListWrapper } from \"@/components/pages/products/ProductListWrapper\";\r\nimport { ProductFilters } from \"@/lib/api/products\";\r\n\r\nexport default function AdminProductsList() {\r\n  const searchParams = useSearchParams();\r\n  const [initialFilters, setInitialFilters] = useState<ProductFilters>({});\r\n  const wrapperRef = useRef<{ handleRefresh: () => void; loading: boolean }>(\r\n    null\r\n  );\r\n\r\n  // Extract filters from URL parameters\r\n  useEffect(() => {\r\n    const filters: ProductFilters = {};\r\n\r\n    // Get category from URL\r\n    const category = searchParams.get(\"category\");\r\n    if (category) {\r\n      filters.category = category;\r\n    }\r\n\r\n    // Get other potential filters from URL\r\n    const brand = searchParams.get(\"brand\");\r\n    if (brand) {\r\n      filters.brand = brand;\r\n    }\r\n\r\n    const search = searchParams.get(\"search\");\r\n    if (search) {\r\n      filters.search = search;\r\n    }\r\n\r\n    const status = searchParams.get(\"status\");\r\n    if (status) {\r\n      filters.status = status;\r\n    }\r\n\r\n    const minPrice = searchParams.get(\"minPrice\");\r\n    if (minPrice) {\r\n      filters.minPrice = Number(minPrice);\r\n    }\r\n\r\n    const maxPrice = searchParams.get(\"maxPrice\");\r\n    if (maxPrice) {\r\n      filters.maxPrice = Number(maxPrice);\r\n    }\r\n\r\n    const sortBy = searchParams.get(\"sortBy\");\r\n    if (sortBy) {\r\n      filters.sortBy = sortBy as \"price\" | \"createdAt\" | \"name\";\r\n    }\r\n\r\n    const sortOrder = searchParams.get(\"sortOrder\");\r\n    if (sortOrder) {\r\n      filters.sortOrder = sortOrder as \"asc\" | \"desc\";\r\n    }\r\n\r\n    setInitialFilters(filters);\r\n  }, [searchParams]);\r\n\r\n  const handleRefresh = () => {\r\n    wrapperRef.current?.handleRefresh();\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <PageHeaderWrapper\r\n        title=\"Products\"\r\n        description=\"Browse and manage your product catalog\"\r\n      >\r\n        <ProductListActions\r\n          onRefresh={handleRefresh}\r\n          loading={wrapperRef.current?.loading || false}\r\n        />\r\n      </PageHeaderWrapper>\r\n\r\n      <div className=\"container mx-auto mt-6\">\r\n        <ProductListWrapper\r\n          ref={wrapperRef}\r\n          initialFilters={initialFilters}\r\n          key={JSON.stringify(initialFilters)}\r\n        />\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AACA;AACA;AARA;;;;;;;AAWe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,iQAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IACtE,MAAM,aAAa,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EACtB;IAGF,sCAAsC;IACtC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAA0B,CAAC;QAEjC,wBAAwB;QACxB,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,IAAI,UAAU;YACZ,QAAQ,QAAQ,GAAG;QACrB;QAEA,uCAAuC;QACvC,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,IAAI,OAAO;YACT,QAAQ,KAAK,GAAG;QAClB;QAEA,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ;YACV,QAAQ,MAAM,GAAG;QACnB;QAEA,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ;YACV,QAAQ,MAAM,GAAG;QACnB;QAEA,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,IAAI,UAAU;YACZ,QAAQ,QAAQ,GAAG,OAAO;QAC5B;QAEA,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,IAAI,UAAU;YACZ,QAAQ,QAAQ,GAAG,OAAO;QAC5B;QAEA,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,IAAI,QAAQ;YACV,QAAQ,MAAM,GAAG;QACnB;QAEA,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,IAAI,WAAW;YACb,QAAQ,SAAS,GAAG;QACtB;QAEA,kBAAkB;IACpB,GAAG;QAAC;KAAa;IAEjB,MAAM,gBAAgB;QACpB,WAAW,OAAO,EAAE;IACtB;IAEA,qBACE;;0BACE,6WAAC,0IAAA,CAAA,oBAAiB;gBAChB,OAAM;gBACN,aAAY;0BAEZ,cAAA,6WAAC,sJAAA,CAAA,qBAAkB;oBACjB,WAAW;oBACX,SAAS,WAAW,OAAO,EAAE,WAAW;;;;;;;;;;;0BAI5C,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,sJAAA,CAAA,qBAAkB;oBACjB,KAAK;oBACL,gBAAgB;mBACX,KAAK,SAAS,CAAC;;;;;;;;;;;;AAK9B", "debugId": null}}]}