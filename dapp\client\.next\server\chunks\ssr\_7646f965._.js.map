{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/common/PageHeaderWrapper.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ntype PageHeaderWrapperProps = {\r\n  title: string;\r\n  description: string;\r\n  children?: React.ReactNode;\r\n};\r\n\r\n/**\r\n * A standardized page header component with title and description\r\n * Used across admin pages for consistent UI\r\n */\r\nexport const PageHeaderWrapper = ({\r\n  title,\r\n  description,\r\n  children,\r\n}: PageHeaderWrapperProps) => {\r\n  return (\r\n    <div className=\"container mx-auto py-6\">\r\n      <div className=\"flex flex-col items-start justify-between gap-4 border-b pb-5 sm:flex-row sm:items-center sm:gap-0\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold tracking-tight\">{title}</h1>\r\n          <p className=\"text-muted-foreground\">{description}</p>\r\n        </div>\r\n        {children && <div>{children}</div>}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAYO,MAAM,oBAAoB,CAAC,EAChC,KAAK,EACL,WAAW,EACX,QAAQ,EACe;IACvB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;;sCACC,6WAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6WAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;gBAEvC,0BAAY,6WAAC;8BAAK;;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6WAAC,+QAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,+QAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/brands.ts"], "sourcesContent": ["import { ApiResponse } from \"./types\";\n\n// Get API base URL from environment or default to localhost\nconst getApiBaseUrl = (): string => {\n  if (typeof window !== \"undefined\") {\n    // Client-side: use current origin or environment variable\n    return (\n      process.env.NEXT_PUBLIC_API_URL ||\n      `${window.location.protocol}//${window.location.hostname}:3011`\n    );\n  }\n  // Server-side: use environment variable or default\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3011\";\n};\n\nconst API_BASE_URL = getApiBaseUrl();\n\n/**\n * Brand interface matching the backend Brand type\n */\nexport interface Brand {\n  _id: string;\n  name: string;\n  description: string;\n  slug: string;\n  logo?: string;\n  website?: string;\n  color?: string;\n  isActive: boolean;\n  productCount: number;\n  sortOrder: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * DTO for creating a new brand\n */\nexport interface CreateBrandDto {\n  name: string;\n  description?: string;\n  logo?: string;\n  website?: string;\n  color?: string;\n  isActive?: boolean;\n  sortOrder?: number;\n}\n\n/**\n * DTO for updating a brand\n */\nexport interface UpdateBrandDto {\n  name?: string;\n  description?: string;\n  logo?: string;\n  website?: string;\n  color?: string;\n  isActive?: boolean;\n  sortOrder?: number;\n}\n\n/**\n * Brand filters for querying\n */\nexport interface BrandFilters {\n  isActive?: boolean;\n  search?: string;\n}\n\n/**\n * API service for brand management\n */\nexport class BrandApi {\n  private static readonly BASE_URL = `${API_BASE_URL}/api/brands`;\n\n  /**\n   * Get all brands with optional filtering\n   */\n  static async getBrands(\n    filters?: BrandFilters\n  ): Promise<ApiResponse<Brand[]>> {\n    try {\n      const params = new URLSearchParams();\n\n      if (filters?.isActive !== undefined) {\n        params.append(\"isActive\", filters.isActive.toString());\n      }\n\n      if (filters?.search) {\n        params.append(\"search\", filters.search);\n      }\n\n      const queryString = params.toString();\n      const url = queryString\n        ? `${this.BASE_URL}?${queryString}`\n        : this.BASE_URL;\n\n      const response = await fetch(url);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching brands:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a brand by ID\n   */\n  static async getBrandById(id: string): Promise<ApiResponse<Brand>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching brand:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a brand by slug\n   */\n  static async getBrandBySlug(slug: string): Promise<ApiResponse<Brand>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/slug/${slug}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error fetching brand by slug:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a new brand\n   */\n  static async createBrand(\n    brandData: CreateBrandDto\n  ): Promise<ApiResponse<Brand>> {\n    try {\n      const response = await fetch(this.BASE_URL, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(brandData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error creating brand:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update a brand\n   */\n  static async updateBrand(\n    id: string,\n    updateData: UpdateBrandDto\n  ): Promise<ApiResponse<Brand>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error updating brand:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a brand\n   */\n  static async deleteBrand(id: string): Promise<ApiResponse<null>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/${id}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error deleting brand:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Recalculate product counts for all brands\n   */\n  static async recalculateProductCounts(): Promise<ApiResponse<null>> {\n    try {\n      const response = await fetch(`${this.BASE_URL}/recalculate-counts`, {\n        method: \"POST\",\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(\n          errorData.message || `HTTP error! status: ${response.status}`\n        );\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error(\"Error recalculating brand product counts:\", error);\n      throw error;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEA,4DAA4D;AAC5D,MAAM,gBAAgB;IACpB,uCAAmC;;IAMnC;IACA,mDAAmD;IACnD,OAAO,6DAAmC;AAC5C;AAEA,MAAM,eAAe;AAyDd,MAAM;IACX,OAAwB,WAAW,GAAG,aAAa,WAAW,CAAC,CAAC;IAEhE;;GAEC,GACD,aAAa,UACX,OAAsB,EACS;QAC/B,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,SAAS,aAAa,WAAW;gBACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;YACrD;YAEA,IAAI,SAAS,QAAQ;gBACnB,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACxC;YAEA,MAAM,cAAc,OAAO,QAAQ;YACnC,MAAM,MAAM,cACR,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,GACjC,IAAI,CAAC,QAAQ;YAEjB,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,aAAa,EAAU,EAA+B;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI;YAErD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,IAAY,EAA+B;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM;YAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,YACX,SAAyB,EACI;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,QAAQ,EAAE;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,YACX,EAAU,EACV,UAA0B,EACG;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,YAAY,EAAU,EAA8B;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE;gBACrD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,2BAAuD;QAClE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;gBAClE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MACR,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAEjE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useBrands.ts"], "sourcesContent": ["\"use client\";\n\nimport { useCallback, useEffect, useState } from \"react\";\n\nimport { toast } from \"sonner\";\n\nimport {\n  Brand,\n  BrandApi,\n  BrandFilters,\n  CreateBrandDto,\n  UpdateBrandDto,\n} from \"@/lib/api/brands\";\n\n/**\n * Custom hook for managing brands data and operations\n */\nexport const useBrands = (initialFilters?: BrandFilters) => {\n  const [brands, setBrands] = useState<Brand[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [filters, setFilters] = useState<BrandFilters>(initialFilters || {});\n\n  /**\n   * Fetch brands from the API\n   */\n  const fetchBrands = useCallback(\n    async (currentFilters?: BrandFilters) => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const filtersToUse = currentFilters || filters;\n        const response = await BrandApi.getBrands(filtersToUse);\n\n        if (response.success) {\n          setBrands(response.data);\n        } else {\n          throw new Error(\"Failed to fetch brands\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to fetch brands\";\n        setError(errorMessage);\n        console.error(\"Error fetching brands:\", err);\n      } finally {\n        setLoading(false);\n      }\n    },\n    [filters]\n  );\n\n  /**\n   * Create a new brand\n   */\n  const createBrand = useCallback(\n    async (brandData: CreateBrandDto): Promise<Brand | null> => {\n      try {\n        const response = await BrandApi.createBrand(brandData);\n\n        if (response.success) {\n          setBrands((prev) => [...prev, response.data]);\n          toast.success(\"Brand created successfully\");\n          return response.data;\n        } else {\n          throw new Error(\"Failed to create brand\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to create brand\";\n        toast.error(errorMessage);\n        console.error(\"Error creating brand:\", err);\n        return null;\n      }\n    },\n    []\n  );\n\n  /**\n   * Update an existing brand\n   */\n  const updateBrand = useCallback(\n    async (id: string, updateData: UpdateBrandDto): Promise<Brand | null> => {\n      try {\n        const response = await BrandApi.updateBrand(id, updateData);\n\n        if (response.success) {\n          setBrands((prev) =>\n            prev.map((brand) => (brand._id === id ? response.data : brand))\n          );\n          toast.success(\"Brand updated successfully\");\n          return response.data;\n        } else {\n          throw new Error(\"Failed to update brand\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to update brand\";\n        toast.error(errorMessage);\n        console.error(\"Error updating brand:\", err);\n        return null;\n      }\n    },\n    []\n  );\n\n  /**\n   * Delete a brand\n   */\n  const deleteBrand = useCallback(async (id: string): Promise<boolean> => {\n    try {\n      const response = await BrandApi.deleteBrand(id);\n\n      if (response.success) {\n        setBrands((prev) => prev.filter((brand) => brand._id !== id));\n        toast.success(\"Brand deleted successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to delete brand\");\n      }\n    } catch (err) {\n      const errorMessage =\n        err instanceof Error ? err.message : \"Failed to delete brand\";\n      toast.error(errorMessage);\n      console.error(\"Error deleting brand:\", err);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Get a brand by ID\n   */\n  const getBrandById = useCallback(\n    async (id: string): Promise<Brand | null> => {\n      try {\n        const response = await BrandApi.getBrandById(id);\n\n        if (response.success) {\n          return response.data;\n        } else {\n          throw new Error(\"Failed to fetch brand\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to fetch brand\";\n        console.error(\"Error fetching brand by ID:\", err);\n        return null;\n      }\n    },\n    []\n  );\n\n  /**\n   * Get a brand by slug\n   */\n  const getBrandBySlug = useCallback(\n    async (slug: string): Promise<Brand | null> => {\n      try {\n        const response = await BrandApi.getBrandBySlug(slug);\n\n        if (response.success) {\n          return response.data;\n        } else {\n          throw new Error(\"Failed to fetch brand\");\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : \"Failed to fetch brand\";\n        console.error(\"Error fetching brand by slug:\", err);\n        return null;\n      }\n    },\n    []\n  );\n\n  /**\n   * Recalculate product counts for all brands\n   */\n  const recalculateProductCounts = useCallback(async (): Promise<boolean> => {\n    try {\n      const response = await BrandApi.recalculateProductCounts();\n\n      if (response.success) {\n        // Refresh brands to get updated counts\n        await fetchBrands();\n        toast.success(\"Brand product counts recalculated successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to recalculate product counts\");\n      }\n    } catch (err) {\n      const errorMessage =\n        err instanceof Error\n          ? err.message\n          : \"Failed to recalculate product counts\";\n      toast.error(errorMessage);\n      console.error(\"Error recalculating product counts:\", err);\n      return false;\n    }\n  }, [fetchBrands]);\n\n  /**\n   * Update filters and refetch data\n   */\n  const updateFilters = useCallback(\n    (newFilters: BrandFilters) => {\n      setFilters(newFilters);\n      fetchBrands(newFilters);\n    },\n    [fetchBrands]\n  );\n\n  /**\n   * Refresh brands data\n   */\n  const refreshBrands = useCallback(() => {\n    fetchBrands();\n  }, [fetchBrands]);\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchBrands();\n  }, [fetchBrands]);\n\n  return {\n    brands,\n    loading,\n    error,\n    filters,\n    createBrand,\n    updateBrand,\n    deleteBrand,\n    getBrandById,\n    getBrandBySlug,\n    recalculateProductCounts,\n    updateFilters,\n    refreshBrands,\n    refetch: refreshBrands,\n  };\n};\n\n/**\n * Hook for brand CRUD operations (similar to useCategoryMutations)\n */\nexport function useBrandMutations() {\n  const [loading, setLoading] = useState(false);\n\n  const createBrand = useCallback(async (brandData: CreateBrandDto) => {\n    setLoading(true);\n    try {\n      const response = await BrandApi.createBrand(brandData);\n      if (response.success) {\n        toast.success(\"Brand created successfully\");\n        return response.data;\n      } else {\n        throw new Error(\"Failed to create brand\");\n      }\n    } catch (error) {\n      const errorMessage =\n        error instanceof Error ? error.message : \"Failed to create brand\";\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const updateBrand = useCallback(\n    async (id: string, updateData: UpdateBrandDto) => {\n      setLoading(true);\n      try {\n        const response = await BrandApi.updateBrand(id, updateData);\n        if (response.success) {\n          toast.success(\"Brand updated successfully\");\n          return response.data;\n        } else {\n          throw new Error(\"Failed to update brand\");\n        }\n      } catch (error) {\n        const errorMessage =\n          error instanceof Error ? error.message : \"Failed to update brand\";\n        toast.error(errorMessage);\n        throw error;\n      } finally {\n        setLoading(false);\n      }\n    },\n    []\n  );\n\n  const deleteBrand = useCallback(async (id: string) => {\n    setLoading(true);\n    try {\n      const response = await BrandApi.deleteBrand(id);\n      if (response.success) {\n        toast.success(\"Brand deleted successfully\");\n        return true;\n      } else {\n        throw new Error(\"Failed to delete brand\");\n      }\n    } catch (error) {\n      const errorMessage =\n        error instanceof Error ? error.message : \"Failed to delete brand\";\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    createBrand,\n    updateBrand,\n    deleteBrand,\n    loading,\n  };\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AANA;;;;AAiBO,MAAM,YAAY,CAAC;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAgB,kBAAkB,CAAC;IAExE;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC5B,OAAO;QACL,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,eAAe,kBAAkB;YACvC,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;YAE1C,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,SAAS,IAAI;YACzB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,SAAS;YACT,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF,GACA;QAAC;KAAQ;IAGX;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC5B,OAAO;QACL,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAE5C,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,CAAC,OAAS;2BAAI;wBAAM,SAAS,IAAI;qBAAC;gBAC5C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC5B,OAAO,IAAY;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,IAAI;YAEhD,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,CAAC,OACT,KAAK,GAAG,CAAC,CAAC,QAAW,MAAM,GAAG,KAAK,KAAK,SAAS,IAAI,GAAG;gBAE1D,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAE5C,IAAI,SAAS,OAAO,EAAE;gBACpB,UAAU,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,QAAU,MAAM,GAAG,KAAK;gBACzD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,eAAe,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC7B,OAAO;QACL,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC;YAE7C,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC/B,OAAO;QACL,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,cAAc,CAAC;YAE/C,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,2BAA2B,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,wBAAwB;YAExD,IAAI,SAAS,OAAO,EAAE;gBACpB,uCAAuC;gBACvC,MAAM;gBACN,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QACX,IAAI,OAAO,GACX;YACN,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;QACT;IACF,GAAG;QAAC;KAAY;IAEhB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,WAAW;QACX,YAAY;IACd,GACA;QAAC;KAAY;IAGf;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAChC;IACF,GAAG;QAAC;KAAY;IAEhB,qBAAqB;IACrB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS;IACX;AACF;AAKO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAC5C,IAAI,SAAS,OAAO,EAAE;gBACpB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAC5B,OAAO,IAAY;QACjB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,IAAI;YAChD,IAAI,SAAS,OAAO,EAAE;gBACpB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GACA,EAAE;IAGJ,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAC5C,IAAI,SAAS,OAAO,EAAE;gBACpB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/brands/BrandManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useState } from \"react\";\n\nimport {\n  Archive,\n  Check,\n  Copy,\n  Edit,\n  ExternalLink,\n  Eye,\n  Filter,\n  Globe,\n  Grid3X3,\n  List,\n  MoreHorizontal,\n  Plus,\n  RefreshCw,\n  Search,\n  Trash2,\n  X,\n} from \"lucide-react\";\nimport { useRouter } from \"next/navigation\";\nimport { toast } from \"sonner\";\n\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { useBrandMutations, useBrands } from \"@/hooks/useBrands\";\nimport { Brand, CreateBrandDto } from \"@/lib/api/brands\";\n\ninterface BrandManagerEnhancedProps {\n  initialBrands?: Brand[];\n  onBrandsChange?: (brands: Brand[]) => void;\n}\n\n/**\n * Enhanced component for managing product brands with professional UI\n */\nexport const BrandManagerEnhanced = ({\n  initialBrands = [],\n  onBrandsChange,\n}: BrandManagerEnhancedProps) => {\n  const router = useRouter();\n\n  // API hooks\n  const { brands: apiBrands, loading, error, refetch } = useBrands();\n  const {\n    createBrand,\n    updateBrand,\n    deleteBrand,\n    loading: mutationLoading,\n  } = useBrandMutations();\n\n  // Local state\n  const [brands, setBrands] = useState<Brand[]>(initialBrands);\n  const [newBrand, setNewBrand] = useState<Partial<CreateBrandDto>>({\n    name: \"\",\n    description: \"\",\n    logo: \"\",\n    website: \"\",\n    color: \"#3B82F6\",\n    isActive: true,\n  });\n  const [editingBrandId, setEditingBrandId] = useState<string | null>(null);\n  const [editForm, setEditForm] = useState<Partial<Brand>>({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [showInactive, setShowInactive] = useState(false);\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\n  const [sortBy, setSortBy] = useState<\"name\" | \"created\" | \"products\">(\"name\");\n\n  // Update local brands when API data changes\n  useEffect(() => {\n    // Always sync with API data, even if empty (this shows loading state correctly)\n    setBrands(apiBrands);\n    if (onBrandsChange) {\n      onBrandsChange(apiBrands);\n    }\n  }, [apiBrands, onBrandsChange]);\n\n  // Filter and sort brands\n  const filteredAndSortedBrands = brands\n    .filter((brand) => {\n      const matchesSearch =\n        brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        brand.description.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesStatus = showInactive || brand.isActive;\n      return matchesSearch && matchesStatus;\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case \"name\":\n          return a.name.localeCompare(b.name);\n        case \"created\":\n          return (\n            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n          );\n        case \"products\":\n          return b.productCount - a.productCount;\n        default:\n          return 0;\n      }\n    });\n\n  // Add a new brand\n  const handleAddBrand = async () => {\n    if (!newBrand.name) {\n      toast.error(\"Brand name is required\");\n      return;\n    }\n\n    try {\n      await createBrand({\n        name: newBrand.name,\n        description: newBrand.description || \"\",\n        logo: newBrand.logo || \"\",\n        website: newBrand.website || \"\",\n        color: newBrand.color || \"#3B82F6\",\n        isActive: newBrand.isActive ?? true,\n      });\n\n      // Reset form\n      setNewBrand({\n        name: \"\",\n        description: \"\",\n        logo: \"\",\n        website: \"\",\n        color: \"#3B82F6\",\n        isActive: true,\n      });\n      setShowAddForm(false);\n\n      // Refresh brands list\n      await refetch();\n    } catch (error) {\n      // Error is already handled in the hook\n      console.error(\"Failed to create brand:\", error);\n    }\n  };\n\n  // Start editing a brand\n  const handleEditStart = (brand: Brand) => {\n    setEditingBrandId(brand._id);\n    setEditForm({ ...brand });\n  };\n\n  // Cancel editing\n  const handleEditCancel = () => {\n    setEditingBrandId(null);\n    setEditForm({});\n  };\n\n  // Save edited brand\n  const handleEditSave = async () => {\n    if (!editForm.name || !editingBrandId) {\n      toast.error(\"Brand name is required\");\n      return;\n    }\n\n    try {\n      await updateBrand(editingBrandId, {\n        name: editForm.name,\n        description: editForm.description || \"\",\n        logo: editForm.logo || \"\",\n        website: editForm.website || \"\",\n        color: editForm.color || \"#3B82F6\",\n        isActive: editForm.isActive ?? true,\n      });\n\n      setEditingBrandId(null);\n      setEditForm({});\n\n      // Refresh brands list\n      await refetch();\n    } catch (error) {\n      // Error is already handled in the hook\n      console.error(\"Failed to update brand:\", error);\n    }\n  };\n\n  // Delete a brand\n  const handleDeleteBrand = async (brand: Brand) => {\n    if (brand.productCount > 0) {\n      toast.error(\n        `Cannot delete \"${brand.name}\" because it has ${brand.productCount} products. Please remove or reassign the products first.`\n      );\n      return;\n    }\n\n    if (\n      confirm(\n        `Are you sure you want to delete \"${brand.name}\"? This action cannot be undone.`\n      )\n    ) {\n      try {\n        await deleteBrand(brand._id);\n        await refetch();\n      } catch (error) {\n        // Error is already handled in the hook\n        console.error(\"Failed to delete brand:\", error);\n      }\n    }\n  };\n\n  // Archive/Unarchive a brand\n  const handleArchiveBrand = async (brand: Brand) => {\n    try {\n      await updateBrand(brand._id, {\n        isActive: !brand.isActive,\n      });\n      await refetch();\n    } catch (error) {\n      console.error(\"Failed to archive brand:\", error);\n    }\n  };\n\n  // Duplicate a brand\n  const handleDuplicateBrand = async (brand: Brand) => {\n    try {\n      await createBrand({\n        name: `${brand.name} (Copy)`,\n        description: brand.description,\n        logo: brand.logo,\n        website: brand.website,\n        color: brand.color,\n        isActive: brand.isActive,\n      });\n      await refetch();\n    } catch (error) {\n      console.error(\"Failed to duplicate brand:\", error);\n    }\n  };\n\n  // View products for a brand\n  const handleViewProducts = (brand: Brand) => {\n    router.push(`/admin/products/list?brand=${encodeURIComponent(brand.name)}`);\n  };\n\n  if (error) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center text-red-600\">\n            <p>Error loading brands: {error}</p>\n            <Button onClick={refetch} className=\"mt-2\">\n              Try Again\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold\">Brand Management</h1>\n          <p className=\"text-muted-foreground\">\n            Manage your product brands and their information\n          </p>\n        </div>\n        <div className=\"flex gap-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={refetch}\n            disabled={loading}\n          >\n            <RefreshCw\n              className={`mr-2 h-4 w-4 ${loading ? \"animate-spin\" : \"\"}`}\n            />\n            Refresh\n          </Button>\n          <Button onClick={() => setShowAddForm(true)}>\n            <Plus className=\"mr-2 h-4 w-4\" />\n            Add Brand\n          </Button>\n        </div>\n      </div>\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 gap-4 md:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Brands</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{brands.length}</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Active Brands</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              {brands.filter((b) => b.isActive).length}\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">\n              Brands with Products\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              {brands.filter((b) => b.productCount > 0).length}\n            </div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <CardTitle className=\"text-sm font-medium\">\n              Total Products\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              {brands.reduce((sum, b) => sum + b.productCount, 0)}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Controls */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">Controls</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400\" />\n                <Input\n                  placeholder=\"Search brands...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <Select\n                value={sortBy}\n                onValueChange={(value: any) => setSortBy(value)}\n              >\n                <SelectTrigger className=\"w-40\">\n                  <SelectValue placeholder=\"Sort by\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"name\">Name</SelectItem>\n                  <SelectItem value=\"created\">Created Date</SelectItem>\n                  <SelectItem value=\"products\">Product Count</SelectItem>\n                </SelectContent>\n              </Select>\n              <Button\n                variant={showInactive ? \"default\" : \"outline\"}\n                onClick={() => setShowInactive(!showInactive)}\n              >\n                <Filter className=\"mr-2 h-4 w-4\" />\n                {showInactive ? \"Show All\" : \"Show Inactive\"}\n              </Button>\n              <div className=\"flex rounded-md border\">\n                <Button\n                  variant={viewMode === \"grid\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"grid\")}\n                  className=\"rounded-r-none\"\n                >\n                  <Grid3X3 className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant={viewMode === \"list\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"list\")}\n                  className=\"rounded-l-none\"\n                >\n                  <List className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Add Brand Form */}\n      {showAddForm && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Add New Brand</CardTitle>\n            <CardDescription>\n              Create a new brand for your product catalog\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 gap-4 md:grid-cols-2\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"brand-name\">Brand Name *</Label>\n                <Input\n                  id=\"brand-name\"\n                  placeholder=\"Enter brand name\"\n                  value={newBrand.name}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, name: e.target.value })\n                  }\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"brand-color\">Brand Color</Label>\n                <div className=\"flex gap-2\">\n                  <Input\n                    id=\"brand-color\"\n                    type=\"color\"\n                    value={newBrand.color}\n                    onChange={(e) =>\n                      setNewBrand({ ...newBrand, color: e.target.value })\n                    }\n                    className=\"w-16\"\n                  />\n                  <Input\n                    placeholder=\"#3B82F6\"\n                    value={newBrand.color}\n                    onChange={(e) =>\n                      setNewBrand({ ...newBrand, color: e.target.value })\n                    }\n                  />\n                </div>\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"brand-website\">Website</Label>\n                <Input\n                  id=\"brand-website\"\n                  placeholder=\"https://example.com\"\n                  value={newBrand.website}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, website: e.target.value })\n                  }\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"brand-logo\">Logo URL</Label>\n                <Input\n                  id=\"brand-logo\"\n                  placeholder=\"https://example.com/logo.png\"\n                  value={newBrand.logo}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, logo: e.target.value })\n                  }\n                />\n              </div>\n              <div className=\"space-y-2 md:col-span-2\">\n                <Label htmlFor=\"brand-description\">Description</Label>\n                <Textarea\n                  id=\"brand-description\"\n                  placeholder=\"Enter brand description\"\n                  value={newBrand.description}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, description: e.target.value })\n                  }\n                />\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"brand-active\"\n                  checked={newBrand.isActive}\n                  onCheckedChange={(checked) =>\n                    setNewBrand({ ...newBrand, isActive: checked })\n                  }\n                />\n                <Label htmlFor=\"brand-active\">Active</Label>\n              </div>\n            </div>\n            <div className=\"mt-6 flex gap-2\">\n              <Button onClick={handleAddBrand} disabled={mutationLoading}>\n                <Check className=\"mr-2 h-4 w-4\" />\n                Add Brand\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowAddForm(false)}>\n                <X className=\"mr-2 h-4 w-4\" />\n                Cancel\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Brands Display */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Brands ({filteredAndSortedBrands.length})</CardTitle>\n          <CardDescription>\n            Manage your brand catalog and view product associations\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {loading ? (\n            <div className=\"space-y-2\">\n              {[...Array(5)].map((_, i) => (\n                <Skeleton key={i} className=\"h-16 w-full\" />\n              ))}\n            </div>\n          ) : filteredAndSortedBrands.length === 0 ? (\n            <div className=\"py-8 text-center\">\n              <p className=\"text-muted-foreground\">No brands found</p>\n              {searchTerm && (\n                <Button\n                  variant=\"link\"\n                  onClick={() => setSearchTerm(\"\")}\n                  className=\"mt-2\"\n                >\n                  Clear search\n                </Button>\n              )}\n            </div>\n          ) : viewMode === \"grid\" ? (\n            <div className=\"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3\">\n              {filteredAndSortedBrands.map((brand) => (\n                <Card key={brand._id} className=\"relative\">\n                  <CardHeader className=\"pb-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-3\">\n                        <div\n                          className=\"h-6 w-6 rounded-full border-2\"\n                          style={{ backgroundColor: brand.color }}\n                        />\n                        <div>\n                          {editingBrandId === brand._id ? (\n                            <Input\n                              value={editForm.name}\n                              onChange={(e) =>\n                                setEditForm({\n                                  ...editForm,\n                                  name: e.target.value,\n                                })\n                              }\n                              className=\"h-8 text-sm font-medium\"\n                            />\n                          ) : (\n                            <CardTitle className=\"text-lg\">\n                              {brand.name}\n                            </CardTitle>\n                          )}\n                        </div>\n                      </div>\n                      <DropdownMenu>\n                        <DropdownMenuTrigger asChild>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <MoreHorizontal className=\"h-4 w-4\" />\n                          </Button>\n                        </DropdownMenuTrigger>\n                        <DropdownMenuContent align=\"end\">\n                          {editingBrandId === brand._id ? (\n                            <>\n                              <DropdownMenuItem onClick={handleEditSave}>\n                                <Check className=\"mr-2 h-4 w-4\" />\n                                Save\n                              </DropdownMenuItem>\n                              <DropdownMenuItem onClick={handleEditCancel}>\n                                <X className=\"mr-2 h-4 w-4\" />\n                                Cancel\n                              </DropdownMenuItem>\n                            </>\n                          ) : (\n                            <>\n                              <DropdownMenuItem\n                                onClick={() => handleEditStart(brand)}\n                              >\n                                <Edit className=\"mr-2 h-4 w-4\" />\n                                Edit\n                              </DropdownMenuItem>\n                              <DropdownMenuItem\n                                onClick={() => handleViewProducts(brand)}\n                              >\n                                <Eye className=\"mr-2 h-4 w-4\" />\n                                View Products ({brand.productCount})\n                              </DropdownMenuItem>\n                              <DropdownMenuItem\n                                onClick={() => handleDuplicateBrand(brand)}\n                              >\n                                <Copy className=\"mr-2 h-4 w-4\" />\n                                Duplicate\n                              </DropdownMenuItem>\n                              <DropdownMenuItem\n                                onClick={() => handleArchiveBrand(brand)}\n                              >\n                                <Archive className=\"mr-2 h-4 w-4\" />\n                                {brand.isActive ? \"Archive\" : \"Unarchive\"}\n                              </DropdownMenuItem>\n                              {brand.website && (\n                                <DropdownMenuItem\n                                  onClick={() =>\n                                    window.open(brand.website, \"_blank\")\n                                  }\n                                >\n                                  <ExternalLink className=\"mr-2 h-4 w-4\" />\n                                  Visit Website\n                                </DropdownMenuItem>\n                              )}\n                              <DropdownMenuSeparator />\n                              <DropdownMenuItem\n                                onClick={() => handleDeleteBrand(brand)}\n                                className=\"text-red-600\"\n                                disabled={brand.productCount > 0}\n                              >\n                                <Trash2 className=\"mr-2 h-4 w-4\" />\n                                Delete\n                              </DropdownMenuItem>\n                            </>\n                          )}\n                        </DropdownMenuContent>\n                      </DropdownMenu>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    {editingBrandId === brand._id ? (\n                      <div className=\"space-y-3\">\n                        <Textarea\n                          placeholder=\"Description\"\n                          value={editForm.description}\n                          onChange={(e) =>\n                            setEditForm({\n                              ...editForm,\n                              description: e.target.value,\n                            })\n                          }\n                          className=\"min-h-[60px]\"\n                        />\n                        <div className=\"flex gap-2\">\n                          <Input\n                            type=\"color\"\n                            value={editForm.color}\n                            onChange={(e) =>\n                              setEditForm({\n                                ...editForm,\n                                color: e.target.value,\n                              })\n                            }\n                            className=\"w-16\"\n                          />\n                          <Input\n                            placeholder=\"Website\"\n                            value={editForm.website}\n                            onChange={(e) =>\n                              setEditForm({\n                                ...editForm,\n                                website: e.target.value,\n                              })\n                            }\n                          />\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <Switch\n                            checked={editForm.isActive}\n                            onCheckedChange={(checked) =>\n                              setEditForm({ ...editForm, isActive: checked })\n                            }\n                          />\n                          <Label>Active</Label>\n                        </div>\n                      </div>\n                    ) : (\n                      <div className=\"space-y-3\">\n                        <p className=\"text-sm text-muted-foreground\">\n                          {brand.description || \"No description\"}\n                        </p>\n                        <div className=\"flex items-center justify-between\">\n                          <Badge\n                            variant={brand.isActive ? \"default\" : \"secondary\"}\n                          >\n                            {brand.isActive ? \"Active\" : \"Inactive\"}\n                          </Badge>\n                          <Badge variant=\"outline\">\n                            {brand.productCount} products\n                          </Badge>\n                        </div>\n                        {brand.website && (\n                          <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\n                            <Globe className=\"h-3 w-3\" />\n                            <span className=\"truncate\">{brand.website}</span>\n                          </div>\n                        )}\n                        <div className=\"text-xs text-muted-foreground\">\n                          Created{\" \"}\n                          {new Date(brand.createdAt).toLocaleDateString()}\n                        </div>\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : (\n            // List view would go here - simplified for now\n            <div className=\"py-8 text-center\">\n              <p className=\"text-muted-foreground\">List view coming soon</p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AAEA;AACA;AACA;AAOA;AAOA;AACA;AACA;AAOA;AACA;AACA;AACA;AArDA;;;;;;;;;;;;;;;;;AAgEO,MAAM,uBAAuB,CAAC,EACnC,gBAAgB,EAAE,EAClB,cAAc,EACY;IAC1B,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,YAAY;IACZ,MAAM,EAAE,QAAQ,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,YAAS,AAAD;IAC/D,MAAM,EACJ,WAAW,EACX,WAAW,EACX,WAAW,EACX,SAAS,eAAe,EACzB,GAAG,CAAA,GAAA,kHAAA,CAAA,oBAAiB,AAAD;IAEpB,cAAc;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;QAChE,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmC;IAEtE,4CAA4C;IAC5C,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,gFAAgF;QAChF,UAAU;QACV,IAAI,gBAAgB;YAClB,eAAe;QACjB;IACF,GAAG;QAAC;QAAW;KAAe;IAE9B,yBAAyB;IACzB,MAAM,0BAA0B,OAC7B,MAAM,CAAC,CAAC;QACP,MAAM,gBACJ,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACjE,MAAM,gBAAgB,gBAAgB,MAAM,QAAQ;QACpD,OAAO,iBAAiB;IAC1B,GACC,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YAEnE,KAAK;gBACH,OAAO,EAAE,YAAY,GAAG,EAAE,YAAY;YACxC;gBACE,OAAO;QACX;IACF;IAEF,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,YAAY;gBAChB,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW,IAAI;gBACrC,MAAM,SAAS,IAAI,IAAI;gBACvB,SAAS,SAAS,OAAO,IAAI;gBAC7B,OAAO,SAAS,KAAK,IAAI;gBACzB,UAAU,SAAS,QAAQ,IAAI;YACjC;YAEA,aAAa;YACb,YAAY;gBACV,MAAM;gBACN,aAAa;gBACb,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,UAAU;YACZ;YACA,eAAe;YAEf,sBAAsB;YACtB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,uCAAuC;YACvC,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,wBAAwB;IACxB,MAAM,kBAAkB,CAAC;QACvB,kBAAkB,MAAM,GAAG;QAC3B,YAAY;YAAE,GAAG,KAAK;QAAC;IACzB;IAEA,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,YAAY,CAAC;IACf;IAEA,oBAAoB;IACpB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,gBAAgB;YACrC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,YAAY,gBAAgB;gBAChC,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW,IAAI;gBACrC,MAAM,SAAS,IAAI,IAAI;gBACvB,SAAS,SAAS,OAAO,IAAI;gBAC7B,OAAO,SAAS,KAAK,IAAI;gBACzB,UAAU,SAAS,QAAQ,IAAI;YACjC;YAEA,kBAAkB;YAClB,YAAY,CAAC;YAEb,sBAAsB;YACtB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,uCAAuC;YACvC,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,iBAAiB;IACjB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,MAAM,YAAY,GAAG,GAAG;YAC1B,wQAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,eAAe,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE,MAAM,YAAY,CAAC,wDAAwD,CAAC;YAE9H;QACF;QAEA,IACE,QACE,CAAC,iCAAiC,EAAE,MAAM,IAAI,CAAC,gCAAgC,CAAC,GAElF;YACA,IAAI;gBACF,MAAM,YAAY,MAAM,GAAG;gBAC3B,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,uCAAuC;gBACvC,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,YAAY,MAAM,GAAG,EAAE;gBAC3B,UAAU,CAAC,MAAM,QAAQ;YAC3B;YACA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,oBAAoB;IACpB,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,YAAY;gBAChB,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;gBAC5B,aAAa,MAAM,WAAW;gBAC9B,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;gBAClB,UAAU,MAAM,QAAQ;YAC1B;YACA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,4BAA4B;IAC5B,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,mBAAmB,MAAM,IAAI,GAAG;IAC5E;IAEA,IAAI,OAAO;QACT,qBACE,6WAAC,yHAAA,CAAA,OAAI;sBACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;;gCAAE;gCAAuB;;;;;;;sCAC1B,6WAAC,2HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAS,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAOrD;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;;0CACC,6WAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,6WAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,6WAAC,oSAAA,CAAA,YAAS;wCACR,WAAW,CAAC,aAAa,EAAE,UAAU,iBAAiB,IAAI;;;;;;oCAC1D;;;;;;;0CAGJ,6WAAC,2HAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,eAAe;;kDACpC,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6WAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,6WAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,6WAAC;oCAAI,WAAU;8CAAsB,OAAO,MAAM;;;;;;;;;;;;;;;;;kCAGtD,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6WAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,6WAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,6WAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;kCAI9C,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6WAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAI7C,6WAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,6WAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,YAAY,GAAG,GAAG,MAAM;;;;;;;;;;;;;;;;;kCAItD,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6WAAC,yHAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAI7C,6WAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,6WAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,6WAAC,yHAAA,CAAA,OAAI;;kCACH,6WAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,6WAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAIhB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,2HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe,CAAC,QAAe,UAAU;;8DAEzC,6WAAC,2HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6WAAC,2HAAA,CAAA,gBAAa;;sEACZ,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;;;;;;;;;;;;;sDAGjC,6WAAC,2HAAA,CAAA,SAAM;4CACL,SAAS,eAAe,YAAY;4CACpC,SAAS,IAAM,gBAAgB,CAAC;;8DAEhC,6WAAC,0RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDACjB,eAAe,aAAa;;;;;;;sDAE/B,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2HAAA,CAAA,SAAM;oDACL,SAAS,aAAa,SAAS,YAAY;oDAC3C,MAAK;oDACL,SAAS,IAAM,YAAY;oDAC3B,WAAU;8DAEV,cAAA,6WAAC,gSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,6WAAC,2HAAA,CAAA,SAAM;oDACL,SAAS,aAAa,SAAS,YAAY;oDAC3C,MAAK;oDACL,SAAS,IAAM,YAAY;oDAC3B,WAAU;8DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS3B,6BACC,6WAAC,yHAAA,CAAA,OAAI;;kCACH,6WAAC,yHAAA,CAAA,aAAU;;0CACT,6WAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6WAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAItD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IACT,YAAY;gEAAE,GAAG,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAEnD,WAAU;;;;;;kEAEZ,6WAAC,0HAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IACT,YAAY;gEAAE,GAAG,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;;;;;;;;;;;;;;;;;;kDAKzD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAIzD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAItD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,6HAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,aAAY;gDACZ,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAI7D,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,QAAQ;gDAC1B,iBAAiB,CAAC,UAChB,YAAY;wDAAE,GAAG,QAAQ;wDAAE,UAAU;oDAAQ;;;;;;0DAGjD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;;;;;;;;;;;;;0CAGlC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;wCAAgB,UAAU;;0DACzC,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,eAAe;;0DACtD,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,6WAAC,yHAAA,CAAA,OAAI;;kCACH,6WAAC,yHAAA,CAAA,aAAU;;0CACT,6WAAC,yHAAA,CAAA,YAAS;;oCAAC;oCAAS,wBAAwB,MAAM;oCAAC;;;;;;;0CACnD,6WAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6WAAC,yHAAA,CAAA,cAAW;kCACT,wBACC,6WAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6WAAC,6HAAA,CAAA,WAAQ;oCAAS,WAAU;mCAAb;;;;;;;;;mCAGjB,wBAAwB,MAAM,KAAK,kBACrC,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAE,WAAU;8CAAwB;;;;;;gCACpC,4BACC,6WAAC,2HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CACX;;;;;;;;;;;mCAKH,aAAa,uBACf,6WAAC;4BAAI,WAAU;sCACZ,wBAAwB,GAAG,CAAC,CAAC,sBAC5B,6WAAC,yHAAA,CAAA,OAAI;oCAAiB,WAAU;;sDAC9B,6WAAC,yHAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,MAAM,KAAK;gEAAC;;;;;;0EAExC,6WAAC;0EACE,mBAAmB,MAAM,GAAG,iBAC3B,6WAAC,0HAAA,CAAA,QAAK;oEACJ,OAAO,SAAS,IAAI;oEACpB,UAAU,CAAC,IACT,YAAY;4EACV,GAAG,QAAQ;4EACX,MAAM,EAAE,MAAM,CAAC,KAAK;wEACtB;oEAEF,WAAU;;;;;yFAGZ,6WAAC,yHAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,MAAM,IAAI;;;;;;;;;;;;;;;;;kEAKnB,6WAAC,qIAAA,CAAA,eAAY;;0EACX,6WAAC,qIAAA,CAAA,sBAAmB;gEAAC,OAAO;0EAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAQ,MAAK;8EAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;wEAAC,WAAU;;;;;;;;;;;;;;;;0EAG9B,6WAAC,qIAAA,CAAA,sBAAmB;gEAAC,OAAM;0EACxB,mBAAmB,MAAM,GAAG,iBAC3B;;sFACE,6WAAC,qIAAA,CAAA,mBAAgB;4EAAC,SAAS;;8FACzB,6WAAC,wRAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGpC,6WAAC,qIAAA,CAAA,mBAAgB;4EAAC,SAAS;;8FACzB,6WAAC,gRAAA,CAAA,IAAC;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;iGAKlC;;sFACE,6WAAC,qIAAA,CAAA,mBAAgB;4EACf,SAAS,IAAM,gBAAgB;;8FAE/B,6WAAC,+RAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGnC,6WAAC,qIAAA,CAAA,mBAAgB;4EACf,SAAS,IAAM,mBAAmB;;8FAElC,6WAAC,oRAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;gFAChB,MAAM,YAAY;gFAAC;;;;;;;sFAErC,6WAAC,qIAAA,CAAA,mBAAgB;4EACf,SAAS,IAAM,qBAAqB;;8FAEpC,6WAAC,sRAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGnC,6WAAC,qIAAA,CAAA,mBAAgB;4EACf,SAAS,IAAM,mBAAmB;;8FAElC,6WAAC,4RAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;gFAClB,MAAM,QAAQ,GAAG,YAAY;;;;;;;wEAE/B,MAAM,OAAO,kBACZ,6WAAC,qIAAA,CAAA,mBAAgB;4EACf,SAAS,IACP,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE;;8FAG7B,6WAAC,0SAAA,CAAA,eAAY;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAI7C,6WAAC,qIAAA,CAAA,wBAAqB;;;;;sFACtB,6WAAC,qIAAA,CAAA,mBAAgB;4EACf,SAAS,IAAM,kBAAkB;4EACjC,WAAU;4EACV,UAAU,MAAM,YAAY,GAAG;;8FAE/B,6WAAC,8RAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDASjD,6WAAC,yHAAA,CAAA,cAAW;sDACT,mBAAmB,MAAM,GAAG,iBAC3B,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,6HAAA,CAAA,WAAQ;wDACP,aAAY;wDACZ,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IACT,YAAY;gEACV,GAAG,QAAQ;gEACX,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC7B;wDAEF,WAAU;;;;;;kEAEZ,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,0HAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IACT,YAAY;wEACV,GAAG,QAAQ;wEACX,OAAO,EAAE,MAAM,CAAC,KAAK;oEACvB;gEAEF,WAAU;;;;;;0EAEZ,6WAAC,0HAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,SAAS,OAAO;gEACvB,UAAU,CAAC,IACT,YAAY;wEACV,GAAG,QAAQ;wEACX,SAAS,EAAE,MAAM,CAAC,KAAK;oEACzB;;;;;;;;;;;;kEAIN,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,2HAAA,CAAA,SAAM;gEACL,SAAS,SAAS,QAAQ;gEAC1B,iBAAiB,CAAC,UAChB,YAAY;wEAAE,GAAG,QAAQ;wEAAE,UAAU;oEAAQ;;;;;;0EAGjD,6WAAC,0HAAA,CAAA,QAAK;0EAAC;;;;;;;;;;;;;;;;;qEAIX,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAE,WAAU;kEACV,MAAM,WAAW,IAAI;;;;;;kEAExB,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,0HAAA,CAAA,QAAK;gEACJ,SAAS,MAAM,QAAQ,GAAG,YAAY;0EAErC,MAAM,QAAQ,GAAG,WAAW;;;;;;0EAE/B,6WAAC,0HAAA,CAAA,QAAK;gEAAC,SAAQ;;oEACZ,MAAM,YAAY;oEAAC;;;;;;;;;;;;;oDAGvB,MAAM,OAAO,kBACZ,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,wRAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6WAAC;gEAAK,WAAU;0EAAY,MAAM,OAAO;;;;;;;;;;;;kEAG7C,6WAAC;wDAAI,WAAU;;4DAAgC;4DACrC;4DACP,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;mCAtK5C,MAAM,GAAG;;;;;;;;;mCA+KxB,+CAA+C;sCAC/C,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 2433, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Popover = PopoverPrimitive.Root\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger\r\n\r\nconst PopoverAnchor = PopoverPrimitive.Anchor\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n))\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,6QAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,6QAAA,CAAA,UAAwB;AAE/C,MAAM,gBAAgB,6QAAA,CAAA,SAAuB;AAE7C,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,6WAAC,6QAAA,CAAA,SAAuB;kBACtB,cAAA,6WAAC,6QAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,geACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,6QAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2476, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,iRAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6WAAC,iRAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6WAAC;;;;;0BACD,6WAAC,iRAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,iRAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6WAAC,iRAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,iRAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,iRAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2544, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/emoji-selector.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nimport { ChevronDown, Search } from \"lucide-react\";\n\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { cn } from \"@/lib/utils\";\n\n// Emoji database with search functionality\ntype EmojiData = {\n  emoji: string;\n  name: string;\n  keywords: string[];\n  category: string;\n};\n\nconst emojiDatabase: EmojiData[] = [\n  // Shopping & Commerce\n  {\n    emoji: \"🛒\",\n    name: \"shopping cart\",\n    keywords: [\"cart\", \"shopping\", \"buy\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"🛍️\",\n    name: \"shopping bags\",\n    keywords: [\"bags\", \"shopping\", \"retail\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"💳\",\n    name: \"credit card\",\n    keywords: [\"card\", \"payment\", \"money\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"💰\",\n    name: \"money bag\",\n    keywords: [\"money\", \"cash\", \"wealth\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"🏪\",\n    name: \"store\",\n    keywords: [\"store\", \"shop\", \"retail\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"🏬\",\n    name: \"department store\",\n    keywords: [\"mall\", \"shopping\", \"retail\"],\n    category: \"Shopping\",\n  },\n\n  // Electronics & Tech\n  {\n    emoji: \"📱\",\n    name: \"mobile phone\",\n    keywords: [\"phone\", \"mobile\", \"smartphone\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"💻\",\n    name: \"laptop\",\n    keywords: [\"laptop\", \"computer\", \"tech\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"⌚\",\n    name: \"watch\",\n    keywords: [\"watch\", \"time\", \"smartwatch\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"📺\",\n    name: \"television\",\n    keywords: [\"tv\", \"television\", \"screen\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"🎮\",\n    name: \"video game\",\n    keywords: [\"game\", \"gaming\", \"console\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"📷\",\n    name: \"camera\",\n    keywords: [\"camera\", \"photo\", \"photography\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"🎧\",\n    name: \"headphones\",\n    keywords: [\"headphones\", \"audio\", \"music\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"⚡\",\n    name: \"lightning\",\n    keywords: [\"power\", \"energy\", \"electric\"],\n    category: \"Electronics\",\n  },\n\n  // Fashion & Clothing\n  {\n    emoji: \"👕\",\n    name: \"t-shirt\",\n    keywords: [\"shirt\", \"clothing\", \"apparel\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"👔\",\n    name: \"necktie\",\n    keywords: [\"tie\", \"formal\", \"business\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"👗\",\n    name: \"dress\",\n    keywords: [\"dress\", \"clothing\", \"fashion\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"👠\",\n    name: \"high heel\",\n    keywords: [\"shoes\", \"heels\", \"fashion\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"👜\",\n    name: \"handbag\",\n    keywords: [\"bag\", \"purse\", \"fashion\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"🧥\",\n    name: \"coat\",\n    keywords: [\"coat\", \"jacket\", \"outerwear\"],\n    category: \"Fashion\",\n  },\n\n  // Home & Garden\n  {\n    emoji: \"🏠\",\n    name: \"house\",\n    keywords: [\"house\", \"home\", \"building\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🛏️\",\n    name: \"bed\",\n    keywords: [\"bed\", \"sleep\", \"bedroom\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🪑\",\n    name: \"chair\",\n    keywords: [\"chair\", \"seat\", \"furniture\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🛋️\",\n    name: \"couch\",\n    keywords: [\"couch\", \"sofa\", \"furniture\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🌱\",\n    name: \"seedling\",\n    keywords: [\"plant\", \"garden\", \"grow\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🌸\",\n    name: \"cherry blossom\",\n    keywords: [\"flower\", \"blossom\", \"spring\"],\n    category: \"Home\",\n  },\n\n  // Food & Beverages\n  {\n    emoji: \"🍎\",\n    name: \"apple\",\n    keywords: [\"apple\", \"fruit\", \"food\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"🍕\",\n    name: \"pizza\",\n    keywords: [\"pizza\", \"food\", \"italian\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"🍔\",\n    name: \"hamburger\",\n    keywords: [\"burger\", \"hamburger\", \"food\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"☕\",\n    name: \"coffee\",\n    keywords: [\"coffee\", \"drink\", \"caffeine\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"🍷\",\n    name: \"wine\",\n    keywords: [\"wine\", \"alcohol\", \"drink\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"🧀\",\n    name: \"cheese\",\n    keywords: [\"cheese\", \"dairy\", \"food\"],\n    category: \"Food\",\n  },\n\n  // Sports & Fitness\n  {\n    emoji: \"⚽\",\n    name: \"soccer ball\",\n    keywords: [\"soccer\", \"football\", \"sports\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🏀\",\n    name: \"basketball\",\n    keywords: [\"basketball\", \"sports\", \"ball\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🎾\",\n    name: \"tennis\",\n    keywords: [\"tennis\", \"sports\", \"ball\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🏋️\",\n    name: \"weight lifting\",\n    keywords: [\"gym\", \"fitness\", \"workout\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🚴\",\n    name: \"cycling\",\n    keywords: [\"bike\", \"cycling\", \"exercise\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🏃\",\n    name: \"running\",\n    keywords: [\"running\", \"exercise\", \"fitness\"],\n    category: \"Sports\",\n  },\n\n  // General\n  {\n    emoji: \"📦\",\n    name: \"package\",\n    keywords: [\"box\", \"package\", \"delivery\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"🏷️\",\n    name: \"label\",\n    keywords: [\"tag\", \"label\", \"price\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"📋\",\n    name: \"clipboard\",\n    keywords: [\"list\", \"clipboard\", \"notes\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"📊\",\n    name: \"bar chart\",\n    keywords: [\"chart\", \"graph\", \"data\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"🎯\",\n    name: \"target\",\n    keywords: [\"target\", \"goal\", \"aim\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"🔥\",\n    name: \"fire\",\n    keywords: [\"fire\", \"hot\", \"trending\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"⭐\",\n    name: \"star\",\n    keywords: [\"star\", \"favorite\", \"rating\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"🎁\",\n    name: \"gift\",\n    keywords: [\"gift\", \"present\", \"surprise\"],\n    category: \"General\",\n  },\n];\n\n// Extract categories for tabs\nconst categories = [\n  \"All\",\n  ...Array.from(new Set(emojiDatabase.map((item) => item.category))),\n];\n\ntype EmojiPickerProps = {\n  value?: string;\n  onChange: (emoji: string) => void;\n  placeholder?: string;\n};\n\nexport const EmojiPicker = ({\n  value,\n  onChange,\n  placeholder = \"Pick an emoji\",\n}: EmojiPickerProps) => {\n  const [open, setOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [selectedCategory, setSelectedCategory] = useState<string>(\"All\");\n\n  // Filter emojis based on category and search\n  const filteredEmojis =\n    selectedCategory === \"All\"\n      ? emojiDatabase\n      : emojiDatabase.filter((item) => item.category === selectedCategory);\n\n  const searchFilteredEmojis = searchQuery\n    ? filteredEmojis.filter((item) => {\n        const query = searchQuery.toLowerCase();\n        return (\n          item.name.toLowerCase().includes(query) ||\n          item.keywords.some((keyword) =>\n            keyword.toLowerCase().includes(query)\n          ) ||\n          item.emoji.includes(query)\n        );\n      })\n    : filteredEmojis;\n\n  const handleEmojiSelect = (emoji: string) => {\n    onChange(emoji);\n    setOpen(false);\n    setSearchQuery(\"\"); // Reset search when emoji is selected\n  };\n\n  return (\n    <div className=\"space-y-2\">\n      <Label>Icon</Label>\n      <Popover open={open} onOpenChange={setOpen}>\n        <PopoverTrigger asChild>\n          <button\n            className={cn(\n              // Base input styles - exactly matching Input component\n              \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background\",\n              \"file:border-0 file:bg-transparent file:text-sm file:font-medium\",\n              \"placeholder:text-muted-foreground\",\n              \"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n              \"disabled:cursor-not-allowed disabled:opacity-50\",\n              // Hover and focus states\n              \"hover:bg-accent hover:text-accent-foreground\",\n              \"transition-colors\",\n              // Open state\n              open && \"ring-2 ring-ring ring-offset-2\"\n            )}\n            type=\"button\"\n            role=\"combobox\"\n            aria-expanded={open}\n            aria-haspopup=\"listbox\"\n            aria-controls=\"emoji-listbox\"\n          >\n            {value ? (\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-2xl duration-200 animate-in fade-in\">\n                  {value}\n                </span>\n                <span className=\"font-medium text-foreground\">Selected</span>\n              </div>\n            ) : (\n              <span className=\"text-muted-foreground\">{placeholder}</span>\n            )}\n            <ChevronDown\n              className={cn(\n                \"h-4 w-4 opacity-50 transition-transform\",\n                open && \"rotate-180\"\n              )}\n            />\n          </button>\n        </PopoverTrigger>\n\n        <PopoverContent className=\"w-80 p-0\" align=\"start\">\n          <div className=\"space-y-4 p-4\">\n            {/* Search */}\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search emojis...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n\n            {/* Category Tabs */}\n            <div className=\"flex flex-wrap gap-1\">\n              {categories.map((category) => (\n                <Button\n                  key={category}\n                  variant={\n                    selectedCategory === category ? \"default\" : \"outline\"\n                  }\n                  size=\"sm\"\n                  onClick={() => setSelectedCategory(category)}\n                >\n                  {category}\n                </Button>\n              ))}\n            </div>\n\n            {/* Emoji Grid */}\n            <ScrollArea className=\"h-64\">\n              <div\n                className=\"grid grid-cols-8 gap-2 p-2\"\n                id=\"emoji-listbox\"\n                role=\"listbox\"\n              >\n                {searchFilteredEmojis.map((item, index) => (\n                  <Button\n                    key={`${item.emoji}-${index}`}\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"h-10 w-10 p-0 hover:bg-muted\"\n                    onClick={() => handleEmojiSelect(item.emoji)}\n                    role=\"option\"\n                    aria-selected={value === item.emoji}\n                    title={item.name}\n                  >\n                    <span className=\"text-xl\">{item.emoji}</span>\n                  </Button>\n                ))}\n              </div>\n              {searchFilteredEmojis.length === 0 && (\n                <div className=\"p-4 text-center text-sm text-muted-foreground\">\n                  No emojis found for \"{searchQuery}\"\n                </div>\n              )}\n            </ScrollArea>\n\n            {/* Custom Input */}\n            <div className=\"border-t pt-4\">\n              <Label className=\"text-sm text-muted-foreground\">\n                Or enter custom emoji:\n              </Label>\n              <Input\n                placeholder=\"🎯\"\n                value={value || \"\"}\n                onChange={(e) => onChange(e.target.value)}\n                className=\"mt-1\"\n                maxLength={4}\n              />\n            </div>\n          </div>\n        </PopoverContent>\n      </Popover>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAEA;AACA;AACA;AACA;AAKA;AACA;AAfA;;;;;;;;;;AAyBA,MAAM,gBAA6B;IACjC,sBAAsB;IACtB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAY;SAAM;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAY;SAAS;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAW;SAAQ;QACtC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAS;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAS;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAY;SAAS;QACxC,UAAU;IACZ;IAEA,qBAAqB;IACrB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAU;SAAa;QAC3C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAY;SAAO;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAa;QACzC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAM;YAAc;SAAS;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAU;SAAU;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAS;SAAc;QAC5C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAc;YAAS;SAAQ;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAU;SAAW;QACzC,UAAU;IACZ;IAEA,qBAAqB;IACrB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAY;SAAU;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAU;SAAW;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAY;SAAU;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAS;SAAU;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAS;SAAU;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAU;SAAY;QACzC,UAAU;IACZ;IAEA,gBAAgB;IAChB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAW;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAS;SAAU;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAY;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAY;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAU;SAAO;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAW;SAAS;QACzC,UAAU;IACZ;IAEA,mBAAmB;IACnB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAS;SAAO;QACpC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAU;QACtC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAa;SAAO;QACzC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAS;SAAW;QACzC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAW;SAAQ;QACtC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAS;SAAO;QACrC,UAAU;IACZ;IAEA,mBAAmB;IACnB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAY;SAAS;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAc;YAAU;SAAO;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAU;SAAO;QACtC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAW;SAAU;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAW;SAAW;QACzC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAW;YAAY;SAAU;QAC5C,UAAU;IACZ;IAEA,UAAU;IACV;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAW;SAAW;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAS;SAAQ;QACnC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAa;SAAQ;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAS;SAAO;QACpC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAQ;SAAM;QACnC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAO;SAAW;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAY;SAAS;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAW;SAAW;QACzC,UAAU;IACZ;CACD;AAED,8BAA8B;AAC9B,MAAM,aAAa;IACjB;OACG,MAAM,IAAI,CAAC,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ;CAChE;AAQM,MAAM,cAAc,CAAC,EAC1B,KAAK,EACL,QAAQ,EACR,cAAc,eAAe,EACZ;IACjB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,6CAA6C;IAC7C,MAAM,iBACJ,qBAAqB,QACjB,gBACA,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK;IAEvD,MAAM,uBAAuB,cACzB,eAAe,MAAM,CAAC,CAAC;QACrB,MAAM,QAAQ,YAAY,WAAW;QACrC,OACE,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UACjC,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,UAClB,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAEjC,KAAK,KAAK,CAAC,QAAQ,CAAC;IAExB,KACA;IAEJ,MAAM,oBAAoB,CAAC;QACzB,SAAS;QACT,QAAQ;QACR,eAAe,KAAK,sCAAsC;IAC5D;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,0HAAA,CAAA,QAAK;0BAAC;;;;;;0BACP,6WAAC,4HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCACjC,6WAAC,4HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6WAAC;4BACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uDAAuD;4BACvD,uIACA,mEACA,qCACA,uGACA,mDACA,yBAAyB;4BACzB,gDACA,qBACA,aAAa;4BACb,QAAQ;4BAEV,MAAK;4BACL,MAAK;4BACL,iBAAe;4BACf,iBAAc;4BACd,iBAAc;;gCAEb,sBACC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAK,WAAU;sDACb;;;;;;sDAEH,6WAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;yDAGhD,6WAAC;oCAAK,WAAU;8CAAyB;;;;;;8CAE3C,6WAAC,wSAAA,CAAA,cAAW;oCACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2CACA,QAAQ;;;;;;;;;;;;;;;;;kCAMhB,6WAAC,4HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;kCACzC,cAAA,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6WAAC,0HAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;8CAKd,6WAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6WAAC,2HAAA,CAAA,SAAM;4CAEL,SACE,qBAAqB,WAAW,YAAY;4CAE9C,MAAK;4CACL,SAAS,IAAM,oBAAoB;sDAElC;2CAPI;;;;;;;;;;8CAaX,6WAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6WAAC;4CACC,WAAU;4CACV,IAAG;4CACH,MAAK;sDAEJ,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,6WAAC,2HAAA,CAAA,SAAM;oDAEL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB,KAAK,KAAK;oDAC3C,MAAK;oDACL,iBAAe,UAAU,KAAK,KAAK;oDACnC,OAAO,KAAK,IAAI;8DAEhB,cAAA,6WAAC;wDAAK,WAAU;kEAAW,KAAK,KAAK;;;;;;mDAThC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;wCAalC,qBAAqB,MAAM,KAAK,mBAC/B,6WAAC;4CAAI,WAAU;;gDAAgD;gDACvC;gDAAY;;;;;;;;;;;;;8CAMxC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAgC;;;;;;sDAGjD,6WAAC,0HAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO,SAAS;4CAChB,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3B", "debugId": null}}, {"offset": {"line": 3291, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/categoryApi.ts"], "sourcesContent": ["import { Category } from \"@/components/pages/management/CategoryManager\";\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3011\";\n\nexport type CreateCategoryDto = {\n  name: string;\n  description?: string;\n  icon?: string;\n  color?: string;\n  isActive?: boolean;\n  parentId?: string;\n  sortOrder?: number;\n};\n\nexport type UpdateCategoryDto = Partial<CreateCategoryDto>;\n\nexport type CategoryFilters = {\n  isActive?: boolean;\n  parentId?: string;\n  search?: string;\n};\n\nexport type ApiResponse<T> = {\n  success: boolean;\n  data?: T;\n  message?: string;\n  count?: number;\n};\n\n/**\n * Category API service for frontend-backend communication\n */\nexport class CategoryApiService {\n  private static baseUrl = `${API_BASE_URL}/api/categories`;\n\n  /**\n   * Transform backend category to frontend format\n   */\n  private static transformCategory(backendCategory: any): Category {\n    console.log(\"Transforming category:\", backendCategory); // Debug log\n    const transformed = {\n      id: backendCategory._id || backendCategory.id,\n      name: backendCategory.name,\n      description: backendCategory.description,\n      slug: backendCategory.slug,\n      icon: backendCategory.icon,\n      color: backendCategory.color,\n      isActive: backendCategory.isActive,\n      productCount: backendCategory.productCount,\n      parentId: backendCategory.parentId,\n      sortOrder: backendCategory.sortOrder,\n      createdAt: backendCategory.createdAt,\n      updatedAt: backendCategory.updatedAt,\n    };\n    console.log(\"Transformed category:\", transformed); // Debug log\n    return transformed;\n  }\n\n  /**\n   * Get all categories with optional filtering\n   */\n  static async getCategories(filters?: CategoryFilters): Promise<Category[]> {\n    try {\n      const params = new URLSearchParams();\n\n      if (filters?.isActive !== undefined) {\n        params.append(\"isActive\", filters.isActive.toString());\n      }\n      if (filters?.parentId) {\n        params.append(\"parentId\", filters.parentId);\n      }\n      if (filters?.search) {\n        params.append(\"search\", filters.search);\n      }\n\n      const url = `${this.baseUrl}${params.toString() ? `?${params.toString()}` : \"\"}`;\n      const response = await fetch(url);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any[]> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch categories\");\n      }\n\n      const categories = (result.data || []).map(this.transformCategory);\n      return categories;\n    } catch (error) {\n      console.error(\"Error fetching categories:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a category by ID\n   */\n  static async getCategoryById(id: string): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/${id}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"Category not found\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error fetching category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a category by slug\n   */\n  static async getCategoryBySlug(slug: string): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/slug/${slug}`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to fetch category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"Category not found\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error fetching category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a new category\n   */\n  static async createCategory(\n    categoryData: CreateCategoryDto\n  ): Promise<Category> {\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(categoryData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to create category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"No category data returned\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error creating category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update a category\n   */\n  static async updateCategory(\n    id: string,\n    updateData: UpdateCategoryDto\n  ): Promise<Category> {\n    try {\n      const response = await fetch(`${this.baseUrl}/${id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(updateData),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to update category\");\n      }\n\n      if (!result.data) {\n        throw new Error(\"No category data returned\");\n      }\n\n      return this.transformCategory(result.data);\n    } catch (error) {\n      console.error(\"Error updating category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a category\n   */\n  static async deleteCategory(id: string): Promise<void> {\n    try {\n      console.log(\"Deleting category with ID:\", id); // Debug log\n      if (!id || id === \"undefined\") {\n        throw new Error(\"Category ID is required for deletion\");\n      }\n\n      const response = await fetch(`${this.baseUrl}/${id}`, {\n        method: \"DELETE\",\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<null> = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.message || \"Failed to delete category\");\n      }\n    } catch (error) {\n      console.error(\"Error deleting category:\", error);\n      throw error;\n    }\n  }\n\n  /**\n   * Recalculate product counts for all categories\n   */\n  static async recalculateProductCounts(): Promise<void> {\n    try {\n      const response = await fetch(`${this.baseUrl}/recalculate-counts`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result: ApiResponse<any> = await response.json();\n\n      if (!result.success) {\n        throw new Error(\n          result.message || \"Failed to recalculate product counts\"\n        );\n      }\n    } catch (error) {\n      console.error(\"Error recalculating product counts:\", error);\n      throw error;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEA,MAAM,eAAe,6DAAmC;AA8BjD,MAAM;IACX,OAAe,UAAU,GAAG,aAAa,eAAe,CAAC,CAAC;IAE1D;;GAEC,GACD,OAAe,kBAAkB,eAAoB,EAAY;QAC/D,QAAQ,GAAG,CAAC,0BAA0B,kBAAkB,YAAY;QACpE,MAAM,cAAc;YAClB,IAAI,gBAAgB,GAAG,IAAI,gBAAgB,EAAE;YAC7C,MAAM,gBAAgB,IAAI;YAC1B,aAAa,gBAAgB,WAAW;YACxC,MAAM,gBAAgB,IAAI;YAC1B,MAAM,gBAAgB,IAAI;YAC1B,OAAO,gBAAgB,KAAK;YAC5B,UAAU,gBAAgB,QAAQ;YAClC,cAAc,gBAAgB,YAAY;YAC1C,UAAU,gBAAgB,QAAQ;YAClC,WAAW,gBAAgB,SAAS;YACpC,WAAW,gBAAgB,SAAS;YACpC,WAAW,gBAAgB,SAAS;QACtC;QACA,QAAQ,GAAG,CAAC,yBAAyB,cAAc,YAAY;QAC/D,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,cAAc,OAAyB,EAAuB;QACzE,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,SAAS,aAAa,WAAW;gBACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ;YACrD;YACA,IAAI,SAAS,UAAU;gBACrB,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YAC5C;YACA,IAAI,SAAS,QAAQ;gBACnB,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACxC;YAEA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YAChF,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,MAAM,aAAa,CAAC,OAAO,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,iBAAiB;YACjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,gBAAgB,EAAU,EAAqB;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI;YAEpD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,kBAAkB,IAAY,EAAqB;QAC9D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM;YAE3D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,YAA+B,EACZ;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eACX,EAAU,EACV,UAA6B,EACV;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,EAAU,EAAiB;QACrD,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B,KAAK,YAAY;YAC3D,IAAI,CAAC,MAAM,OAAO,aAAa;gBAC7B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;gBACpD,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA4B,MAAM,SAAS,IAAI;YAErD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,2BAA0C;QACrD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAA2B,MAAM,SAAS,IAAI;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MACR,OAAO,OAAO,IAAI;YAEtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 3496, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useCategories.ts"], "sourcesContent": ["/**\n * React hooks for category data management\n * Provides easy-to-use hooks for CRUD operations on categories\n */\n\nimport { useCallback, useEffect, useState } from 'react';\nimport { toast } from 'sonner';\n\nimport { Category } from '@/components/pages/management/CategoryManager';\nimport {\n  CategoryApiService,\n  type CategoryFilters,\n  type CreateCategoryDto,\n  type UpdateCategoryDto,\n} from '@/lib/api/categoryApi';\n\n// Hook state types\ninterface UseCategoriesState {\n  categories: Category[];\n  loading: boolean;\n  error: string | null;\n}\n\ninterface UseCategoryState {\n  category: Category | null;\n  loading: boolean;\n  error: string | null;\n}\n\n/**\n * Hook for fetching and managing categories list\n */\nexport function useCategories(initialFilters: CategoryFilters = {}) {\n  const [state, setState] = useState<UseCategoriesState>({\n    categories: [],\n    loading: true,\n    error: null,\n  });\n\n  const [filters, setFilters] = useState<CategoryFilters>(initialFilters);\n\n  const fetchCategories = useCallback(async (newFilters?: CategoryFilters) => {\n    setState(prev => ({ ...prev, loading: true, error: null }));\n    \n    try {\n      const filtersToUse = newFilters || filters;\n      const categories = await CategoryApiService.getCategories(filtersToUse);\n      \n      setState({\n        categories,\n        loading: false,\n        error: null,\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch categories';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      toast.error('Failed to load categories');\n    }\n  }, [filters]);\n\n  // Initial fetch\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  const updateFilters = useCallback((newFilters: CategoryFilters) => {\n    setFilters(newFilters);\n    fetchCategories(newFilters);\n  }, [fetchCategories]);\n\n  return {\n    ...state,\n    filters,\n    updateFilters,\n    refetch: fetchCategories,\n  };\n}\n\n/**\n * Hook for fetching and managing a single category\n */\nexport function useCategory(id: string | null) {\n  const [state, setState] = useState<UseCategoryState>({\n    category: null,\n    loading: true,\n    error: null,\n  });\n\n  const fetchCategory = useCallback(async () => {\n    if (!id) {\n      setState({ category: null, loading: false, error: null });\n      return;\n    }\n\n    setState(prev => ({ ...prev, loading: true, error: null }));\n    \n    try {\n      const category = await CategoryApiService.getCategoryById(id);\n      setState({\n        category,\n        loading: false,\n        error: null,\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch category';\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: errorMessage,\n      }));\n      toast.error('Failed to load category');\n    }\n  }, [id]);\n\n  useEffect(() => {\n    fetchCategory();\n  }, [fetchCategory]);\n\n  return {\n    ...state,\n    refetch: fetchCategory,\n  };\n}\n\n/**\n * Hook for category CRUD operations\n */\nexport function useCategoryMutations() {\n  const [loading, setLoading] = useState(false);\n\n  const createCategory = useCallback(async (categoryData: CreateCategoryDto) => {\n    setLoading(true);\n    try {\n      const category = await CategoryApiService.createCategory(categoryData);\n      toast.success('Category created successfully');\n      return category;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const updateCategory = useCallback(async (id: string, updateData: UpdateCategoryDto) => {\n    setLoading(true);\n    try {\n      const category = await CategoryApiService.updateCategory(id, updateData);\n      toast.success('Category updated successfully');\n      return category;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const deleteCategory = useCallback(async (id: string) => {\n    setLoading(true);\n    try {\n      await CategoryApiService.deleteCategory(id);\n      toast.success('Category deleted successfully');\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete category';\n      toast.error(errorMessage);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    createCategory,\n    updateCategory,\n    deleteCategory,\n    loading,\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AAGA;;;;AAuBO,SAAS,cAAc,iBAAkC,CAAC,CAAC;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,YAAY,EAAE;QACd,SAAS;QACT,OAAO;IACT;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAExD,MAAM,kBAAkB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,IAAI;YACF,MAAM,eAAe,cAAc;YACnC,MAAM,aAAa,MAAM,yHAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;YAE1D,SAAS;gBACP;gBACA,SAAS;gBACT,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAQ;IAEZ,gBAAgB;IAChB,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,WAAW;QACX,gBAAgB;IAClB,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA,SAAS;IACX;AACF;AAKO,SAAS,YAAY,EAAiB;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,UAAU;QACV,SAAS;QACT,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,IAAI;YACP,SAAS;gBAAE,UAAU;gBAAM,SAAS;gBAAO,OAAO;YAAK;YACvD;QACF;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK,CAAC;QAEzD,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YAC1D,SAAS;gBACP;gBACA,SAAS;gBACT,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO;gBACT,CAAC;YACD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;KAAG;IAEP,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,OAAO;QACL,GAAG,KAAK;QACR,SAAS;IACX;AACF;AAKO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACzD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QACpD,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,IAAI;YAC7D,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,WAAW;QACX,IAAI;YACF,MAAM,yHAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACxC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3667, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/management/CategoryManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  Archive,\r\n  Copy,\r\n  Edit,\r\n  Eye,\r\n  Filter,\r\n  FolderOpen,\r\n  Grid3X3,\r\n  LayoutGrid,\r\n  List,\r\n  MoreHorizontal,\r\n  Package,\r\n  Pencil,\r\n  Plus,\r\n  RefreshCw,\r\n  Save,\r\n  Search,\r\n  SortAsc,\r\n  SortDesc,\r\n  Sparkles,\r\n  Tag,\r\n  Trash2,\r\n  TrendingUp,\r\n  X,\r\n} from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { EmojiPicker } from \"@/components/ui/emoji-selector\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { useCategories, useCategoryMutations } from \"@/hooks/useCategories\";\r\nimport { CategoryApiService } from \"@/lib/api/categoryApi\";\r\n\r\nexport type Category = {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  slug: string;\r\n  icon?: string;\r\n  color?: string;\r\n  isActive: boolean;\r\n  productCount: number;\r\n  parentId?: string;\r\n  sortOrder: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n};\r\n\r\ntype CategoryManagerProps = {\r\n  initialCategories?: Category[];\r\n  onCategoriesChange?: (categories: Category[]) => void;\r\n};\r\n\r\n/**\r\n * Enhanced component for managing product categories with professional UI\r\n */\r\nexport const CategoryManagerEnhanced = ({\r\n  initialCategories = [],\r\n  onCategoriesChange,\r\n}: CategoryManagerProps) => {\r\n  const router = useRouter();\r\n\r\n  // API hooks\r\n  const {\r\n    categories: apiCategories,\r\n    loading,\r\n    error,\r\n    refetch,\r\n  } = useCategories();\r\n  const {\r\n    createCategory,\r\n    updateCategory,\r\n    deleteCategory,\r\n    loading: mutationLoading,\r\n  } = useCategoryMutations();\r\n\r\n  // Local state\r\n  const [categories, setCategories] = useState<Category[]>(initialCategories);\r\n  const [newCategory, setNewCategory] = useState<Partial<Category>>({\r\n    name: \"\",\r\n    description: \"\",\r\n    icon: \"\",\r\n    color: \"#3B82F6\",\r\n    isActive: true,\r\n  });\r\n  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(\r\n    null\r\n  );\r\n  const [editForm, setEditForm] = useState<Partial<Category>>({});\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"all\");\r\n  const [sortBy, setSortBy] = useState(\"name\");\r\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\r\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\r\n  const [showAddForm, setShowAddForm] = useState(false);\r\n\r\n  // Update local categories when API data changes\r\n  useEffect(() => {\r\n    if (apiCategories.length > 0) {\r\n      setCategories(apiCategories);\r\n      if (onCategoriesChange) {\r\n        onCategoriesChange(apiCategories);\r\n      }\r\n    } else if (initialCategories.length > 0) {\r\n      setCategories(initialCategories);\r\n    }\r\n  }, [apiCategories, initialCategories, onCategoriesChange]);\r\n\r\n  // Generate a slug from the category name\r\n  const generateSlug = (name: string) => {\r\n    return name\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, \"-\")\r\n      .replace(/^-|-$/g, \"\");\r\n  };\r\n\r\n  // Filter and sort categories\r\n  const filteredAndSortedCategories = categories\r\n    .filter((category) => {\r\n      const matchesSearch =\r\n        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        category.description.toLowerCase().includes(searchQuery.toLowerCase());\r\n      const matchesFilter =\r\n        filterStatus === \"all\" ||\r\n        (filterStatus === \"active\" && category.isActive) ||\r\n        (filterStatus === \"inactive\" && !category.isActive);\r\n      return matchesSearch && matchesFilter;\r\n    })\r\n    .sort((a, b) => {\r\n      let comparison = 0;\r\n      switch (sortBy) {\r\n        case \"name\":\r\n          comparison = a.name.localeCompare(b.name);\r\n          break;\r\n        case \"products\":\r\n          comparison = a.productCount - b.productCount;\r\n          break;\r\n        case \"created\":\r\n          comparison =\r\n            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\r\n          break;\r\n        default:\r\n          comparison = a.sortOrder - b.sortOrder;\r\n      }\r\n      return sortOrder === \"asc\" ? comparison : -comparison;\r\n    });\r\n\r\n  // Add a new category\r\n  const handleAddCategory = async () => {\r\n    if (!newCategory.name) {\r\n      toast.error(\"Category name is required\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await createCategory({\r\n        name: newCategory.name,\r\n        description: newCategory.description || \"\",\r\n        icon: newCategory.icon || \"📦\",\r\n        color: newCategory.color || \"#3B82F6\",\r\n        isActive: newCategory.isActive ?? true,\r\n      });\r\n\r\n      // Reset form\r\n      setNewCategory({\r\n        name: \"\",\r\n        description: \"\",\r\n        icon: \"\",\r\n        color: \"#3B82F6\",\r\n        isActive: true,\r\n      });\r\n      setShowAddForm(false);\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      // Error is already handled in the hook\r\n      console.error(\"Failed to create category:\", error);\r\n    }\r\n  };\r\n\r\n  // Start editing a category\r\n  const handleEditStart = (category: Category) => {\r\n    setEditingCategoryId(category.id);\r\n    setEditForm({ ...category });\r\n  };\r\n\r\n  // Cancel editing\r\n  const handleEditCancel = () => {\r\n    setEditingCategoryId(null);\r\n    setEditForm({});\r\n  };\r\n\r\n  // Save edited category\r\n  const handleEditSave = async () => {\r\n    if (!editForm.name || !editingCategoryId) {\r\n      toast.error(\"Category name is required\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await updateCategory(editingCategoryId, {\r\n        name: editForm.name,\r\n        description: editForm.description,\r\n        icon: editForm.icon,\r\n        color: editForm.color,\r\n        isActive: editForm.isActive,\r\n      });\r\n\r\n      // Reset editing state\r\n      setEditingCategoryId(null);\r\n      setEditForm({});\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      // Error is already handled in the hook\r\n      console.error(\"Failed to update category:\", error);\r\n    }\r\n  };\r\n\r\n  // Delete a category\r\n  const handleDeleteCategory = async (categoryId: string) => {\r\n    try {\r\n      console.log(\"Attempting to delete category with ID:\", categoryId); // Debug log\r\n      console.log(\"Type of categoryId:\", typeof categoryId); // Debug log\r\n\r\n      if (!categoryId) {\r\n        console.error(\"Category ID is undefined or empty\");\r\n        return;\r\n      }\r\n\r\n      await deleteCategory(categoryId);\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      // Error is already handled in the hook\r\n      console.error(\"Failed to delete category:\", error);\r\n    }\r\n  };\r\n\r\n  // Recalculate product counts for all categories\r\n  const handleRecalculateProductCounts = async () => {\r\n    try {\r\n      await CategoryApiService.recalculateProductCounts();\r\n      toast.success(\"Product counts recalculated successfully\");\r\n\r\n      // Refresh categories list to show updated counts\r\n      await refetch();\r\n    } catch (error) {\r\n      console.error(\"Failed to recalculate product counts:\", error);\r\n      toast.error(\"Failed to recalculate product counts\");\r\n    }\r\n  };\r\n\r\n  // View products in a category\r\n  const handleViewProducts = (category: Category) => {\r\n    // Navigate to products page with category filter\r\n    router.push(\r\n      `/admin/products?category=${encodeURIComponent(category.name)}`\r\n    );\r\n    toast.success(`Viewing products in \"${category.name}\" category`);\r\n  };\r\n\r\n  // Duplicate a category\r\n  const handleDuplicateCategory = async (category: Category) => {\r\n    try {\r\n      const duplicatedName = `${category.name} (Copy)`;\r\n\r\n      await createCategory({\r\n        name: duplicatedName,\r\n        description: category.description || \"\",\r\n        icon: category.icon || \"📦\",\r\n        color: category.color || \"#3B82F6\",\r\n        isActive: category.isActive,\r\n      });\r\n\r\n      toast.success(`Category \"${duplicatedName}\" created successfully`);\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      console.error(\"Failed to duplicate category:\", error);\r\n      toast.error(\"Failed to duplicate category\");\r\n    }\r\n  };\r\n\r\n  // Archive/Unarchive a category (toggle isActive status)\r\n  const handleArchiveCategory = async (category: Category) => {\r\n    try {\r\n      const newStatus = !category.isActive;\r\n      const action = newStatus ? \"unarchived\" : \"archived\";\r\n\r\n      await updateCategory(category.id, {\r\n        isActive: newStatus,\r\n      });\r\n\r\n      toast.success(`Category \"${category.name}\" ${action} successfully`);\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      console.error(\"Failed to archive/unarchive category:\", error);\r\n      toast.error(\"Failed to update category status\");\r\n    }\r\n  };\r\n\r\n  // Show loading state\r\n  if (loading && categories.length === 0) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <Card>\r\n          <CardContent className=\"p-12 text-center\">\r\n            <div className=\"mx-auto h-12 w-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent\"></div>\r\n            <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\r\n              Loading categories...\r\n            </h3>\r\n            <p className=\"mt-1 text-sm text-gray-500\">\r\n              Please wait while we fetch your categories.\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state\r\n  if (error && categories.length === 0) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <Card>\r\n          <CardContent className=\"p-12 text-center\">\r\n            <div className=\"mx-auto h-12 w-12 rounded-full bg-red-100 p-3\">\r\n              <X className=\"h-6 w-6 text-red-600\" />\r\n            </div>\r\n            <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\r\n              Failed to load categories\r\n            </h3>\r\n            <p className=\"mt-1 text-sm text-gray-500\">{error}</p>\r\n            <Button className=\"mt-4\" onClick={() => refetch()}>\r\n              Try Again\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Statistics */}\r\n      <div className=\"grid gap-4 md:grid-cols-4\">\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-blue-100 p-2\">\r\n                <LayoutGrid className=\"h-5 w-5 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Total Categories</p>\r\n                <p className=\"text-2xl font-bold\">{categories.length}</p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-green-100 p-2\">\r\n                <TrendingUp className=\"h-5 w-5 text-green-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Active Categories</p>\r\n                <p className=\"text-2xl font-bold\">\r\n                  {categories.filter((c) => c.isActive).length}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-orange-100 p-2\">\r\n                <Package className=\"h-5 w-5 text-orange-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Total Products</p>\r\n                <p className=\"text-2xl font-bold\">\r\n                  {categories.reduce((sum, c) => sum + c.productCount, 0)}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-purple-100 p-2\">\r\n                <Sparkles className=\"h-5 w-5 text-purple-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Avg Products</p>\r\n                <p className=\"text-2xl font-bold\">\r\n                  {categories.length > 0\r\n                    ? Math.round(\r\n                        categories.reduce((sum, c) => sum + c.productCount, 0) /\r\n                          categories.length\r\n                      )\r\n                    : 0}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Controls */}\r\n      <Card>\r\n        <CardContent className=\"p-6\">\r\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n            {/* Search and Filters */}\r\n            <div className=\"flex flex-1 gap-4\">\r\n              <div className=\"relative max-w-md flex-1\">\r\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n                <Input\r\n                  placeholder=\"Search categories...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-10\"\r\n                />\r\n              </div>\r\n\r\n              <Select value={filterStatus} onValueChange={setFilterStatus}>\r\n                <SelectTrigger className=\"w-32\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Status</SelectItem>\r\n                  <SelectItem value=\"active\">Active</SelectItem>\r\n                  <SelectItem value=\"inactive\">Inactive</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n\r\n              <Select value={sortBy} onValueChange={setSortBy}>\r\n                <SelectTrigger className=\"w-32\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"name\">Name</SelectItem>\r\n                  <SelectItem value=\"products\">Products</SelectItem>\r\n                  <SelectItem value=\"created\">Created</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            {/* View Controls */}\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                variant={sortOrder === \"asc\" ? \"default\" : \"outline\"}\r\n                size=\"sm\"\r\n                onClick={() =>\r\n                  setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\")\r\n                }\r\n              >\r\n                {sortOrder === \"asc\" ? (\r\n                  <SortAsc className=\"h-4 w-4\" />\r\n                ) : (\r\n                  <SortDesc className=\"h-4 w-4\" />\r\n                )}\r\n              </Button>\r\n\r\n              <div className=\"flex rounded-md border\">\r\n                <Button\r\n                  variant={viewMode === \"grid\" ? \"default\" : \"ghost\"}\r\n                  size=\"sm\"\r\n                  onClick={() => setViewMode(\"grid\")}\r\n                  className=\"rounded-r-none\"\r\n                >\r\n                  <Grid3X3 className=\"h-4 w-4\" />\r\n                </Button>\r\n                <Button\r\n                  variant={viewMode === \"list\" ? \"default\" : \"ghost\"}\r\n                  size=\"sm\"\r\n                  onClick={() => setViewMode(\"list\")}\r\n                  className=\"rounded-l-none\"\r\n                >\r\n                  <List className=\"h-4 w-4\" />\r\n                </Button>\r\n              </div>\r\n\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleRecalculateProductCounts}\r\n                disabled={mutationLoading || loading}\r\n              >\r\n                <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n                Fix Counts\r\n              </Button>\r\n\r\n              <Button onClick={() => setShowAddForm(true)}>\r\n                <Plus className=\"mr-2 h-4 w-4\" />\r\n                Add Category\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Add Category Modal */}\r\n      {showAddForm && (\r\n        <Card className=\"border-2 border-blue-200 bg-blue-50/50\">\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <Plus className=\"h-5 w-5 text-blue-600\" />\r\n              Add New Category\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"grid gap-4 md:grid-cols-2\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"new-category-name\">Category Name *</Label>\r\n                <Input\r\n                  id=\"new-category-name\"\r\n                  value={newCategory.name}\r\n                  onChange={(e) =>\r\n                    setNewCategory({ ...newCategory, name: e.target.value })\r\n                  }\r\n                  placeholder=\"e.g. Electronics\"\r\n                  className=\"h-10 bg-background px-3 py-2\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <EmojiPicker\r\n                  value={newCategory.icon}\r\n                  onChange={(emoji) =>\r\n                    setNewCategory({ ...newCategory, icon: emoji })\r\n                  }\r\n                  placeholder=\"Pick an icon for this category\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <Label htmlFor=\"new-category-description\">Description</Label>\r\n                <Textarea\r\n                  id=\"new-category-description\"\r\n                  value={newCategory.description}\r\n                  onChange={(e) =>\r\n                    setNewCategory({\r\n                      ...newCategory,\r\n                      description: e.target.value,\r\n                    })\r\n                  }\r\n                  placeholder=\"Describe what products belong in this category...\"\r\n                  className=\"mt-1 bg-background\"\r\n                  rows={3}\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"new-category-color\">Color</Label>\r\n                <Input\r\n                  id=\"new-category-color\"\r\n                  type=\"color\"\r\n                  value={newCategory.color}\r\n                  onChange={(e) =>\r\n                    setNewCategory({ ...newCategory, color: e.target.value })\r\n                  }\r\n                  className=\"mt-1 h-10\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex items-center space-x-2 pt-6\">\r\n                <Switch\r\n                  id=\"new-category-active\"\r\n                  checked={newCategory.isActive}\r\n                  onCheckedChange={(checked) =>\r\n                    setNewCategory({ ...newCategory, isActive: checked })\r\n                  }\r\n                />\r\n                <Label htmlFor=\"new-category-active\">Active category</Label>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mt-6 flex gap-2\">\r\n              <Button\r\n                onClick={handleAddCategory}\r\n                disabled={mutationLoading || loading}\r\n              >\r\n                <Save className=\"mr-2 h-4 w-4\" />\r\n                {mutationLoading ? \"Adding...\" : \"Add Category\"}\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setShowAddForm(false)}\r\n                disabled={mutationLoading}\r\n              >\r\n                <X className=\"mr-2 h-4 w-4\" />\r\n                Cancel\r\n              </Button>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Categories Display */}\r\n      {viewMode === \"grid\" ? (\r\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\r\n          {filteredAndSortedCategories.map((category) => {\r\n            console.log(\"Rendering category:\", category); // Debug log\r\n            return (\r\n              <Card\r\n                key={category.id}\r\n                className=\"transition-shadow hover:shadow-md\"\r\n              >\r\n                <CardContent className=\"p-4\">\r\n                  {editingCategoryId === category.id ? (\r\n                    // Edit form\r\n                    <div className=\"space-y-3\">\r\n                      <Input\r\n                        value={editForm.name || \"\"}\r\n                        onChange={(e) =>\r\n                          setEditForm({ ...editForm, name: e.target.value })\r\n                        }\r\n                        placeholder=\"Category name\"\r\n                      />\r\n                      <EmojiPicker\r\n                        value={editForm.icon || \"\"}\r\n                        onChange={(emoji) =>\r\n                          setEditForm({ ...editForm, icon: emoji })\r\n                        }\r\n                        placeholder=\"Pick an icon\"\r\n                      />\r\n                      <Textarea\r\n                        value={editForm.description || \"\"}\r\n                        onChange={(e) =>\r\n                          setEditForm({\r\n                            ...editForm,\r\n                            description: e.target.value,\r\n                          })\r\n                        }\r\n                        placeholder=\"Description\"\r\n                        rows={2}\r\n                      />\r\n\r\n                      {/* Color and Status Controls */}\r\n                      <div className=\"flex items-end gap-4\">\r\n                        <div className=\"flex-1\">\r\n                          <Label className=\"text-sm text-gray-600\">Color</Label>\r\n                          <Input\r\n                            type=\"color\"\r\n                            value={editForm.color || \"#3B82F6\"}\r\n                            onChange={(e) =>\r\n                              setEditForm({\r\n                                ...editForm,\r\n                                color: e.target.value,\r\n                              })\r\n                            }\r\n                            className=\"mt-1 h-10\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <Switch\r\n                            className=\"mb-2.5\"\r\n                            checked={editForm.isActive ?? true}\r\n                            onCheckedChange={(checked) =>\r\n                              setEditForm({ ...editForm, isActive: checked })\r\n                            }\r\n                          />\r\n                          <Label className=\"text-sm text-gray-600\">\r\n                            {editForm.isActive ? \"Active\" : \"Inactive\"}\r\n                          </Label>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"flex gap-2\">\r\n                        <Button\r\n                          size=\"sm\"\r\n                          onClick={handleEditSave}\r\n                          disabled={mutationLoading}\r\n                        >\r\n                          <Save className=\"mr-1 h-3 w-3\" />\r\n                          {mutationLoading ? \"Saving...\" : \"Save\"}\r\n                        </Button>\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"outline\"\r\n                          onClick={handleEditCancel}\r\n                          disabled={mutationLoading}\r\n                        >\r\n                          <X className=\"h-3 w-3\" />\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    // Display mode\r\n                    <div className=\"space-y-3\">\r\n                      <div className=\"flex items-start justify-between\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <span className=\"text-2xl\">{category.icon}</span>\r\n                          <div>\r\n                            <h3 className=\"font-semibold\">{category.name}</h3>\r\n                            <Badge\r\n                              variant={\r\n                                category.isActive ? \"default\" : \"secondary\"\r\n                              }\r\n                              className=\"text-xs\"\r\n                            >\r\n                              {category.isActive ? \"Active\" : \"Inactive\"}\r\n                            </Badge>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <DropdownMenu>\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button variant=\"ghost\" size=\"sm\">\r\n                              <MoreHorizontal className=\"h-4 w-4\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent align=\"end\">\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleEditStart(category)}\r\n                            >\r\n                              <Edit className=\"mr-2 h-4 w-4\" />\r\n                              Edit\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleViewProducts(category)}\r\n                            >\r\n                              <Eye className=\"mr-2 h-4 w-4\" />\r\n                              View Products\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleDuplicateCategory(category)}\r\n                            >\r\n                              <Copy className=\"mr-2 h-4 w-4\" />\r\n                              Duplicate\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuSeparator />\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleArchiveCategory(category)}\r\n                              className={\r\n                                category.isActive\r\n                                  ? \"text-orange-600\"\r\n                                  : \"text-green-600\"\r\n                              }\r\n                            >\r\n                              <Archive className=\"mr-2 h-4 w-4\" />\r\n                              {category.isActive ? \"Archive\" : \"Unarchive\"}\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleDeleteCategory(category.id)}\r\n                              className=\"text-red-600\"\r\n                            >\r\n                              <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                              Delete\r\n                            </DropdownMenuItem>\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      </div>\r\n\r\n                      <p className=\"line-clamp-2 text-sm text-gray-600\">\r\n                        {category.description || \"No description provided\"}\r\n                      </p>\r\n\r\n                      <div className=\"flex items-center justify-between text-xs text-gray-500\">\r\n                        <span>{category.productCount} products</span>\r\n                        <span\r\n                          className=\"h-3 w-3 rounded-full\"\r\n                          style={{ backgroundColor: category.color }}\r\n                        ></span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </CardContent>\r\n              </Card>\r\n            );\r\n          })}\r\n        </div>\r\n      ) : (\r\n        // List view\r\n        <Card>\r\n          <CardContent className=\"p-0\">\r\n            <div className=\"divide-y\">\r\n              {filteredAndSortedCategories.map((category) => {\r\n                console.log(\"Rendering category in list view:\", category); // Debug log\r\n                return (\r\n                  <div key={category.id} className=\"p-4 hover:bg-gray-50\">\r\n                    {editingCategoryId === category.id ? (\r\n                      // Edit form\r\n                      <div className=\"space-y-3\">\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <Input\r\n                            value={editForm.name || \"\"}\r\n                            onChange={(e) =>\r\n                              setEditForm({ ...editForm, name: e.target.value })\r\n                            }\r\n                            placeholder=\"Category name\"\r\n                            className=\"flex-1\"\r\n                          />\r\n                          <Input\r\n                            value={editForm.description || \"\"}\r\n                            onChange={(e) =>\r\n                              setEditForm({\r\n                                ...editForm,\r\n                                description: e.target.value,\r\n                              })\r\n                            }\r\n                            placeholder=\"Description\"\r\n                            className=\"flex-1\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"max-w-xs\">\r\n                          <EmojiPicker\r\n                            value={editForm.icon || \"\"}\r\n                            onChange={(emoji) =>\r\n                              setEditForm({ ...editForm, icon: emoji })\r\n                            }\r\n                            placeholder=\"Pick an icon\"\r\n                          />\r\n                        </div>\r\n\r\n                        {/* Color and Status Controls for List View */}\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <div className=\"max-w-xs\">\r\n                            <Label className=\"text-sm text-gray-600\">\r\n                              Color\r\n                            </Label>\r\n                            <Input\r\n                              type=\"color\"\r\n                              value={editForm.color || \"#3B82F6\"}\r\n                              onChange={(e) =>\r\n                                setEditForm({\r\n                                  ...editForm,\r\n                                  color: e.target.value,\r\n                                })\r\n                              }\r\n                              className=\"mt-1 h-10\"\r\n                            />\r\n                          </div>\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <Switch\r\n                              checked={editForm.isActive ?? true}\r\n                              onCheckedChange={(checked) =>\r\n                                setEditForm({ ...editForm, isActive: checked })\r\n                              }\r\n                            />\r\n                            <Label className=\"text-sm text-gray-600\">\r\n                              {editForm.isActive ? \"Active\" : \"Inactive\"}\r\n                            </Label>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-2\">\r\n                          <Button size=\"sm\" onClick={handleEditSave}>\r\n                            <Save className=\"h-4 w-4\" />\r\n                          </Button>\r\n                          <Button\r\n                            size=\"sm\"\r\n                            variant=\"outline\"\r\n                            onClick={handleEditCancel}\r\n                          >\r\n                            <X className=\"h-4 w-4\" />\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      // Display mode\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <span className=\"text-2xl\">{category.icon}</span>\r\n                          <div>\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <h3 className=\"font-semibold\">{category.name}</h3>\r\n                              <Badge\r\n                                variant={\r\n                                  category.isActive ? \"default\" : \"secondary\"\r\n                                }\r\n                                className=\"text-xs\"\r\n                              >\r\n                                {category.isActive ? \"Active\" : \"Inactive\"}\r\n                              </Badge>\r\n                            </div>\r\n                            <p className=\"text-sm text-gray-600\">\r\n                              {category.description || \"No description\"}\r\n                            </p>\r\n                            <div className=\"mt-1 flex items-center gap-4 text-xs text-gray-500\">\r\n                              <span>{category.productCount} products</span>\r\n                              <span>Slug: {category.slug}</span>\r\n                              <div className=\"flex items-center gap-1\">\r\n                                <span>Color:</span>\r\n                                <span\r\n                                  className=\"h-3 w-3 rounded-full\"\r\n                                  style={{ backgroundColor: category.color }}\r\n                                ></span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-2\">\r\n                          <Button\r\n                            size=\"sm\"\r\n                            variant=\"outline\"\r\n                            onClick={() => handleEditStart(category)}\r\n                          >\r\n                            <Pencil className=\"h-4 w-4\" />\r\n                          </Button>\r\n                          <Button\r\n                            size=\"sm\"\r\n                            variant=\"outline\"\r\n                            onClick={() => handleDeleteCategory(category.id)}\r\n                          >\r\n                            <Trash2 className=\"h-4 w-4\" />\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Empty State */}\r\n      {filteredAndSortedCategories.length === 0 && (\r\n        <Card>\r\n          <CardContent className=\"p-12 text-center\">\r\n            <FolderOpen className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n            <h3 className=\"mt-2 text-lg font-medium text-gray-900\">\r\n              No categories found\r\n            </h3>\r\n            <p className=\"mt-1 text-sm text-gray-500\">\r\n              {searchQuery || filterStatus !== \"all\"\r\n                ? \"Try adjusting your search or filter criteria.\"\r\n                : \"Get started by creating your first category.\"}\r\n            </p>\r\n            {!searchQuery && filterStatus === \"all\" && (\r\n              <Button className=\"mt-4\" onClick={() => setShowAddForm(true)}>\r\n                <Plus className=\"mr-2 h-4 w-4\" />\r\n                Add Your First Category\r\n              </Button>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA;AACA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAvDA;;;;;;;;;;;;;;;;;;AAgFO,MAAM,0BAA0B,CAAC,EACtC,oBAAoB,EAAE,EACtB,kBAAkB,EACG;IACrB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,YAAY;IACZ,MAAM,EACJ,YAAY,aAAa,EACzB,OAAO,EACP,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD;IAChB,MAAM,EACJ,cAAc,EACd,cAAc,EACd,cAAc,EACd,SAAS,eAAe,EACzB,GAAG,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEvB,cAAc;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB;QAChE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,gDAAgD;IAChD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,cAAc;YACd,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;QACF,OAAO,IAAI,kBAAkB,MAAM,GAAG,GAAG;YACvC,cAAc;QAChB;IACF,GAAG;QAAC;QAAe;QAAmB;KAAmB;IAEzD,yCAAyC;IACzC,MAAM,eAAe,CAAC;QACpB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,UAAU;IACvB;IAEA,6BAA6B;IAC7B,MAAM,8BAA8B,WACjC,MAAM,CAAC,CAAC;QACP,MAAM,gBACJ,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACrE,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,SAAS,QAAQ,IAC9C,iBAAiB,cAAc,CAAC,SAAS,QAAQ;QACpD,OAAO,iBAAiB;IAC1B,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aAAa,EAAE,YAAY,GAAG,EAAE,YAAY;gBAC5C;YACF,KAAK;gBACH,aACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACjE;YACF;gBACE,aAAa,EAAE,SAAS,GAAG,EAAE,SAAS;QAC1C;QACA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEF,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,eAAe;gBACnB,MAAM,YAAY,IAAI;gBACtB,aAAa,YAAY,WAAW,IAAI;gBACxC,MAAM,YAAY,IAAI,IAAI;gBAC1B,OAAO,YAAY,KAAK,IAAI;gBAC5B,UAAU,YAAY,QAAQ,IAAI;YACpC;YAEA,aAAa;YACb,eAAe;gBACb,MAAM;gBACN,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,UAAU;YACZ;YACA,eAAe;YAEf,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,uCAAuC;YACvC,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,CAAC;QACvB,qBAAqB,SAAS,EAAE;QAChC,YAAY;YAAE,GAAG,QAAQ;QAAC;IAC5B;IAEA,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,qBAAqB;QACrB,YAAY,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,mBAAmB;YACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,eAAe,mBAAmB;gBACtC,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;gBACjC,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;YAC7B;YAEA,sBAAsB;YACtB,qBAAqB;YACrB,YAAY,CAAC;YAEb,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,uCAAuC;YACvC,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,oBAAoB;IACpB,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,QAAQ,GAAG,CAAC,0CAA0C,aAAa,YAAY;YAC/E,QAAQ,GAAG,CAAC,uBAAuB,OAAO,aAAa,YAAY;YAEnE,IAAI,CAAC,YAAY;gBACf,QAAQ,KAAK,CAAC;gBACd;YACF;YAEA,MAAM,eAAe;YAErB,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,uCAAuC;YACvC,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,gDAAgD;IAChD,MAAM,iCAAiC;QACrC,IAAI;YACF,MAAM,yHAAA,CAAA,qBAAkB,CAAC,wBAAwB;YACjD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,iDAAiD;YACjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB,CAAC;QAC1B,iDAAiD;QACjD,OAAO,IAAI,CACT,CAAC,yBAAyB,EAAE,mBAAmB,SAAS,IAAI,GAAG;QAEjE,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,qBAAqB,EAAE,SAAS,IAAI,CAAC,UAAU,CAAC;IACjE;IAEA,uBAAuB;IACvB,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,MAAM,iBAAiB,GAAG,SAAS,IAAI,CAAC,OAAO,CAAC;YAEhD,MAAM,eAAe;gBACnB,MAAM;gBACN,aAAa,SAAS,WAAW,IAAI;gBACrC,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,SAAS,KAAK,IAAI;gBACzB,UAAU,SAAS,QAAQ;YAC7B;YAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,eAAe,sBAAsB,CAAC;YAEjE,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,wDAAwD;IACxD,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,MAAM,YAAY,CAAC,SAAS,QAAQ;YACpC,MAAM,SAAS,YAAY,eAAe;YAE1C,MAAM,eAAe,SAAS,EAAE,EAAE;gBAChC,UAAU;YACZ;YAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,OAAO,aAAa,CAAC;YAElE,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBAAqB;IACrB,IAAI,WAAW,WAAW,MAAM,KAAK,GAAG;QACtC,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC;4BAAI,WAAU;;;;;;sCACf,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;IAOpD;IAEA,mBAAmB;IACnB,IAAI,SAAS,WAAW,MAAM,KAAK,GAAG;QACpC,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAEf,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCAA8B;;;;;;sCAC3C,6WAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;4BAAO,SAAS,IAAM;sCAAW;;;;;;;;;;;;;;;;;;;;;;IAO7D;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,GAAG,IACjB,KAAK,KAAK,CACR,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAClD,WAAW,MAAM,IAErB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAId,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAQ,eAAe;;0DACpC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,cAAc,QAAQ,YAAY;wCAC3C,MAAK;wCACL,SAAS,IACP,aAAa,cAAc,QAAQ,SAAS;kDAG7C,cAAc,sBACb,6WAAC,kTAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6WAAC,qTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAIxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,gSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU,mBAAmB;;0DAE7B,6WAAC,oSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIxC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;;0DACpC,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,6BACC,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAI9C,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IACT,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAExD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;kDACC,cAAA,6WAAC,sIAAA,CAAA,cAAW;4CACV,OAAO,YAAY,IAAI;4CACvB,UAAU,CAAC,QACT,eAAe;oDAAE,GAAG,WAAW;oDAAE,MAAM;gDAAM;4CAE/C,aAAY;;;;;;;;;;;kDAIhB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAA2B;;;;;;0DAC1C,6WAAC,6HAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,YAAY,WAAW;gDAC9B,UAAU,CAAC,IACT,eAAe;wDACb,GAAG,WAAW;wDACd,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC7B;gDAEF,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;;kDAIV,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAqB;;;;;;0DACpC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,YAAY,KAAK;gDACxB,UAAU,CAAC,IACT,eAAe;wDAAE,GAAG,WAAW;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAEzD,WAAU;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,YAAY,QAAQ;gDAC7B,iBAAiB,CAAC,UAChB,eAAe;wDAAE,GAAG,WAAW;wDAAE,UAAU;oDAAQ;;;;;;0DAGvD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAsB;;;;;;;;;;;;;;;;;;0CAIzC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,mBAAmB;;0DAE7B,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,kBAAkB,cAAc;;;;;;;kDAEnC,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe;wCAC9B,UAAU;;0DAEV,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YASvC,aAAa,uBACZ,6WAAC;gBAAI,WAAU;0BACZ,4BAA4B,GAAG,CAAC,CAAC;oBAChC,QAAQ,GAAG,CAAC,uBAAuB,WAAW,YAAY;oBAC1D,qBACE,6WAAC,yHAAA,CAAA,OAAI;wBAEH,WAAU;kCAEV,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,sBAAsB,SAAS,EAAE,GAChC,YAAY;0CACZ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0HAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAElD,aAAY;;;;;;kDAEd,6WAAC,sIAAA,CAAA,cAAW;wCACV,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,QACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM;4CAAM;wCAEzC,aAAY;;;;;;kDAEd,6WAAC,6HAAA,CAAA,WAAQ;wCACP,OAAO,SAAS,WAAW,IAAI;wCAC/B,UAAU,CAAC,IACT,YAAY;gDACV,GAAG,QAAQ;gDACX,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7B;wCAEF,aAAY;wCACZ,MAAM;;;;;;kDAIR,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,0HAAA,CAAA,QAAK;wDAAC,WAAU;kEAAwB;;;;;;kEACzC,6WAAC,0HAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,SAAS,KAAK,IAAI;wDACzB,UAAU,CAAC,IACT,YAAY;gEACV,GAAG,QAAQ;gEACX,OAAO,EAAE,MAAM,CAAC,KAAK;4DACvB;wDAEF,WAAU;;;;;;;;;;;;0DAGd,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,2HAAA,CAAA,SAAM;wDACL,WAAU;wDACV,SAAS,SAAS,QAAQ,IAAI;wDAC9B,iBAAiB,CAAC,UAChB,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU;4DAAQ;;;;;;kEAGjD,6WAAC,0HAAA,CAAA,QAAK;wDAAC,WAAU;kEACd,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;kDAKtC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,UAAU;;kEAEV,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,kBAAkB,cAAc;;;;;;;0DAEnC,6WAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;gDACT,UAAU;0DAEV,cAAA,6WAAC,gRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;uCAKnB,eAAe;0CACf,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAY,SAAS,IAAI;;;;;;kEACzC,6WAAC;;0EACC,6WAAC;gEAAG,WAAU;0EAAiB,SAAS,IAAI;;;;;;0EAC5C,6WAAC,0HAAA,CAAA,QAAK;gEACJ,SACE,SAAS,QAAQ,GAAG,YAAY;gEAElC,WAAU;0EAET,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;0DAKtC,6WAAC,qIAAA,CAAA,eAAY;;kEACX,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,gBAAgB;;kFAE/B,6WAAC,+RAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,mBAAmB;;kFAElC,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,wBAAwB;;kFAEvC,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;0EACtB,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,sBAAsB;gEACrC,WACE,SAAS,QAAQ,GACb,oBACA;;kFAGN,6WAAC,4RAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAClB,SAAS,QAAQ,GAAG,YAAY;;;;;;;0EAEnC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,qBAAqB,SAAS,EAAE;gEAC/C,WAAU;;kFAEV,6WAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kDAO3C,6WAAC;wCAAE,WAAU;kDACV,SAAS,WAAW,IAAI;;;;;;kDAG3B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;;oDAAM,SAAS,YAAY;oDAAC;;;;;;;0DAC7B,6WAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;;;;;;;;;;;;;;;;;;;;;;;uBA7J9C,SAAS,EAAE;;;;;gBAqKtB;;;;;uBAGF,YAAY;0BACZ,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;kCACZ,4BAA4B,GAAG,CAAC,CAAC;4BAChC,QAAQ,GAAG,CAAC,oCAAoC,WAAW,YAAY;4BACvE,qBACE,6WAAC;gCAAsB,WAAU;0CAC9B,sBAAsB,SAAS,EAAE,GAChC,YAAY;8CACZ,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,0HAAA,CAAA,QAAK;oDACJ,OAAO,SAAS,IAAI,IAAI;oDACxB,UAAU,CAAC,IACT,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAElD,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6WAAC,0HAAA,CAAA,QAAK;oDACJ,OAAO,SAAS,WAAW,IAAI;oDAC/B,UAAU,CAAC,IACT,YAAY;4DACV,GAAG,QAAQ;4DACX,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC7B;oDAEF,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAGd,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC,sIAAA,CAAA,cAAW;gDACV,OAAO,SAAS,IAAI,IAAI;gDACxB,UAAU,CAAC,QACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM;oDAAM;gDAEzC,aAAY;;;;;;;;;;;sDAKhB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAwB;;;;;;sEAGzC,6WAAC,0HAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,OAAO,SAAS,KAAK,IAAI;4DACzB,UAAU,CAAC,IACT,YAAY;oEACV,GAAG,QAAQ;oEACX,OAAO,EAAE,MAAM,CAAC,KAAK;gEACvB;4DAEF,WAAU;;;;;;;;;;;;8DAGd,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,2HAAA,CAAA,SAAM;4DACL,SAAS,SAAS,QAAQ,IAAI;4DAC9B,iBAAiB,CAAC,UAChB,YAAY;oEAAE,GAAG,QAAQ;oEAAE,UAAU;gEAAQ;;;;;;sEAGjD,6WAAC,0HAAA,CAAA,QAAK;4DAAC,WAAU;sEACd,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;sDAKtC,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2HAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAS;8DACzB,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS;8DAET,cAAA,6WAAC,gRAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;2CAKnB,eAAe;8CACf,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAK,WAAU;8DAAY,SAAS,IAAI;;;;;;8DACzC,6WAAC;;sEACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAG,WAAU;8EAAiB,SAAS,IAAI;;;;;;8EAC5C,6WAAC,0HAAA,CAAA,QAAK;oEACJ,SACE,SAAS,QAAQ,GAAG,YAAY;oEAElC,WAAU;8EAET,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;sEAGpC,6WAAC;4DAAE,WAAU;sEACV,SAAS,WAAW,IAAI;;;;;;sEAE3B,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;;wEAAM,SAAS,YAAY;wEAAC;;;;;;;8EAC7B,6WAAC;;wEAAK;wEAAO,SAAS,IAAI;;;;;;;8EAC1B,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;sFAAK;;;;;;sFACN,6WAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,iBAAiB,SAAS,KAAK;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOnD,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB;8DAE/B,cAAA,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,qBAAqB,SAAS,EAAE;8DAE/C,cAAA,6WAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA9HlB,SAAS,EAAE;;;;;wBAqIzB;;;;;;;;;;;;;;;;YAOP,4BAA4B,MAAM,KAAK,mBACtC,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,sSAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCACV,eAAe,iBAAiB,QAC7B,kDACA;;;;;;wBAEL,CAAC,eAAe,iBAAiB,uBAChC,6WAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;4BAAO,SAAS,IAAM,eAAe;;8CACrD,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 5644, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/management/ColorManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\n\r\nimport {\r\n  BarChart3,\r\n  Check,\r\n  ChevronDown,\r\n  ChevronUp,\r\n  Edit,\r\n  Eye,\r\n  EyeOff,\r\n  Filter,\r\n  Grid3X3,\r\n  MoreHorizontal,\r\n  Palette,\r\n  Plus,\r\n  Search,\r\n  Sparkles,\r\n  Trash2,\r\n  <PERSON><PERSON>dingUp,\r\n  X,\r\n} from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\n\r\n// Utility function to generate slug from name\r\nconst generateSlug = (name: string): string => {\r\n  return name\r\n    .toLowerCase()\r\n    .replace(/[^a-z0-9]+/g, \"-\")\r\n    .replace(/(^-|-$)/g, \"\");\r\n};\r\n\r\nexport type Color = {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  slug: string;\r\n  hexCode: string;\r\n  rgbCode?: string;\r\n  category: \"primary\" | \"secondary\" | \"neutral\" | \"accent\" | \"pastel\";\r\n  isActive: boolean;\r\n  isPopular: boolean;\r\n  productCount: number;\r\n  sortOrder: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n};\r\n\r\n// Mock colors with enhanced data\r\nconst mockColors: Color[] = [\r\n  {\r\n    id: \"color-1\",\r\n    name: \"Classic Black\",\r\n    description:\r\n      \"Timeless black color perfect for elegant and professional products\",\r\n    slug: \"classic-black\",\r\n    hexCode: \"#000000\",\r\n    rgbCode: \"rgb(0, 0, 0)\",\r\n    category: \"neutral\",\r\n    isActive: true,\r\n    isPopular: true,\r\n    productCount: 245,\r\n    sortOrder: 1,\r\n    createdAt: \"2024-01-01T00:00:00Z\",\r\n    updatedAt: \"2024-01-15T10:30:00Z\",\r\n  },\r\n  {\r\n    id: \"color-2\",\r\n    name: \"Pure White\",\r\n    description: \"Clean white color ideal for minimalist and modern designs\",\r\n    slug: \"pure-white\",\r\n    hexCode: \"#FFFFFF\",\r\n    rgbCode: \"rgb(255, 255, 255)\",\r\n    category: \"neutral\",\r\n    isActive: true,\r\n    isPopular: true,\r\n    productCount: 198,\r\n    sortOrder: 2,\r\n    createdAt: \"2024-01-02T00:00:00Z\",\r\n    updatedAt: \"2024-01-14T16:45:00Z\",\r\n  },\r\n  {\r\n    id: \"color-3\",\r\n    name: \"Ocean Blue\",\r\n    description:\r\n      \"Deep blue reminiscent of ocean depths, perfect for tech products\",\r\n    slug: \"ocean-blue\",\r\n    hexCode: \"#1E40AF\",\r\n    rgbCode: \"rgb(30, 64, 175)\",\r\n    category: \"primary\",\r\n    isActive: true,\r\n    isPopular: true,\r\n    productCount: 156,\r\n    sortOrder: 3,\r\n    createdAt: \"2024-01-03T00:00:00Z\",\r\n    updatedAt: \"2024-01-13T12:20:00Z\",\r\n  },\r\n  {\r\n    id: \"color-4\",\r\n    name: \"Forest Green\",\r\n    description:\r\n      \"Natural green color representing sustainability and eco-friendliness\",\r\n    slug: \"forest-green\",\r\n    hexCode: \"#059669\",\r\n    rgbCode: \"rgb(5, 150, 105)\",\r\n    category: \"secondary\",\r\n    isActive: true,\r\n    isPopular: false,\r\n    productCount: 89,\r\n    sortOrder: 4,\r\n    createdAt: \"2024-01-04T00:00:00Z\",\r\n    updatedAt: \"2024-01-12T09:15:00Z\",\r\n  },\r\n  {\r\n    id: \"color-5\",\r\n    name: \"Sunset Orange\",\r\n    description: \"Vibrant orange that captures attention and energy\",\r\n    slug: \"sunset-orange\",\r\n    hexCode: \"#EA580C\",\r\n    rgbCode: \"rgb(234, 88, 12)\",\r\n    category: \"accent\",\r\n    isActive: false,\r\n    isPopular: false,\r\n    productCount: 34,\r\n    sortOrder: 5,\r\n    createdAt: \"2024-01-05T00:00:00Z\",\r\n    updatedAt: \"2024-01-11T11:30:00Z\",\r\n  },\r\n  {\r\n    id: \"color-6\",\r\n    name: \"Soft Pink\",\r\n    description: \"Gentle pink color perfect for beauty and lifestyle products\",\r\n    slug: \"soft-pink\",\r\n    hexCode: \"#F472B6\",\r\n    rgbCode: \"rgb(244, 114, 182)\",\r\n    category: \"pastel\",\r\n    isActive: true,\r\n    isPopular: true,\r\n    productCount: 67,\r\n    sortOrder: 6,\r\n    createdAt: \"2024-01-06T00:00:00Z\",\r\n    updatedAt: \"2024-01-10T08:45:00Z\",\r\n  },\r\n];\r\n\r\ntype ColorManagerProps = {\r\n  initialColors?: Color[];\r\n  onColorsChange?: (colors: Color[]) => void;\r\n};\r\n\r\n/**\r\n * Enhanced component for managing product colors with professional UI\r\n */\r\nexport const ColorManagerEnhanced = ({\r\n  initialColors = mockColors,\r\n  onColorsChange,\r\n}: ColorManagerProps) => {\r\n  const [colors, setColors] = useState<Color[]>(initialColors);\r\n  const [newColor, setNewColor] = useState<Partial<Color>>({\r\n    name: \"\",\r\n    description: \"\",\r\n    hexCode: \"#000000\",\r\n    category: \"neutral\",\r\n    isActive: true,\r\n    isPopular: false,\r\n  });\r\n  const [editingColorId, setEditingColorId] = useState<string | null>(null);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterCategory, setFilterCategory] = useState<string>(\"all\");\r\n  const [filterStatus, setFilterStatus] = useState<string>(\"all\");\r\n  const [sortBy, setSortBy] = useState<string>(\"name\");\r\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\r\n  const [showAddForm, setShowAddForm] = useState(false);\r\n\r\n  // Notify parent component of changes\r\n  const notifyChange = (updatedColors: Color[]) => {\r\n    setColors(updatedColors);\r\n    onColorsChange?.(updatedColors);\r\n  };\r\n\r\n  // Filter and sort colors\r\n  const filteredColors = colors\r\n    .filter((color) => {\r\n      const matchesSearch = color.name\r\n        .toLowerCase()\r\n        .includes(searchQuery.toLowerCase());\r\n      const matchesCategory =\r\n        filterCategory === \"all\" || color.category === filterCategory;\r\n      const matchesStatus =\r\n        filterStatus === \"all\" ||\r\n        (filterStatus === \"active\" && color.isActive) ||\r\n        (filterStatus === \"inactive\" && !color.isActive);\r\n      return matchesSearch && matchesCategory && matchesStatus;\r\n    })\r\n    .sort((a, b) => {\r\n      switch (sortBy) {\r\n        case \"name\":\r\n          return a.name.localeCompare(b.name);\r\n        case \"products\":\r\n          return b.productCount - a.productCount;\r\n        case \"created\":\r\n          return (\r\n            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\r\n          );\r\n        default:\r\n          return a.sortOrder - b.sortOrder;\r\n      }\r\n    });\r\n\r\n  // Statistics\r\n  const stats = {\r\n    total: colors.length,\r\n    active: colors.filter((c) => c.isActive).length,\r\n    popular: colors.filter((c) => c.isPopular).length,\r\n    totalProducts: colors.reduce((sum, c) => sum + c.productCount, 0),\r\n  };\r\n\r\n  // Add a new color\r\n  const handleAddColor = () => {\r\n    if (!newColor.name) {\r\n      toast.error(\"Color name is required\");\r\n      return;\r\n    }\r\n\r\n    if (!newColor.hexCode || !/^#[0-9A-F]{6}$/i.test(newColor.hexCode)) {\r\n      toast.error(\"Valid hex color code is required (e.g., #FF0000)\");\r\n      return;\r\n    }\r\n\r\n    const slug = generateSlug(newColor.name);\r\n\r\n    // Check if slug already exists\r\n    if (colors.some((color) => color.slug === slug)) {\r\n      toast.error(\"A color with this name already exists\");\r\n      return;\r\n    }\r\n\r\n    // Convert hex to RGB\r\n    const hex = newColor.hexCode.replace(\"#\", \"\");\r\n    const r = parseInt(hex.substr(0, 2), 16);\r\n    const g = parseInt(hex.substr(2, 2), 16);\r\n    const b = parseInt(hex.substr(4, 2), 16);\r\n    const rgbCode = `rgb(${r}, ${g}, ${b})`;\r\n\r\n    const newColorWithId: Color = {\r\n      id: `color-${Date.now()}`,\r\n      name: newColor.name,\r\n      description: newColor.description || \"\",\r\n      slug,\r\n      hexCode: newColor.hexCode.toUpperCase(),\r\n      rgbCode,\r\n      category: newColor.category || \"neutral\",\r\n      isActive: newColor.isActive ?? true,\r\n      isPopular: newColor.isPopular ?? false,\r\n      productCount: 0,\r\n      sortOrder: colors.length + 1,\r\n      createdAt: new Date().toISOString(),\r\n      updatedAt: new Date().toISOString(),\r\n    };\r\n\r\n    const updatedColors = [...colors, newColorWithId];\r\n    notifyChange(updatedColors);\r\n    setNewColor({\r\n      name: \"\",\r\n      description: \"\",\r\n      hexCode: \"#000000\",\r\n      category: \"neutral\",\r\n      isActive: true,\r\n      isPopular: false,\r\n    });\r\n    setShowAddForm(false);\r\n    toast.success(\"Color added successfully!\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Statistics Dashboard */}\r\n      <div className=\"grid gap-4 md:grid-cols-4\">\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium text-gray-600\">\r\n              Total Colors\r\n            </CardTitle>\r\n            <Palette className=\"h-4 w-4 text-blue-600\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold text-gray-900\">\r\n              {stats.total}\r\n            </div>\r\n            <div className=\"mt-1 flex items-center text-xs text-gray-500\">\r\n              <TrendingUp className=\"mr-1 h-3 w-3 text-green-500\" />\r\n              <span>+2 this month</span>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium text-gray-600\">\r\n              Active Colors\r\n            </CardTitle>\r\n            <Eye className=\"h-4 w-4 text-green-600\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold text-gray-900\">\r\n              {stats.active}\r\n            </div>\r\n            <div className=\"mt-1 text-xs text-gray-500\">\r\n              {Math.round((stats.active / stats.total) * 100)}% of total\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium text-gray-600\">\r\n              Popular Colors\r\n            </CardTitle>\r\n            <Sparkles className=\"h-4 w-4 text-purple-600\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold text-gray-900\">\r\n              {stats.popular}\r\n            </div>\r\n            <div className=\"mt-1 text-xs text-gray-500\">High demand colors</div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n            <CardTitle className=\"text-sm font-medium text-gray-600\">\r\n              Total Products\r\n            </CardTitle>\r\n            <BarChart3 className=\"h-4 w-4 text-orange-600\" />\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"text-2xl font-bold text-gray-900\">\r\n              {stats.totalProducts}\r\n            </div>\r\n            <div className=\"mt-1 text-xs text-gray-500\">\r\n              Using color variants\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Controls */}\r\n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n        <div className=\"flex flex-1 gap-2\">\r\n          <div className=\"relative max-w-sm flex-1\">\r\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n            <Input\r\n              placeholder=\"Search colors...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              className=\"pl-10\"\r\n            />\r\n          </div>\r\n          <Select value={filterCategory} onValueChange={setFilterCategory}>\r\n            <SelectTrigger className=\"w-[140px]\">\r\n              <Filter className=\"mr-2 h-4 w-4\" />\r\n              <SelectValue />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"all\">All Categories</SelectItem>\r\n              <SelectItem value=\"primary\">Primary</SelectItem>\r\n              <SelectItem value=\"secondary\">Secondary</SelectItem>\r\n              <SelectItem value=\"neutral\">Neutral</SelectItem>\r\n              <SelectItem value=\"accent\">Accent</SelectItem>\r\n              <SelectItem value=\"pastel\">Pastel</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n          <Select value={filterStatus} onValueChange={setFilterStatus}>\r\n            <SelectTrigger className=\"w-[120px]\">\r\n              <SelectValue />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"all\">All Status</SelectItem>\r\n              <SelectItem value=\"active\">Active</SelectItem>\r\n              <SelectItem value=\"inactive\">Inactive</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n\r\n        <div className=\"flex gap-2\">\r\n          <Select value={sortBy} onValueChange={setSortBy}>\r\n            <SelectTrigger className=\"w-[140px]\">\r\n              <SelectValue />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"name\">Sort by Name</SelectItem>\r\n              <SelectItem value=\"products\">Sort by Products</SelectItem>\r\n              <SelectItem value=\"created\">Sort by Created</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={() => setViewMode(viewMode === \"grid\" ? \"list\" : \"grid\")}\r\n          >\r\n            <Grid3X3 className=\"h-4 w-4\" />\r\n          </Button>\r\n          <Button\r\n            size=\"sm\"\r\n            onClick={() => setShowAddForm(!showAddForm)}\r\n            className=\"bg-purple-600 hover:bg-purple-700\"\r\n          >\r\n            <Plus className=\"mr-2 h-4 w-4\" />\r\n            Add Color\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Add Color Form */}\r\n      {showAddForm && (\r\n        <Card className=\"border-purple-200 bg-purple-50\">\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2 text-purple-900\">\r\n              <Plus className=\"h-5 w-5\" />\r\n              Add New Color\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            <div className=\"grid gap-4 md:grid-cols-2\">\r\n              <div>\r\n                <Label htmlFor=\"color-name\">Color Name *</Label>\r\n                <Input\r\n                  id=\"color-name\"\r\n                  value={newColor.name}\r\n                  onChange={(e) =>\r\n                    setNewColor({ ...newColor, name: e.target.value })\r\n                  }\r\n                  placeholder=\"e.g., Ocean Blue\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <Label htmlFor=\"color-hex\">Hex Code *</Label>\r\n                <div className=\"flex gap-2\">\r\n                  <Input\r\n                    id=\"color-hex\"\r\n                    value={newColor.hexCode}\r\n                    onChange={(e) =>\r\n                      setNewColor({ ...newColor, hexCode: e.target.value })\r\n                    }\r\n                    placeholder=\"#000000\"\r\n                    className=\"flex-1\"\r\n                  />\r\n                  <div\r\n                    className=\"h-10 w-16 rounded border-2 border-gray-300\"\r\n                    style={{ backgroundColor: newColor.hexCode }}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div>\r\n              <Label htmlFor=\"color-description\">Description</Label>\r\n              <Textarea\r\n                id=\"color-description\"\r\n                value={newColor.description}\r\n                onChange={(e) =>\r\n                  setNewColor({ ...newColor, description: e.target.value })\r\n                }\r\n                placeholder=\"Describe this color and its best use cases...\"\r\n                rows={2}\r\n              />\r\n            </div>\r\n            <div className=\"grid gap-4 md:grid-cols-3\">\r\n              <div>\r\n                <Label htmlFor=\"color-category\">Category</Label>\r\n                <Select\r\n                  value={newColor.category}\r\n                  onValueChange={(value) =>\r\n                    setNewColor({\r\n                      ...newColor,\r\n                      category: value as Color[\"category\"],\r\n                    })\r\n                  }\r\n                >\r\n                  <SelectTrigger>\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"primary\">Primary</SelectItem>\r\n                    <SelectItem value=\"secondary\">Secondary</SelectItem>\r\n                    <SelectItem value=\"neutral\">Neutral</SelectItem>\r\n                    <SelectItem value=\"accent\">Accent</SelectItem>\r\n                    <SelectItem value=\"pastel\">Pastel</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Switch\r\n                  id=\"color-active\"\r\n                  checked={newColor.isActive}\r\n                  onCheckedChange={(checked) =>\r\n                    setNewColor({ ...newColor, isActive: checked })\r\n                  }\r\n                />\r\n                <Label htmlFor=\"color-active\">Active</Label>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Switch\r\n                  id=\"color-popular\"\r\n                  checked={newColor.isPopular}\r\n                  onCheckedChange={(checked) =>\r\n                    setNewColor({ ...newColor, isPopular: checked })\r\n                  }\r\n                />\r\n                <Label htmlFor=\"color-popular\">Popular</Label>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex gap-2 pt-2\">\r\n              <Button\r\n                onClick={handleAddColor}\r\n                className=\"bg-purple-600 hover:bg-purple-700\"\r\n              >\r\n                <Check className=\"mr-2 h-4 w-4\" />\r\n                Add Color\r\n              </Button>\r\n              <Button variant=\"outline\" onClick={() => setShowAddForm(false)}>\r\n                <X className=\"mr-2 h-4 w-4\" />\r\n                Cancel\r\n              </Button>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Colors Grid/List */}\r\n      <div\r\n        className={\r\n          viewMode === \"grid\"\r\n            ? \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\"\r\n            : \"space-y-3\"\r\n        }\r\n      >\r\n        {filteredColors.map((color) => (\r\n          <Card\r\n            key={color.id}\r\n            className={`transition-all hover:shadow-md ${\r\n              !color.isActive ? \"opacity-60\" : \"\"\r\n            }`}\r\n          >\r\n            <CardContent className=\"p-4\">\r\n              <div className=\"flex items-start justify-between\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div\r\n                    className=\"h-12 w-12 rounded-lg border-2 border-gray-200 shadow-sm\"\r\n                    style={{ backgroundColor: color.hexCode }}\r\n                  />\r\n                  <div>\r\n                    <h3 className=\"font-semibold text-gray-900\">\r\n                      {color.name}\r\n                    </h3>\r\n                    <p className=\"text-sm text-gray-600\">{color.hexCode}</p>\r\n                    <div className=\"mt-1 flex items-center gap-2\">\r\n                      <Badge\r\n                        variant=\"outline\"\r\n                        className={`text-xs ${\r\n                          color.category === \"primary\"\r\n                            ? \"border-blue-200 text-blue-700\"\r\n                            : color.category === \"secondary\"\r\n                              ? \"border-green-200 text-green-700\"\r\n                              : color.category === \"neutral\"\r\n                                ? \"border-gray-200 text-gray-700\"\r\n                                : color.category === \"accent\"\r\n                                  ? \"border-orange-200 text-orange-700\"\r\n                                  : \"border-pink-200 text-pink-700\"\r\n                        }`}\r\n                      >\r\n                        {color.category}\r\n                      </Badge>\r\n                      {color.isPopular && (\r\n                        <Badge variant=\"secondary\" className=\"text-xs\">\r\n                          <Sparkles className=\"mr-1 h-3 w-3\" />\r\n                          Popular\r\n                        </Badge>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <DropdownMenu>\r\n                  <DropdownMenuTrigger asChild>\r\n                    <Button variant=\"ghost\" size=\"sm\">\r\n                      <MoreHorizontal className=\"h-4 w-4\" />\r\n                    </Button>\r\n                  </DropdownMenuTrigger>\r\n                  <DropdownMenuContent align=\"end\">\r\n                    <DropdownMenuItem>\r\n                      <Edit className=\"mr-2 h-4 w-4\" />\r\n                      Edit\r\n                    </DropdownMenuItem>\r\n                    <DropdownMenuItem>\r\n                      {color.isActive ? (\r\n                        <>\r\n                          <EyeOff className=\"mr-2 h-4 w-4\" />\r\n                          Deactivate\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <Eye className=\"mr-2 h-4 w-4\" />\r\n                          Activate\r\n                        </>\r\n                      )}\r\n                    </DropdownMenuItem>\r\n                    <DropdownMenuSeparator />\r\n                    <DropdownMenuItem className=\"text-red-600\">\r\n                      <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                      Delete\r\n                    </DropdownMenuItem>\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              </div>\r\n              {color.description && (\r\n                <p className=\"mt-2 text-sm text-gray-600\">\r\n                  {color.description}\r\n                </p>\r\n              )}\r\n              <div className=\"mt-3 flex items-center justify-between text-sm text-gray-500\">\r\n                <span>{color.productCount} products</span>\r\n                <span\r\n                  className={color.isActive ? \"text-green-600\" : \"text-red-600\"}\r\n                >\r\n                  {color.isActive ? \"Active\" : \"Inactive\"}\r\n                </span>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n\r\n      {filteredColors.length === 0 && (\r\n        <Card>\r\n          <CardContent className=\"py-12 text-center\">\r\n            <Palette className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n            <h3 className=\"mt-4 text-lg font-semibold text-gray-900\">\r\n              No colors found\r\n            </h3>\r\n            <p className=\"mt-2 text-gray-600\">\r\n              {searchQuery || filterCategory !== \"all\" || filterStatus !== \"all\"\r\n                ? \"Try adjusting your search or filters\"\r\n                : \"Get started by adding your first color\"}\r\n            </p>\r\n            {!searchQuery &&\r\n              filterCategory === \"all\" &&\r\n              filterStatus === \"all\" && (\r\n                <Button\r\n                  className=\"mt-4 bg-purple-600 hover:bg-purple-700\"\r\n                  onClick={() => setShowAddForm(true)}\r\n                >\r\n                  <Plus className=\"mr-2 h-4 w-4\" />\r\n                  Add Your First Color\r\n                </Button>\r\n              )}\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AAOA;AACA;AA7CA;;;;;;;;;;;;;;AA+CA,8CAA8C;AAC9C,MAAM,eAAe,CAAC;IACpB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;AACzB;AAkBA,iCAAiC;AACjC,MAAM,aAAsB;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;IACb;CACD;AAUM,MAAM,uBAAuB,CAAC,EACnC,gBAAgB,UAAU,EAC1B,cAAc,EACI;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,MAAM;QACN,aAAa;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qCAAqC;IACrC,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,iBAAiB;IACnB;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,OACpB,MAAM,CAAC,CAAC;QACP,MAAM,gBAAgB,MAAM,IAAI,CAC7B,WAAW,GACX,QAAQ,CAAC,YAAY,WAAW;QACnC,MAAM,kBACJ,mBAAmB,SAAS,MAAM,QAAQ,KAAK;QACjD,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,MAAM,QAAQ,IAC3C,iBAAiB,cAAc,CAAC,MAAM,QAAQ;QACjD,OAAO,iBAAiB,mBAAmB;IAC7C,GACC,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OAAO,EAAE,YAAY,GAAG,EAAE,YAAY;YACxC,KAAK;gBACH,OACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YAEnE;gBACE,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;QACpC;IACF;IAEF,aAAa;IACb,MAAM,QAAQ;QACZ,OAAO,OAAO,MAAM;QACpB,QAAQ,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;QAC/C,SAAS,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,EAAE,MAAM;QACjD,eAAe,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE;IACjE;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,OAAO,GAAG;YAClE,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,OAAO,aAAa,SAAS,IAAI;QAEvC,+BAA+B;QAC/B,IAAI,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,IAAI,KAAK,OAAO;YAC/C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,qBAAqB;QACrB,MAAM,MAAM,SAAS,OAAO,CAAC,OAAO,CAAC,KAAK;QAC1C,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;QACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;QACrC,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;QACrC,MAAM,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAEvC,MAAM,iBAAwB;YAC5B,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW,IAAI;YACrC;YACA,SAAS,SAAS,OAAO,CAAC,WAAW;YACrC;YACA,UAAU,SAAS,QAAQ,IAAI;YAC/B,UAAU,SAAS,QAAQ,IAAI;YAC/B,WAAW,SAAS,SAAS,IAAI;YACjC,cAAc;YACd,WAAW,OAAO,MAAM,GAAG;YAC3B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,gBAAgB;eAAI;YAAQ;SAAe;QACjD,aAAa;QACb,YAAY;YACV,MAAM;YACN,aAAa;YACb,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;QACb;QACA,eAAe;QACf,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDAGzD,6WAAC,4RAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;0CAErB,6WAAC,yHAAA,CAAA,cAAW;;kDACV,6WAAC;wCAAI,WAAU;kDACZ,MAAM,KAAK;;;;;;kDAEd,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,sSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6WAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDAGzD,6WAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;0CAEjB,6WAAC,yHAAA,CAAA,cAAW;;kDACV,6WAAC;wCAAI,WAAU;kDACZ,MAAM,MAAM;;;;;;kDAEf,6WAAC;wCAAI,WAAU;;4CACZ,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;4CAAK;;;;;;;;;;;;;;;;;;;kCAKtD,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDAGzD,6WAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6WAAC,yHAAA,CAAA,cAAW;;kDACV,6WAAC;wCAAI,WAAU;kDACZ,MAAM,OAAO;;;;;;kDAEhB,6WAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAIhD,6WAAC,yHAAA,CAAA,OAAI;;0CACH,6WAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDAGzD,6WAAC,sSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,6WAAC,yHAAA,CAAA,cAAW;;kDACV,6WAAC;wCAAI,WAAU;kDACZ,MAAM,aAAa;;;;;;kDAEtB,6WAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;0BAQlD,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC,0HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAGd,6WAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAgB,eAAe;;kDAC5C,6WAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,6WAAC,2HAAA,CAAA,gBAAa;;0DACZ,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;0DAC5B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;0DAC5B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;0DAC3B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;;;;;;;;;;;;;0CAG/B,6WAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAc,eAAe;;kDAC1C,6WAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6WAAC,2HAAA,CAAA,gBAAa;;0DACZ,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;0DAC3B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAKnC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO;gCAAQ,eAAe;;kDACpC,6WAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6WAAC,2HAAA,CAAA,gBAAa;;0DACZ,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAO;;;;;;0DACzB,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,6WAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;;;;;;;;;;;;;0CAGhC,6WAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,YAAY,aAAa,SAAS,SAAS;0CAE1D,cAAA,6WAAC,gSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAErB,6WAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAOtC,6BACC,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIhC,6WAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAElD,aAAY;;;;;;;;;;;;kDAGhB,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IACT,YAAY;gEAAE,GAAG,QAAQ;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAErD,aAAY;wDACZ,WAAU;;;;;;kEAEZ,6WAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,SAAS,OAAO;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAKnD,6WAAC;;kDACC,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAoB;;;;;;kDACnC,6WAAC,6HAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAEzD,aAAY;wCACZ,MAAM;;;;;;;;;;;;0CAGV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6WAAC,2HAAA,CAAA,SAAM;gDACL,OAAO,SAAS,QAAQ;gDACxB,eAAe,CAAC,QACd,YAAY;wDACV,GAAG,QAAQ;wDACX,UAAU;oDACZ;;kEAGF,6WAAC,2HAAA,CAAA,gBAAa;kEACZ,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6WAAC,2HAAA,CAAA,gBAAa;;0EACZ,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;0EAC9B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,QAAQ;gDAC1B,iBAAiB,CAAC,UAChB,YAAY;wDAAE,GAAG,QAAQ;wDAAE,UAAU;oDAAQ;;;;;;0DAGjD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;;;;;;;kDAEhC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,SAAS;gDAC3B,iBAAiB,CAAC,UAChB,YAAY;wDAAE,GAAG,QAAQ;wDAAE,WAAW;oDAAQ;;;;;;0DAGlD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;;;;;;;;;;;;;0CAGnC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;;0DAEV,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,eAAe;;0DACtD,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,6WAAC;gBACC,WACE,aAAa,SACT,6CACA;0BAGL,eAAe,GAAG,CAAC,CAAC,sBACnB,6WAAC,yHAAA,CAAA,OAAI;wBAEH,WAAW,CAAC,+BAA+B,EACzC,CAAC,MAAM,QAAQ,GAAG,eAAe,IACjC;kCAEF,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,MAAM,OAAO;oDAAC;;;;;;8DAE1C,6WAAC;;sEACC,6WAAC;4DAAG,WAAU;sEACX,MAAM,IAAI;;;;;;sEAEb,6WAAC;4DAAE,WAAU;sEAAyB,MAAM,OAAO;;;;;;sEACnD,6WAAC;4DAAI,WAAU;;8EACb,6WAAC,0HAAA,CAAA,QAAK;oEACJ,SAAQ;oEACR,WAAW,CAAC,QAAQ,EAClB,MAAM,QAAQ,KAAK,YACf,kCACA,MAAM,QAAQ,KAAK,cACjB,oCACA,MAAM,QAAQ,KAAK,YACjB,kCACA,MAAM,QAAQ,KAAK,WACjB,sCACA,iCACV;8EAED,MAAM,QAAQ;;;;;;gEAEhB,MAAM,SAAS,kBACd,6WAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;sFACnC,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;sDAO/C,6WAAC,qIAAA,CAAA,eAAY;;8DACX,6WAAC,qIAAA,CAAA,sBAAmB;oDAAC,OAAO;8DAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,6WAAC,qIAAA,CAAA,sBAAmB;oDAAC,OAAM;;sEACzB,6WAAC,qIAAA,CAAA,mBAAgB;;8EACf,6WAAC,+RAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6WAAC,qIAAA,CAAA,mBAAgB;sEACd,MAAM,QAAQ,iBACb;;kFACE,6WAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;6FAIrC;;kFACE,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;sEAKtC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;sEACtB,6WAAC,qIAAA,CAAA,mBAAgB;4DAAC,WAAU;;8EAC1B,6WAAC,8RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;gCAM1C,MAAM,WAAW,kBAChB,6WAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;8CAGtB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;;gDAAM,MAAM,YAAY;gDAAC;;;;;;;sDAC1B,6WAAC;4CACC,WAAW,MAAM,QAAQ,GAAG,mBAAmB;sDAE9C,MAAM,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;uBArF9B,MAAM,EAAE;;;;;;;;;;YA6FlB,eAAe,MAAM,KAAK,mBACzB,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,4RAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6WAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6WAAC;4BAAE,WAAU;sCACV,eAAe,mBAAmB,SAAS,iBAAiB,QACzD,yCACA;;;;;;wBAEL,CAAC,eACA,mBAAmB,SACnB,iBAAiB,uBACf,6WAAC,2HAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 7101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/management/MaterialManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\n\r\nimport {\r\n  Archive,\r\n  Atom,\r\n  Copy,\r\n  Edit,\r\n  Eye,\r\n  Filter,\r\n  Grid3X3,\r\n  Layers,\r\n  List,\r\n  MoreHorizontal,\r\n  Package,\r\n  Pencil,\r\n  Plus,\r\n  Recycle,\r\n  Save,\r\n  Search,\r\n  Shield,\r\n  SortAsc,\r\n  SortDesc,\r\n  Sparkles,\r\n  Tag,\r\n  Trash2,\r\n  TrendingUp,\r\n  X,\r\n} from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\n\r\nexport type Material = {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  slug: string;\r\n  icon?: string;\r\n  color?: string;\r\n  type: \"natural\" | \"synthetic\" | \"composite\" | \"recycled\";\r\n  isActive: boolean;\r\n  isEcoFriendly: boolean;\r\n  isDurable: boolean;\r\n  productCount: number;\r\n  properties: string[];\r\n  careInstructions?: string;\r\n  origin?: string;\r\n  sortOrder: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n};\r\n\r\n// Mock materials with enhanced data\r\nconst mockMaterials: Material[] = [\r\n  {\r\n    id: \"mat-1\",\r\n    name: \"Organic Cotton\",\r\n    description:\r\n      \"100% organic cotton grown without harmful chemicals or pesticides\",\r\n    slug: \"organic-cotton\",\r\n    icon: \"🌱\",\r\n    color: \"#10B981\",\r\n    type: \"natural\",\r\n    isActive: true,\r\n    isEcoFriendly: true,\r\n    isDurable: true,\r\n    productCount: 156,\r\n    properties: [\"Breathable\", \"Soft\", \"Hypoallergenic\", \"Biodegradable\"],\r\n    careInstructions: \"Machine wash cold, tumble dry low\",\r\n    origin: \"India\",\r\n    sortOrder: 1,\r\n    createdAt: \"2024-01-01T00:00:00Z\",\r\n    updatedAt: \"2024-01-15T10:30:00Z\",\r\n  },\r\n  {\r\n    id: \"mat-2\",\r\n    name: \"Recycled Polyester\",\r\n    description: \"High-quality polyester made from recycled plastic bottles\",\r\n    slug: \"recycled-polyester\",\r\n    icon: \"♻️\",\r\n    color: \"#3B82F6\",\r\n    type: \"recycled\",\r\n    isActive: true,\r\n    isEcoFriendly: true,\r\n    isDurable: true,\r\n    productCount: 234,\r\n    properties: [\"Water-resistant\", \"Quick-dry\", \"Lightweight\", \"Durable\"],\r\n    careInstructions: \"Machine wash warm, hang dry\",\r\n    origin: \"Global\",\r\n    sortOrder: 2,\r\n    createdAt: \"2024-01-02T00:00:00Z\",\r\n    updatedAt: \"2024-01-14T16:45:00Z\",\r\n  },\r\n  {\r\n    id: \"mat-3\",\r\n    name: \"Genuine Leather\",\r\n    description: \"Premium full-grain leather from ethically sourced cattle\",\r\n    slug: \"genuine-leather\",\r\n    icon: \"🐄\",\r\n    color: \"#92400E\",\r\n    type: \"natural\",\r\n    isActive: true,\r\n    isEcoFriendly: false,\r\n    isDurable: true,\r\n    productCount: 89,\r\n    properties: [\"Durable\", \"Flexible\", \"Ages beautifully\", \"Water-resistant\"],\r\n    careInstructions: \"Clean with leather conditioner, avoid water\",\r\n    origin: \"Italy\",\r\n    sortOrder: 3,\r\n    createdAt: \"2024-01-03T00:00:00Z\",\r\n    updatedAt: \"2024-01-13T09:20:00Z\",\r\n  },\r\n  {\r\n    id: \"mat-4\",\r\n    name: \"Bamboo Fiber\",\r\n    description:\r\n      \"Sustainable bamboo fiber with natural antibacterial properties\",\r\n    slug: \"bamboo-fiber\",\r\n    icon: \"🎋\",\r\n    color: \"#059669\",\r\n    type: \"natural\",\r\n    isActive: true,\r\n    isEcoFriendly: true,\r\n    isDurable: false,\r\n    productCount: 67,\r\n    properties: [\"Antibacterial\", \"Moisture-wicking\", \"Soft\", \"Renewable\"],\r\n    careInstructions: \"Gentle machine wash, air dry\",\r\n    origin: \"China\",\r\n    sortOrder: 4,\r\n    createdAt: \"2024-01-04T00:00:00Z\",\r\n    updatedAt: \"2024-01-12T14:15:00Z\",\r\n  },\r\n  {\r\n    id: \"mat-5\",\r\n    name: \"Carbon Fiber\",\r\n    description: \"Ultra-lightweight and strong carbon fiber composite material\",\r\n    slug: \"carbon-fiber\",\r\n    icon: \"⚫\",\r\n    color: \"#1F2937\",\r\n    type: \"composite\",\r\n    isActive: true,\r\n    isEcoFriendly: false,\r\n    isDurable: true,\r\n    productCount: 23,\r\n    properties: [\r\n      \"Ultra-light\",\r\n      \"High strength\",\r\n      \"Corrosion resistant\",\r\n      \"Conductive\",\r\n    ],\r\n    careInstructions: \"Wipe clean with dry cloth, avoid harsh chemicals\",\r\n    origin: \"Japan\",\r\n    sortOrder: 5,\r\n    createdAt: \"2024-01-05T00:00:00Z\",\r\n    updatedAt: \"2024-01-11T11:30:00Z\",\r\n  },\r\n  {\r\n    id: \"mat-6\",\r\n    name: \"Merino Wool\",\r\n    description:\r\n      \"Premium merino wool known for its softness and temperature regulation\",\r\n    slug: \"merino-wool\",\r\n    icon: \"🐑\",\r\n    color: \"#F59E0B\",\r\n    type: \"natural\",\r\n    isActive: false,\r\n    isEcoFriendly: true,\r\n    isDurable: true,\r\n    productCount: 45,\r\n    properties: [\r\n      \"Temperature regulating\",\r\n      \"Odor resistant\",\r\n      \"Soft\",\r\n      \"Moisture-wicking\",\r\n    ],\r\n    careInstructions: \"Hand wash or gentle cycle, lay flat to dry\",\r\n    origin: \"New Zealand\",\r\n    sortOrder: 6,\r\n    createdAt: \"2024-01-06T00:00:00Z\",\r\n    updatedAt: \"2024-01-10T08:45:00Z\",\r\n  },\r\n];\r\n\r\ntype MaterialManagerProps = {\r\n  initialMaterials?: Material[];\r\n  onMaterialsChange?: (materials: Material[]) => void;\r\n};\r\n\r\n/**\r\n * Enhanced component for managing product materials with professional UI\r\n */\r\nexport const MaterialManagerEnhanced = ({\r\n  initialMaterials = mockMaterials,\r\n  onMaterialsChange,\r\n}: MaterialManagerProps) => {\r\n  const [materials, setMaterials] = useState<Material[]>(initialMaterials);\r\n  const [newMaterial, setNewMaterial] = useState<Partial<Material>>({\r\n    name: \"\",\r\n    description: \"\",\r\n    icon: \"\",\r\n    color: \"#10B981\",\r\n    type: \"natural\",\r\n    isActive: true,\r\n    isEcoFriendly: false,\r\n    isDurable: true,\r\n    properties: [],\r\n    careInstructions: \"\",\r\n    origin: \"\",\r\n  });\r\n  const [editingMaterialId, setEditingMaterialId] = useState<string | null>(\r\n    null\r\n  );\r\n  const [editForm, setEditForm] = useState<Partial<Material>>({});\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"all\");\r\n  const [filterType, setFilterType] = useState(\"all\");\r\n  const [filterEco, setFilterEco] = useState(\"all\");\r\n  const [sortBy, setSortBy] = useState(\"name\");\r\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\r\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\r\n  const [showAddForm, setShowAddForm] = useState(false);\r\n\r\n  // Generate a slug from the material name\r\n  const generateSlug = (name: string) => {\r\n    return name\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, \"-\")\r\n      .replace(/^-|-$/g, \"\");\r\n  };\r\n\r\n  // Filter and sort materials\r\n  const filteredAndSortedMaterials = materials\r\n    .filter((material) => {\r\n      const matchesSearch =\r\n        material.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        material.description\r\n          .toLowerCase()\r\n          .includes(searchQuery.toLowerCase()) ||\r\n        material.properties.some((prop) =>\r\n          prop.toLowerCase().includes(searchQuery.toLowerCase())\r\n        );\r\n\r\n      const matchesStatus =\r\n        filterStatus === \"all\" ||\r\n        (filterStatus === \"active\" && material.isActive) ||\r\n        (filterStatus === \"inactive\" && !material.isActive);\r\n\r\n      const matchesType = filterType === \"all\" || material.type === filterType;\r\n\r\n      const matchesEco =\r\n        filterEco === \"all\" ||\r\n        (filterEco === \"eco\" && material.isEcoFriendly) ||\r\n        (filterEco === \"non-eco\" && !material.isEcoFriendly);\r\n\r\n      return matchesSearch && matchesStatus && matchesType && matchesEco;\r\n    })\r\n    .sort((a, b) => {\r\n      let comparison = 0;\r\n      switch (sortBy) {\r\n        case \"name\":\r\n          comparison = a.name.localeCompare(b.name);\r\n          break;\r\n        case \"products\":\r\n          comparison = a.productCount - b.productCount;\r\n          break;\r\n        case \"type\":\r\n          comparison = a.type.localeCompare(b.type);\r\n          break;\r\n        case \"created\":\r\n          comparison =\r\n            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\r\n          break;\r\n        default:\r\n          comparison = a.sortOrder - b.sortOrder;\r\n      }\r\n      return sortOrder === \"asc\" ? comparison : -comparison;\r\n    });\r\n\r\n  // Add a new material\r\n  const handleAddMaterial = () => {\r\n    if (!newMaterial.name) {\r\n      toast.error(\"Material name is required\");\r\n      return;\r\n    }\r\n\r\n    const slug = generateSlug(newMaterial.name);\r\n\r\n    // Check if slug already exists\r\n    if (materials.some((material) => material.slug === slug)) {\r\n      toast.error(\"A material with this name already exists\");\r\n      return;\r\n    }\r\n\r\n    const newMaterialWithId: Material = {\r\n      id: `material-${Date.now()}`,\r\n      name: newMaterial.name,\r\n      description: newMaterial.description || \"\",\r\n      slug,\r\n      icon: newMaterial.icon || \"🧵\",\r\n      color: newMaterial.color || \"#10B981\",\r\n      type: newMaterial.type || \"natural\",\r\n      isActive: newMaterial.isActive ?? true,\r\n      isEcoFriendly: newMaterial.isEcoFriendly ?? false,\r\n      isDurable: newMaterial.isDurable ?? true,\r\n      productCount: 0,\r\n      properties: newMaterial.properties || [],\r\n      careInstructions: newMaterial.careInstructions || \"\",\r\n      origin: newMaterial.origin || \"\",\r\n      sortOrder: materials.length + 1,\r\n      createdAt: new Date().toISOString(),\r\n      updatedAt: new Date().toISOString(),\r\n    };\r\n\r\n    const updatedMaterials = [...materials, newMaterialWithId];\r\n    setMaterials(updatedMaterials);\r\n    setNewMaterial({\r\n      name: \"\",\r\n      description: \"\",\r\n      icon: \"\",\r\n      color: \"#10B981\",\r\n      type: \"natural\",\r\n      isActive: true,\r\n      isEcoFriendly: false,\r\n      isDurable: true,\r\n      properties: [],\r\n      careInstructions: \"\",\r\n      origin: \"\",\r\n    });\r\n    setShowAddForm(false);\r\n\r\n    // Notify parent component\r\n    if (onMaterialsChange) {\r\n      onMaterialsChange(updatedMaterials);\r\n    }\r\n\r\n    toast.success(\"Material added successfully\");\r\n  };\r\n\r\n  // Start editing a material\r\n  const handleEditStart = (material: Material) => {\r\n    setEditingMaterialId(material.id);\r\n    setEditForm({ ...material });\r\n  };\r\n\r\n  // Cancel editing\r\n  const handleEditCancel = () => {\r\n    setEditingMaterialId(null);\r\n    setEditForm({});\r\n  };\r\n\r\n  // Save edited material\r\n  const handleEditSave = () => {\r\n    if (!editForm.name) {\r\n      toast.error(\"Material name is required\");\r\n      return;\r\n    }\r\n\r\n    const updatedMaterials = materials.map((material) =>\r\n      material.id === editingMaterialId\r\n        ? {\r\n            ...material,\r\n            name: editForm.name || material.name,\r\n            description: editForm.description || material.description,\r\n            icon: editForm.icon || material.icon,\r\n            color: editForm.color || material.color,\r\n            type: editForm.type || material.type,\r\n            isActive: editForm.isActive ?? material.isActive,\r\n            isEcoFriendly: editForm.isEcoFriendly ?? material.isEcoFriendly,\r\n            isDurable: editForm.isDurable ?? material.isDurable,\r\n            properties: editForm.properties || material.properties,\r\n            careInstructions:\r\n              editForm.careInstructions || material.careInstructions,\r\n            origin: editForm.origin || material.origin,\r\n            // Only update slug if name changed\r\n            slug:\r\n              material.name !== editForm.name\r\n                ? generateSlug(editForm.name)\r\n                : material.slug,\r\n            updatedAt: new Date().toISOString(),\r\n          }\r\n        : material\r\n    );\r\n\r\n    setMaterials(updatedMaterials);\r\n    setEditingMaterialId(null);\r\n    setEditForm({});\r\n\r\n    // Notify parent component\r\n    if (onMaterialsChange) {\r\n      onMaterialsChange(updatedMaterials);\r\n    }\r\n\r\n    toast.success(\"Material updated successfully\");\r\n  };\r\n\r\n  // Delete a material\r\n  const handleDeleteMaterial = (materialId: string) => {\r\n    const updatedMaterials = materials.filter(\r\n      (material) => material.id !== materialId\r\n    );\r\n    setMaterials(updatedMaterials);\r\n\r\n    // Notify parent component\r\n    if (onMaterialsChange) {\r\n      onMaterialsChange(updatedMaterials);\r\n    }\r\n\r\n    toast.success(\"Material deleted successfully\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Statistics */}\r\n      <div className=\"grid gap-4 md:grid-cols-4\">\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-purple-100 p-2\">\r\n                <Layers className=\"h-5 w-5 text-purple-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Total Materials</p>\r\n                <p className=\"text-2xl font-bold\">{materials.length}</p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-green-100 p-2\">\r\n                <Recycle className=\"h-5 w-5 text-green-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Eco-Friendly</p>\r\n                <p className=\"text-2xl font-bold\">\r\n                  {materials.filter((m) => m.isEcoFriendly).length}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-blue-100 p-2\">\r\n                <Shield className=\"h-5 w-5 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Durable</p>\r\n                <p className=\"text-2xl font-bold\">\r\n                  {materials.filter((m) => m.isDurable).length}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-orange-100 p-2\">\r\n                <Atom className=\"h-5 w-5 text-orange-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Natural</p>\r\n                <p className=\"text-2xl font-bold\">\r\n                  {materials.filter((m) => m.type === \"natural\").length}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Controls */}\r\n      <Card>\r\n        <CardContent className=\"p-6\">\r\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n            {/* Search and Filters */}\r\n            <div className=\"flex flex-1 gap-4\">\r\n              <div className=\"relative max-w-md flex-1\">\r\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n                <Input\r\n                  placeholder=\"Search materials...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-10\"\r\n                />\r\n              </div>\r\n\r\n              <Select value={filterStatus} onValueChange={setFilterStatus}>\r\n                <SelectTrigger className=\"w-32\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Status</SelectItem>\r\n                  <SelectItem value=\"active\">Active</SelectItem>\r\n                  <SelectItem value=\"inactive\">Inactive</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n\r\n              <Select value={filterType} onValueChange={setFilterType}>\r\n                <SelectTrigger className=\"w-32\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Types</SelectItem>\r\n                  <SelectItem value=\"natural\">Natural</SelectItem>\r\n                  <SelectItem value=\"synthetic\">Synthetic</SelectItem>\r\n                  <SelectItem value=\"composite\">Composite</SelectItem>\r\n                  <SelectItem value=\"recycled\">Recycled</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n\r\n              <Select value={filterEco} onValueChange={setFilterEco}>\r\n                <SelectTrigger className=\"w-32\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Eco</SelectItem>\r\n                  <SelectItem value=\"eco\">Eco-Friendly</SelectItem>\r\n                  <SelectItem value=\"non-eco\">Standard</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            {/* View Controls */}\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                variant={sortOrder === \"asc\" ? \"default\" : \"outline\"}\r\n                size=\"sm\"\r\n                onClick={() =>\r\n                  setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\")\r\n                }\r\n              >\r\n                {sortOrder === \"asc\" ? (\r\n                  <SortAsc className=\"h-4 w-4\" />\r\n                ) : (\r\n                  <SortDesc className=\"h-4 w-4\" />\r\n                )}\r\n              </Button>\r\n\r\n              <div className=\"flex rounded-md border\">\r\n                <Button\r\n                  variant={viewMode === \"grid\" ? \"default\" : \"ghost\"}\r\n                  size=\"sm\"\r\n                  onClick={() => setViewMode(\"grid\")}\r\n                  className=\"rounded-r-none\"\r\n                >\r\n                  <Grid3X3 className=\"h-4 w-4\" />\r\n                </Button>\r\n                <Button\r\n                  variant={viewMode === \"list\" ? \"default\" : \"ghost\"}\r\n                  size=\"sm\"\r\n                  onClick={() => setViewMode(\"list\")}\r\n                  className=\"rounded-l-none\"\r\n                >\r\n                  <List className=\"h-4 w-4\" />\r\n                </Button>\r\n              </div>\r\n\r\n              <Button onClick={() => setShowAddForm(true)}>\r\n                <Plus className=\"mr-2 h-4 w-4\" />\r\n                Add Material\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Materials Display - Grid View */}\r\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\r\n        {filteredAndSortedMaterials.map((material) => (\r\n          <Card key={material.id} className=\"transition-shadow hover:shadow-md\">\r\n            <CardContent className=\"p-4\">\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <span className=\"text-3xl\">{material.icon}</span>\r\n                    <div>\r\n                      <h3 className=\"font-semibold\">{material.name}</h3>\r\n                      <div className=\"flex flex-wrap gap-1\">\r\n                        <Badge\r\n                          variant={material.isActive ? \"default\" : \"secondary\"}\r\n                          className=\"text-xs\"\r\n                        >\r\n                          {material.isActive ? \"Active\" : \"Inactive\"}\r\n                        </Badge>\r\n                        <Badge\r\n                          variant=\"outline\"\r\n                          className=\"text-xs\"\r\n                          style={{\r\n                            borderColor: material.color,\r\n                            color: material.color,\r\n                          }}\r\n                        >\r\n                          {material.type}\r\n                        </Badge>\r\n                        {material.isEcoFriendly && (\r\n                          <Badge\r\n                            variant=\"outline\"\r\n                            className=\"border-green-200 text-xs text-green-600\"\r\n                          >\r\n                            Eco-Friendly\r\n                          </Badge>\r\n                        )}\r\n                        {material.isDurable && (\r\n                          <Badge\r\n                            variant=\"outline\"\r\n                            className=\"border-blue-200 text-xs text-blue-600\"\r\n                          >\r\n                            Durable\r\n                          </Badge>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                      <Button variant=\"ghost\" size=\"sm\">\r\n                        <MoreHorizontal className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent align=\"end\">\r\n                      <DropdownMenuItem\r\n                        onClick={() => handleEditStart(material)}\r\n                      >\r\n                        <Edit className=\"mr-2 h-4 w-4\" />\r\n                        Edit\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem>\r\n                        <Eye className=\"mr-2 h-4 w-4\" />\r\n                        View Products\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem>\r\n                        <Copy className=\"mr-2 h-4 w-4\" />\r\n                        Duplicate\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuSeparator />\r\n                      <DropdownMenuItem>\r\n                        <Archive className=\"mr-2 h-4 w-4\" />\r\n                        Archive\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuItem\r\n                        onClick={() => handleDeleteMaterial(material.id)}\r\n                        className=\"text-red-600\"\r\n                      >\r\n                        <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                        Delete\r\n                      </DropdownMenuItem>\r\n                    </DropdownMenuContent>\r\n                  </DropdownMenu>\r\n                </div>\r\n\r\n                <p className=\"line-clamp-2 text-sm text-gray-600\">\r\n                  {material.description || \"No description provided\"}\r\n                </p>\r\n\r\n                <div className=\"space-y-2 text-xs text-gray-500\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <span>{material.productCount} products</span>\r\n                    {material.origin && <span>Origin: {material.origin}</span>}\r\n                  </div>\r\n\r\n                  {material.properties.length > 0 && (\r\n                    <div className=\"flex flex-wrap gap-1\">\r\n                      {material.properties\r\n                        .slice(0, 3)\r\n                        .map((property, index) => (\r\n                          <span\r\n                            key={index}\r\n                            className=\"rounded bg-gray-100 px-2 py-1 text-xs\"\r\n                          >\r\n                            {property}\r\n                          </span>\r\n                        ))}\r\n                      {material.properties.length > 3 && (\r\n                        <span className=\"rounded bg-gray-100 px-2 py-1 text-xs\">\r\n                          +{material.properties.length - 3} more\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n\r\n                  {material.careInstructions && (\r\n                    <p className=\"text-xs italic text-gray-400\">\r\n                      Care: {material.careInstructions}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Empty State */}\r\n      {filteredAndSortedMaterials.length === 0 && (\r\n        <Card>\r\n          <CardContent className=\"p-12 text-center\">\r\n            <Layers className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n            <h3 className=\"mt-2 text-lg font-medium text-gray-900\">\r\n              No materials found\r\n            </h3>\r\n            <p className=\"mt-1 text-sm text-gray-500\">\r\n              {searchQuery ||\r\n              filterStatus !== \"all\" ||\r\n              filterType !== \"all\" ||\r\n              filterEco !== \"all\"\r\n                ? \"Try adjusting your search or filter criteria.\"\r\n                : \"Get started by adding your first material.\"}\r\n            </p>\r\n            {!searchQuery &&\r\n              filterStatus === \"all\" &&\r\n              filterType === \"all\" &&\r\n              filterEco === \"all\" && (\r\n                <Button className=\"mt-4\" onClick={() => setShowAddForm(true)}>\r\n                  <Plus className=\"mr-2 h-4 w-4\" />\r\n                  Add Your First Material\r\n                </Button>\r\n              )}\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0BA;AAEA;AACA;AACA;AACA;AAOA;AAEA;AA5CA;;;;;;;;;;;AA0EA,oCAAoC;AACpC,MAAM,gBAA4B;IAChC;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YAAC;YAAc;YAAQ;YAAkB;SAAgB;QACrE,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YAAC;YAAmB;YAAa;YAAe;SAAU;QACtE,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YAAC;YAAW;YAAY;YAAoB;SAAkB;QAC1E,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YAAC;YAAiB;YAAoB;YAAQ;SAAY;QACtE,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,cAAc;QACd,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;CACD;AAUM,MAAM,0BAA0B,CAAC,EACtC,mBAAmB,aAAa,EAChC,iBAAiB,EACI;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAc;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB;QAChE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;QACX,YAAY,EAAE;QACd,kBAAkB;QAClB,QAAQ;IACV;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,yCAAyC;IACzC,MAAM,eAAe,CAAC;QACpB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,UAAU;IACvB;IAEA,4BAA4B;IAC5B,MAAM,6BAA6B,UAChC,MAAM,CAAC,CAAC;QACP,MAAM,gBACJ,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,SAAS,WAAW,CACjB,WAAW,GACX,QAAQ,CAAC,YAAY,WAAW,OACnC,SAAS,UAAU,CAAC,IAAI,CAAC,CAAC,OACxB,KAAK,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAGvD,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,SAAS,QAAQ,IAC9C,iBAAiB,cAAc,CAAC,SAAS,QAAQ;QAEpD,MAAM,cAAc,eAAe,SAAS,SAAS,IAAI,KAAK;QAE9D,MAAM,aACJ,cAAc,SACb,cAAc,SAAS,SAAS,aAAa,IAC7C,cAAc,aAAa,CAAC,SAAS,aAAa;QAErD,OAAO,iBAAiB,iBAAiB,eAAe;IAC1D,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aAAa,EAAE,YAAY,GAAG,EAAE,YAAY;gBAC5C;YACF,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACjE;YACF;gBACE,aAAa,EAAE,SAAS,GAAG,EAAE,SAAS;QAC1C;QACA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEF,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,OAAO,aAAa,YAAY,IAAI;QAE1C,+BAA+B;QAC/B,IAAI,UAAU,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI,KAAK,OAAO;YACxD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,oBAA8B;YAClC,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;YAC5B,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,WAAW,IAAI;YACxC;YACA,MAAM,YAAY,IAAI,IAAI;YAC1B,OAAO,YAAY,KAAK,IAAI;YAC5B,MAAM,YAAY,IAAI,IAAI;YAC1B,UAAU,YAAY,QAAQ,IAAI;YAClC,eAAe,YAAY,aAAa,IAAI;YAC5C,WAAW,YAAY,SAAS,IAAI;YACpC,cAAc;YACd,YAAY,YAAY,UAAU,IAAI,EAAE;YACxC,kBAAkB,YAAY,gBAAgB,IAAI;YAClD,QAAQ,YAAY,MAAM,IAAI;YAC9B,WAAW,UAAU,MAAM,GAAG;YAC9B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,mBAAmB;eAAI;YAAW;SAAkB;QAC1D,aAAa;QACb,eAAe;YACb,MAAM;YACN,aAAa;YACb,MAAM;YACN,OAAO;YACP,MAAM;YACN,UAAU;YACV,eAAe;YACf,WAAW;YACX,YAAY,EAAE;YACd,kBAAkB;YAClB,QAAQ;QACV;QACA,eAAe;QAEf,0BAA0B;QAC1B,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,CAAC;QACvB,qBAAqB,SAAS,EAAE;QAChC,YAAY;YAAE,GAAG,QAAQ;QAAC;IAC5B;IAEA,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,qBAAqB;QACrB,YAAY,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAC,WACtC,SAAS,EAAE,KAAK,oBACZ;gBACE,GAAG,QAAQ;gBACX,MAAM,SAAS,IAAI,IAAI,SAAS,IAAI;gBACpC,aAAa,SAAS,WAAW,IAAI,SAAS,WAAW;gBACzD,MAAM,SAAS,IAAI,IAAI,SAAS,IAAI;gBACpC,OAAO,SAAS,KAAK,IAAI,SAAS,KAAK;gBACvC,MAAM,SAAS,IAAI,IAAI,SAAS,IAAI;gBACpC,UAAU,SAAS,QAAQ,IAAI,SAAS,QAAQ;gBAChD,eAAe,SAAS,aAAa,IAAI,SAAS,aAAa;gBAC/D,WAAW,SAAS,SAAS,IAAI,SAAS,SAAS;gBACnD,YAAY,SAAS,UAAU,IAAI,SAAS,UAAU;gBACtD,kBACE,SAAS,gBAAgB,IAAI,SAAS,gBAAgB;gBACxD,QAAQ,SAAS,MAAM,IAAI,SAAS,MAAM;gBAC1C,mCAAmC;gBACnC,MACE,SAAS,IAAI,KAAK,SAAS,IAAI,GAC3B,aAAa,SAAS,IAAI,IAC1B,SAAS,IAAI;gBACnB,WAAW,IAAI,OAAO,WAAW;YACnC,IACA;QAGN,aAAa;QACb,qBAAqB;QACrB,YAAY,CAAC;QAEb,0BAA0B;QAC1B,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,oBAAoB;IACpB,MAAM,uBAAuB,CAAC;QAC5B,MAAM,mBAAmB,UAAU,MAAM,CACvC,CAAC,WAAa,SAAS,EAAE,KAAK;QAEhC,aAAa;QAEb,0BAA0B;QAC1B,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM3D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjE,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAId,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAW,eAAe;;0DACvC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,cAAc,QAAQ,YAAY;wCAC3C,MAAK;wCACL,SAAS,IACP,aAAa,cAAc,QAAQ,SAAS;kDAG7C,cAAc,sBACb,6WAAC,kTAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6WAAC,qTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAIxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,gSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;;0DACpC,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,6WAAC;gBAAI,WAAU;0BACZ,2BAA2B,GAAG,CAAC,CAAC,yBAC/B,6WAAC,yHAAA,CAAA,OAAI;wBAAmB,WAAU;kCAChC,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAY,SAAS,IAAI;;;;;;kEACzC,6WAAC;;0EACC,6WAAC;gEAAG,WAAU;0EAAiB,SAAS,IAAI;;;;;;0EAC5C,6WAAC;gEAAI,WAAU;;kFACb,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAS,SAAS,QAAQ,GAAG,YAAY;wEACzC,WAAU;kFAET,SAAS,QAAQ,GAAG,WAAW;;;;;;kFAElC,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,WAAU;wEACV,OAAO;4EACL,aAAa,SAAS,KAAK;4EAC3B,OAAO,SAAS,KAAK;wEACvB;kFAEC,SAAS,IAAI;;;;;;oEAEf,SAAS,aAAa,kBACrB,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,WAAU;kFACX;;;;;;oEAIF,SAAS,SAAS,kBACjB,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;0DAQT,6WAAC,qIAAA,CAAA,eAAY;;kEACX,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,gBAAgB;;kFAE/B,6WAAC,+RAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;0EACtB,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,4RAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGtC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,qBAAqB,SAAS,EAAE;gEAC/C,WAAU;;kFAEV,6WAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kDAO3C,6WAAC;wCAAE,WAAU;kDACV,SAAS,WAAW,IAAI;;;;;;kDAG3B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;;4DAAM,SAAS,YAAY;4DAAC;;;;;;;oDAC5B,SAAS,MAAM,kBAAI,6WAAC;;4DAAK;4DAAS,SAAS,MAAM;;;;;;;;;;;;;4CAGnD,SAAS,UAAU,CAAC,MAAM,GAAG,mBAC5B,6WAAC;gDAAI,WAAU;;oDACZ,SAAS,UAAU,CACjB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,UAAU,sBACd,6WAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;oDAMV,SAAS,UAAU,CAAC,MAAM,GAAG,mBAC5B,6WAAC;wDAAK,WAAU;;4DAAwC;4DACpD,SAAS,UAAU,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;4CAMxC,SAAS,gBAAgB,kBACxB,6WAAC;gDAAE,WAAU;;oDAA+B;oDACnC,SAAS,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;uBAlHjC,SAAS,EAAE;;;;;;;;;;YA6HzB,2BAA2B,MAAM,KAAK,mBACrC,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,0RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCACV,eACD,iBAAiB,SACjB,eAAe,SACf,cAAc,QACV,kDACA;;;;;;wBAEL,CAAC,eACA,iBAAiB,SACjB,eAAe,SACf,cAAc,uBACZ,6WAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;4BAAO,SAAS,IAAM,eAAe;;8CACrD,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 8434, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,6QAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,6QAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 8487, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/app/admin/products/catalog-settings/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>3,\r\n  Download,\r\n  Filter,\r\n  Hammer,\r\n  Layers,\r\n  LayoutGrid,\r\n  Package,\r\n  Palette,\r\n  Plus,\r\n  Search,\r\n  Sparkles,\r\n  Tag,\r\n  TrendingUp,\r\n  Upload,\r\n} from \"lucide-react\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\n\r\nimport { PageHeaderWrapper } from \"@/components/common/PageHeaderWrapper\";\r\nimport { BrandManagerEnhanced } from \"@/components/pages/brands/BrandManagerEnhanced\";\r\nimport { CategoryManagerEnhanced } from \"@/components/pages/management/CategoryManagerEnhanced\";\r\nimport { ColorManagerEnhanced } from \"@/components/pages/management/ColorManagerEnhanced\";\r\nimport { MaterialManagerEnhanced } from \"@/components/pages/management/MaterialManagerEnhanced\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from \"@/components/ui/tabs\";\r\n\r\nexport default function CatalogSettings() {\r\n  const [activeTab, setActiveTab] = useState(\"overview\");\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n\r\n  // Handle URL parameters for direct tab navigation\r\n  useEffect(() => {\r\n    const tab = searchParams.get(\"tab\");\r\n    if (\r\n      tab &&\r\n      [\"overview\", \"categories\", \"brands\", \"materials\", \"colors\"].includes(tab)\r\n    ) {\r\n      setActiveTab(tab);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  // Function to handle tab changes and update URL\r\n  const handleTabChange = (newTab: string) => {\r\n    setActiveTab(newTab);\r\n    // Update URL with new tab parameter\r\n    const newSearchParams = new URLSearchParams(searchParams.toString());\r\n    newSearchParams.set(\"tab\", newTab);\r\n    router.push(\r\n      `/admin/products/catalog-settings?${newSearchParams.toString()}`\r\n    );\r\n  };\r\n\r\n  // Unified stats for all catalog metadata\r\n  const overallStats = [\r\n    {\r\n      label: \"Total Categories\",\r\n      value: \"18\",\r\n      change: \"+2\",\r\n      trend: \"up\",\r\n      icon: LayoutGrid,\r\n    },\r\n    {\r\n      label: \"Total Brands\",\r\n      value: \"24\",\r\n      change: \"+3\",\r\n      trend: \"up\",\r\n      icon: Tag,\r\n    },\r\n    {\r\n      label: \"Total Materials\",\r\n      value: \"32\",\r\n      change: \"+4\",\r\n      trend: \"up\",\r\n      icon: Hammer,\r\n    },\r\n    {\r\n      label: \"Total Colors\",\r\n      value: \"28\",\r\n      change: \"+2\",\r\n      trend: \"up\",\r\n      icon: Palette,\r\n    },\r\n  ];\r\n\r\n  const quickActions = [\r\n    { label: \"Import Data\", icon: Upload, action: () => console.log(\"Import\") },\r\n    {\r\n      label: \"Export All\",\r\n      icon: Download,\r\n      action: () => console.log(\"Export\"),\r\n    },\r\n    {\r\n      label: \"Catalog Analytics\",\r\n      icon: BarChart3,\r\n      action: () => console.log(\"Analytics\"),\r\n    },\r\n  ];\r\n\r\n  // Cross-reference data showing relationships\r\n  const catalogRelationships = [\r\n    {\r\n      category: \"Electronics\",\r\n      brands: [\"Apple\", \"Samsung\", \"Sony\"],\r\n      materials: [\"Aluminum\", \"Glass\", \"Plastic\"],\r\n      colors: [\"Black\", \"White\", \"Silver\"],\r\n      products: 324,\r\n    },\r\n    {\r\n      category: \"Clothing\",\r\n      brands: [\"Nike\", \"Adidas\", \"H&M\"],\r\n      materials: [\"Cotton\", \"Polyester\", \"Wool\"],\r\n      colors: [\"Red\", \"Blue\", \"Green\"],\r\n      products: 289,\r\n    },\r\n    {\r\n      category: \"Home & Garden\",\r\n      brands: [\"IKEA\", \"Home Depot\", \"Wayfair\"],\r\n      materials: [\"Wood\", \"Metal\", \"Fabric\"],\r\n      colors: [\"Brown\", \"White\", \"Gray\"],\r\n      products: 156,\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <PageHeaderWrapper\r\n        title=\"Catalog Settings\"\r\n        description=\"Manage categories, brands, and materials for your product catalog in one unified interface\"\r\n      >\r\n        <div className=\"flex gap-2\">\r\n          {quickActions.map((action, index) => (\r\n            <Button\r\n              key={index}\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={action.action}\r\n              className=\"hidden sm:flex\"\r\n            >\r\n              <action.icon className=\"mr-2 h-4 w-4\" />\r\n              {action.label}\r\n            </Button>\r\n          ))}\r\n          <Button size=\"sm\">\r\n            <Plus className=\"mr-2 h-4 w-4\" />\r\n            Quick Add\r\n          </Button>\r\n        </div>\r\n      </PageHeaderWrapper>\r\n\r\n      <div className=\"container mx-auto mt-6 space-y-6\">\r\n        <Tabs\r\n          value={activeTab}\r\n          onValueChange={handleTabChange}\r\n          className=\"w-full\"\r\n        >\r\n          <TabsList className=\"grid w-full grid-cols-5\">\r\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\r\n            <TabsTrigger value=\"categories\">Categories</TabsTrigger>\r\n            <TabsTrigger value=\"brands\">Brands</TabsTrigger>\r\n            <TabsTrigger value=\"materials\">Materials</TabsTrigger>\r\n            <TabsTrigger value=\"colors\">Colors</TabsTrigger>\r\n          </TabsList>\r\n\r\n          {/* Overview Tab */}\r\n          <TabsContent value=\"overview\" className=\"space-y-6\">\r\n            {/* Overall Stats */}\r\n            <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\r\n              {overallStats.map((stat, index) => (\r\n                <Card key={index}>\r\n                  <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n                    <CardTitle className=\"text-sm font-medium text-gray-600\">\r\n                      {stat.label}\r\n                    </CardTitle>\r\n                    <stat.icon className=\"h-4 w-4 text-blue-600\" />\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className=\"text-2xl font-bold text-gray-900\">\r\n                      {stat.value}\r\n                    </div>\r\n                    <div className=\"mt-1 flex items-center text-xs text-gray-500\">\r\n                      {stat.trend === \"up\" && (\r\n                        <TrendingUp className=\"mr-1 h-3 w-3 text-green-500\" />\r\n                      )}\r\n                      <span>{stat.change}</span>\r\n                      {stat.trend === \"up\" && (\r\n                        <span className=\"ml-1\">this month</span>\r\n                      )}\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Catalog Relationships */}\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <Layers className=\"h-5 w-5 text-blue-600\" />\r\n                  Catalog Relationships\r\n                </CardTitle>\r\n                <p className=\"text-sm text-gray-600\">\r\n                  See how categories, brands, and materials work together in\r\n                  your catalog\r\n                </p>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"space-y-4\">\r\n                  {catalogRelationships.map((item, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"rounded-lg border bg-gray-50 p-4\"\r\n                    >\r\n                      <div className=\"mb-3 flex items-center justify-between\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <LayoutGrid className=\"h-5 w-5 text-blue-600\" />\r\n                          <span className=\"font-semibold text-gray-900\">\r\n                            {item.category}\r\n                          </span>\r\n                          <Badge variant=\"secondary\">\r\n                            {item.products} products\r\n                          </Badge>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"grid gap-4 md:grid-cols-2\">\r\n                        <div>\r\n                          <div className=\"mb-2 flex items-center gap-2\">\r\n                            <Tag className=\"h-4 w-4 text-green-600\" />\r\n                            <span className=\"text-sm font-medium text-gray-700\">\r\n                              Brands\r\n                            </span>\r\n                          </div>\r\n                          <div className=\"flex flex-wrap gap-1\">\r\n                            {item.brands.map((brand, idx) => (\r\n                              <Badge\r\n                                key={idx}\r\n                                variant=\"outline\"\r\n                                className=\"text-xs\"\r\n                              >\r\n                                {brand}\r\n                              </Badge>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div>\r\n                          <div className=\"mb-2 flex items-center gap-2\">\r\n                            <Hammer className=\"h-4 w-4 text-purple-600\" />\r\n                            <span className=\"text-sm font-medium text-gray-700\">\r\n                              Materials\r\n                            </span>\r\n                          </div>\r\n                          <div className=\"flex flex-wrap gap-1\">\r\n                            {item.materials.map((material, idx) => (\r\n                              <Badge\r\n                                key={idx}\r\n                                variant=\"outline\"\r\n                                className=\"text-xs\"\r\n                              >\r\n                                {material}\r\n                              </Badge>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"mt-4\">\r\n                        <div className=\"mb-2 flex items-center gap-2\">\r\n                          <Palette className=\"h-4 w-4 text-pink-600\" />\r\n                          <span className=\"text-sm font-medium text-gray-700\">\r\n                            Colors\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"flex flex-wrap gap-1\">\r\n                          {item.colors.map((color, idx) => (\r\n                            <Badge\r\n                              key={idx}\r\n                              variant=\"outline\"\r\n                              className=\"text-xs\"\r\n                            >\r\n                              {color}\r\n                            </Badge>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n\r\n            {/* Quick Actions Grid */}\r\n            <div className=\"grid gap-4 md:grid-cols-4\">\r\n              <Card\r\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\r\n                onClick={() => handleTabChange(\"categories\")}\r\n              >\r\n                <CardContent className=\"p-6 text-center\">\r\n                  <LayoutGrid className=\"mx-auto mb-3 h-8 w-8 text-blue-600\" />\r\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\r\n                    Manage Categories\r\n                  </h3>\r\n                  <p className=\"mb-3 text-sm text-gray-600\">\r\n                    Organize your product catalog\r\n                  </p>\r\n                  <div className=\"flex items-center justify-center text-sm text-blue-600\">\r\n                    <span>Go to Categories</span>\r\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              <Card\r\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\r\n                onClick={() => handleTabChange(\"brands\")}\r\n              >\r\n                <CardContent className=\"p-6 text-center\">\r\n                  <Tag className=\"mx-auto mb-3 h-8 w-8 text-green-600\" />\r\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\r\n                    Manage Brands\r\n                  </h3>\r\n                  <p className=\"mb-3 text-sm text-gray-600\">\r\n                    Add and organize product brands\r\n                  </p>\r\n                  <div className=\"flex items-center justify-center text-sm text-green-600\">\r\n                    <span>Go to Brands</span>\r\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              <Card\r\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\r\n                onClick={() => handleTabChange(\"materials\")}\r\n              >\r\n                <CardContent className=\"p-6 text-center\">\r\n                  <Hammer className=\"mx-auto mb-3 h-8 w-8 text-purple-600\" />\r\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\r\n                    Manage Materials\r\n                  </h3>\r\n                  <p className=\"mb-3 text-sm text-gray-600\">\r\n                    Define product materials\r\n                  </p>\r\n                  <div className=\"flex items-center justify-center text-sm text-purple-600\">\r\n                    <span>Go to Materials</span>\r\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              <Card\r\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\r\n                onClick={() => handleTabChange(\"colors\")}\r\n              >\r\n                <CardContent className=\"p-6 text-center\">\r\n                  <Palette className=\"mx-auto mb-3 h-8 w-8 text-pink-600\" />\r\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\r\n                    Manage Colors\r\n                  </h3>\r\n                  <p className=\"mb-3 text-sm text-gray-600\">\r\n                    Define product color options\r\n                  </p>\r\n                  <div className=\"flex items-center justify-center text-sm text-pink-600\">\r\n                    <span>Go to Colors</span>\r\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </TabsContent>\r\n\r\n          {/* Categories Tab */}\r\n          <TabsContent value=\"categories\" className=\"space-y-6\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <LayoutGrid className=\"h-5 w-5 text-blue-600\" />\r\n                  Category Management\r\n                </CardTitle>\r\n                <p className=\"text-sm text-gray-600\">\r\n                  Organize your products with categories and subcategories for\r\n                  better navigation\r\n                </p>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <CategoryManagerEnhanced />\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          {/* Brands Tab */}\r\n          <TabsContent value=\"brands\" className=\"space-y-6\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <Tag className=\"h-5 w-5 text-green-600\" />\r\n                  Brand Management\r\n                </CardTitle>\r\n                <p className=\"text-sm text-gray-600\">\r\n                  Create, organize, and manage product brands for your catalog\r\n                </p>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <BrandManagerEnhanced />\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          {/* Materials Tab */}\r\n          <TabsContent value=\"materials\" className=\"space-y-6\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <Hammer className=\"h-5 w-5 text-purple-600\" />\r\n                  Material Management\r\n                </CardTitle>\r\n                <p className=\"text-sm text-gray-600\">\r\n                  Define and manage materials used in your products for better\r\n                  specifications\r\n                </p>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <MaterialManagerEnhanced />\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n\r\n          {/* Colors Tab */}\r\n          <TabsContent value=\"colors\" className=\"space-y-6\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <Palette className=\"h-5 w-5 text-pink-600\" />\r\n                  Color Management\r\n                </CardTitle>\r\n                <p className=\"text-sm text-gray-600\">\r\n                  Create and manage color options for your products to enhance\r\n                  customer choice and visual appeal\r\n                </p>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <ColorManagerEnhanced />\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;;;;AAiCe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,iQAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,kDAAkD;IAClD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,IACE,OACA;YAAC;YAAY;YAAc;YAAU;YAAa;SAAS,CAAC,QAAQ,CAAC,MACrE;YACA,aAAa;QACf;IACF,GAAG;QAAC;KAAa;IAEjB,gDAAgD;IAChD,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,oCAAoC;QACpC,MAAM,kBAAkB,IAAI,gBAAgB,aAAa,QAAQ;QACjE,gBAAgB,GAAG,CAAC,OAAO;QAC3B,OAAO,IAAI,CACT,CAAC,iCAAiC,EAAE,gBAAgB,QAAQ,IAAI;IAEpE;IAEA,yCAAyC;IACzC,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,MAAM,sSAAA,CAAA,aAAU;QAClB;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,MAAM,oRAAA,CAAA,MAAG;QACX;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,MAAM,0RAAA,CAAA,SAAM;QACd;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,MAAM,4RAAA,CAAA,UAAO;QACf;KACD;IAED,MAAM,eAAe;QACnB;YAAE,OAAO;YAAe,MAAM,0RAAA,CAAA,SAAM;YAAE,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAAU;QAC1E;YACE,OAAO;YACP,MAAM,8RAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,OAAO;YACP,MAAM,sSAAA,CAAA,YAAS;YACf,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;KACD;IAED,6CAA6C;IAC7C,MAAM,uBAAuB;QAC3B;YACE,UAAU;YACV,QAAQ;gBAAC;gBAAS;gBAAW;aAAO;YACpC,WAAW;gBAAC;gBAAY;gBAAS;aAAU;YAC3C,QAAQ;gBAAC;gBAAS;gBAAS;aAAS;YACpC,UAAU;QACZ;QACA;YACE,UAAU;YACV,QAAQ;gBAAC;gBAAQ;gBAAU;aAAM;YACjC,WAAW;gBAAC;gBAAU;gBAAa;aAAO;YAC1C,QAAQ;gBAAC;gBAAO;gBAAQ;aAAQ;YAChC,UAAU;QACZ;QACA;YACE,UAAU;YACV,QAAQ;gBAAC;gBAAQ;gBAAc;aAAU;YACzC,WAAW;gBAAC;gBAAQ;gBAAS;aAAS;YACtC,QAAQ;gBAAC;gBAAS;gBAAS;aAAO;YAClC,UAAU;QACZ;KACD;IAED,qBACE;;0BACE,6WAAC,0IAAA,CAAA,oBAAiB;gBAChB,OAAM;gBACN,aAAY;0BAEZ,cAAA,6WAAC;oBAAI,WAAU;;wBACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,6WAAC,2HAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,SAAS,OAAO,MAAM;gCACtB,WAAU;;kDAEV,6WAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;oCACtB,OAAO,KAAK;;+BAPR;;;;;sCAUT,6WAAC,2HAAA,CAAA,SAAM;4BAAC,MAAK;;8CACX,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAMvC,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,yHAAA,CAAA,OAAI;oBACH,OAAO;oBACP,eAAe;oBACf,WAAU;;sCAEV,6WAAC,yHAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAa;;;;;;8CAChC,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;8CAC/B,6WAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;;;;;;;sCAI9B,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;;8CAEtC,6WAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6WAAC,yHAAA,CAAA,OAAI;;8DACH,6WAAC,yHAAA,CAAA,aAAU;oDAAC,WAAU;;sEACpB,6WAAC,yHAAA,CAAA,YAAS;4DAAC,WAAU;sEAClB,KAAK,KAAK;;;;;;sEAEb,6WAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;;;;;;;8DAEvB,6WAAC,yHAAA,CAAA,cAAW;;sEACV,6WAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK;;;;;;sEAEb,6WAAC;4DAAI,WAAU;;gEACZ,KAAK,KAAK,KAAK,sBACd,6WAAC,sSAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EAExB,6WAAC;8EAAM,KAAK,MAAM;;;;;;gEACjB,KAAK,KAAK,KAAK,sBACd,6WAAC;oEAAK,WAAU;8EAAO;;;;;;;;;;;;;;;;;;;2CAjBpB;;;;;;;;;;8CA0Bf,6WAAC,yHAAA,CAAA,OAAI;;sDACH,6WAAC,yHAAA,CAAA,aAAU;;8DACT,6WAAC,yHAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAA0B;;;;;;;8DAG9C,6WAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,6WAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,6WAAC;gDAAI,WAAU;0DACZ,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,6WAAC;wDAEC,WAAU;;0EAEV,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC;oEAAI,WAAU;;sFACb,6WAAC,sSAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;sFACtB,6WAAC;4EAAK,WAAU;sFACb,KAAK,QAAQ;;;;;;sFAEhB,6WAAC,0HAAA,CAAA,QAAK;4EAAC,SAAQ;;gFACZ,KAAK,QAAQ;gFAAC;;;;;;;;;;;;;;;;;;0EAKrB,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;;0FACC,6WAAC;gFAAI,WAAU;;kGACb,6WAAC,oRAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;kGACf,6WAAC;wFAAK,WAAU;kGAAoC;;;;;;;;;;;;0FAItD,6WAAC;gFAAI,WAAU;0FACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,oBACvB,6WAAC,0HAAA,CAAA,QAAK;wFAEJ,SAAQ;wFACR,WAAU;kGAET;uFAJI;;;;;;;;;;;;;;;;kFAUb,6WAAC;;0FACC,6WAAC;gFAAI,WAAU;;kGACb,6WAAC,0RAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;kGAClB,6WAAC;wFAAK,WAAU;kGAAoC;;;;;;;;;;;;0FAItD,6WAAC;gFAAI,WAAU;0FACZ,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,oBAC7B,6WAAC,0HAAA,CAAA,QAAK;wFAEJ,SAAQ;wFACR,WAAU;kGAET;uFAJI;;;;;;;;;;;;;;;;;;;;;;0EAWf,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAU;;0FACb,6WAAC,4RAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;0FACnB,6WAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;kFAItD,6WAAC;wEAAI,WAAU;kFACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,oBACvB,6WAAC,0HAAA,CAAA,QAAK;gFAEJ,SAAQ;gFACR,WAAU;0FAET;+EAJI;;;;;;;;;;;;;;;;;uDAnER;;;;;;;;;;;;;;;;;;;;;8CAmFf,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,yHAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,6WAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6WAAC,sSAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6WAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,6WAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAK;;;;;;0EACN,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAK5B,6WAAC,yHAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,6WAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6WAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,6WAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAK;;;;;;0EACN,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAK5B,6WAAC,yHAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,6WAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6WAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,6WAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAK;;;;;;0EACN,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAK5B,6WAAC,yHAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,6WAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6WAAC,4RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6WAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,6WAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;0EAAK;;;;;;0EACN,6WAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhC,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAa,WAAU;sCACxC,cAAA,6WAAC,yHAAA,CAAA,OAAI;;kDACH,6WAAC,yHAAA,CAAA,aAAU;;0DACT,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,sSAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAA0B;;;;;;;0DAGlD,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,6WAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,6WAAC,6JAAA,CAAA,0BAAuB;;;;;;;;;;;;;;;;;;;;;sCAM9B,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,6WAAC,yHAAA,CAAA,OAAI;;kDACH,6WAAC,yHAAA,CAAA,aAAU;;0DACT,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAA2B;;;;;;;0DAG5C,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAIvC,6WAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,6WAAC,sJAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;;;;;sCAM3B,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACvC,cAAA,6WAAC,yHAAA,CAAA,OAAI;;kDACH,6WAAC,yHAAA,CAAA,aAAU;;0DACT,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAA4B;;;;;;;0DAGhD,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,6WAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,6WAAC,6JAAA,CAAA,0BAAuB;;;;;;;;;;;;;;;;;;;;;sCAM9B,6WAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,6WAAC,yHAAA,CAAA,OAAI;;kDACH,6WAAC,yHAAA,CAAA,aAAU;;0DACT,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,4RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA0B;;;;;;;0DAG/C,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,6WAAC,yHAAA,CAAA,cAAW;kDACV,cAAA,6WAAC,0JAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC", "debugId": null}}]}