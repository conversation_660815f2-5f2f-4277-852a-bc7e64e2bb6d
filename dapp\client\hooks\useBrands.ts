"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

import { BrandApi, Brand, BrandFilters, CreateBrandDto, UpdateBrandDto } from "@/lib/api/brands";

/**
 * Custom hook for managing brands data and operations
 */
export const useBrands = (initialFilters?: BrandFilters) => {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<BrandFilters>(initialFilters || {});

  /**
   * Fetch brands from the API
   */
  const fetchBrands = useCallback(async (currentFilters?: BrandFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const filtersToUse = currentFilters || filters;
      const response = await BrandApi.getBrands(filtersToUse);
      
      if (response.success) {
        setBrands(response.data);
      } else {
        throw new Error("Failed to fetch brands");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch brands";
      setError(errorMessage);
      console.error("Error fetching brands:", err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  /**
   * Create a new brand
   */
  const createBrand = useCallback(async (brandData: CreateBrandDto): Promise<Brand | null> => {
    try {
      const response = await BrandApi.createBrand(brandData);
      
      if (response.success) {
        setBrands(prev => [...prev, response.data]);
        toast.success("Brand created successfully");
        return response.data;
      } else {
        throw new Error("Failed to create brand");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to create brand";
      toast.error(errorMessage);
      console.error("Error creating brand:", err);
      return null;
    }
  }, []);

  /**
   * Update an existing brand
   */
  const updateBrand = useCallback(async (id: string, updateData: UpdateBrandDto): Promise<Brand | null> => {
    try {
      const response = await BrandApi.updateBrand(id, updateData);
      
      if (response.success) {
        setBrands(prev => 
          prev.map(brand => 
            brand._id === id ? response.data : brand
          )
        );
        toast.success("Brand updated successfully");
        return response.data;
      } else {
        throw new Error("Failed to update brand");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update brand";
      toast.error(errorMessage);
      console.error("Error updating brand:", err);
      return null;
    }
  }, []);

  /**
   * Delete a brand
   */
  const deleteBrand = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await BrandApi.deleteBrand(id);
      
      if (response.success) {
        setBrands(prev => prev.filter(brand => brand._id !== id));
        toast.success("Brand deleted successfully");
        return true;
      } else {
        throw new Error("Failed to delete brand");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete brand";
      toast.error(errorMessage);
      console.error("Error deleting brand:", err);
      return false;
    }
  }, []);

  /**
   * Get a brand by ID
   */
  const getBrandById = useCallback(async (id: string): Promise<Brand | null> => {
    try {
      const response = await BrandApi.getBrandById(id);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error("Failed to fetch brand");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch brand";
      console.error("Error fetching brand by ID:", err);
      return null;
    }
  }, []);

  /**
   * Get a brand by slug
   */
  const getBrandBySlug = useCallback(async (slug: string): Promise<Brand | null> => {
    try {
      const response = await BrandApi.getBrandBySlug(slug);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error("Failed to fetch brand");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch brand";
      console.error("Error fetching brand by slug:", err);
      return null;
    }
  }, []);

  /**
   * Recalculate product counts for all brands
   */
  const recalculateProductCounts = useCallback(async (): Promise<boolean> => {
    try {
      const response = await BrandApi.recalculateProductCounts();
      
      if (response.success) {
        // Refresh brands to get updated counts
        await fetchBrands();
        toast.success("Brand product counts recalculated successfully");
        return true;
      } else {
        throw new Error("Failed to recalculate product counts");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to recalculate product counts";
      toast.error(errorMessage);
      console.error("Error recalculating product counts:", err);
      return false;
    }
  }, [fetchBrands]);

  /**
   * Update filters and refetch data
   */
  const updateFilters = useCallback((newFilters: BrandFilters) => {
    setFilters(newFilters);
    fetchBrands(newFilters);
  }, [fetchBrands]);

  /**
   * Refresh brands data
   */
  const refreshBrands = useCallback(() => {
    fetchBrands();
  }, [fetchBrands]);

  // Initial data fetch
  useEffect(() => {
    fetchBrands();
  }, [fetchBrands]);

  return {
    brands,
    loading,
    error,
    filters,
    createBrand,
    updateBrand,
    deleteBrand,
    getBrandById,
    getBrandBySlug,
    recalculateProductCounts,
    updateFilters,
    refreshBrands,
  };
};
